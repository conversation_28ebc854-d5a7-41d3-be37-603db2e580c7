package com.imile.attendance.permission.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.permission.RpcUserPermissionClient;
import com.imile.attendance.permission.dto.PermissionDTO;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.permission.api.PermissionManagerApi;
import com.imile.permission.api.RoleManagerApi;
import com.imile.permission.api.SysSubAdminApi;
import com.imile.permission.api.UserManagerApi;
import com.imile.permission.api.dto.PermissionApiDTO;
import com.imile.permission.api.dto.RoleApiDTO;
import com.imile.rpc.common.RpcResult;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class RpcUserPermissionClientImpl implements RpcUserPermissionClient {

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private UserManagerApi userManagerApi;

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private PermissionManagerApi permissionManagerApi;

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private RoleManagerApi roleManagerApi;

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private SysSubAdminApi sysSubAdminApi;


    @Override
    public PermissionDTO getUserPermission(String userCode) {
        RpcResult<PermissionApiDTO> allPermission = userManagerApi.getAllPermission(userCode);
        PermissionApiDTO permissionApiDTO = RpcResultProcessor.process(allPermission);
        return JSON.parseObject(JSON.toJSONString(permissionApiDTO), PermissionDTO.class);
    }

    @Override
    public List<String> getAdminUserCodesBySystem(String systemCode) {
        RpcResult<List<String>> adminUserCodesBySystem = sysSubAdminApi.getAdminUserCodesBySystem(systemCode);
        List<String> userCodes = RpcResultProcessor.process(adminUserCodesBySystem);
        return userCodes;
    }

    @Override
    public Boolean addDataPermission(String typeCode, String parentId, String newId) {
        RpcResult<Boolean> booleanRpcResult = permissionManagerApi.addDataPermission(typeCode, parentId, newId);
        Boolean bool = RpcResultProcessor.process(booleanRpcResult);
        return bool;
    }

    @Override
    public Boolean changeDataPermission(String typeCode, String parentId, String currentId, List<String> subIdList) {
        RpcResult<Boolean> booleanRpcResult = permissionManagerApi.changeDataPermission(typeCode, parentId, currentId, subIdList);
        Boolean bool = RpcResultProcessor.process(booleanRpcResult);
        return bool;
    }

    @Override
    public List<RoleApiDTO> selectRoleByMenuId(Long menuId) {
        RpcResult<List<RoleApiDTO>> listRpcResult = roleManagerApi.selectRoleByMenuId(menuId);
        List<RoleApiDTO> roleApiDTOList = RpcResultProcessor.process(listRpcResult);
        return roleApiDTOList;
    }



    @Override
    public Map<String, List<String>> selectUserCodeByRoleId(List<Long> roleIdList) {
        RpcResult<Map<String, List<String>>> listRpcResult = userManagerApi.selectUserCodeByRoleId(roleIdList);
        Map<String, List<String>> result = RpcResultProcessor.process(listRpcResult);
        return result;
    }

    @Override
    public Boolean addRoleForUser(String userCode, List<Long> list) {
        RpcResult<Boolean> booleanRpcResult = userManagerApi.addRoleForUser(userCode, list);
        Boolean process = RpcResultProcessor.process(booleanRpcResult);
        return process;
    }
}
