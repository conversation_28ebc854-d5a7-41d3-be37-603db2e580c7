package com.imile.attendance.hrms;

import com.imile.hermes.enterprise.dto.EntOcApiDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
public interface RpcHermesEntOcClient {

    /**
     * 根据网点编码查询网点信息
     */
    EntOcApiDTO getOcByCode(Long orgId, String ocCode);


    /**
     * 根据网点编码查询网点信息
     */
    List<EntOcApiDTO> getOcByCodes(Long orgId, List<String> ocCodes);
}
