package com.imile.attendance.hrms.support;

import com.imile.attendance.constants.Constants;
import com.imile.attendance.hrms.RpcHermesEntOcClient;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class RpcHermesEntOcClientSupport {

    private final RpcHermesEntOcClient rpcHermesEntOcClient;
    private final RedissonClient redissonClient;

    /**
     * 根据网点编码查询网点信息
     */
    public EntOcApiDTO getOcByCode(String ocCode) {
        RMapCache<String, EntOcApiDTO> cache = redissonClient.getMapCache(Constants.CacheKey.OC_CACHE_KEY);

        // 尝试从缓存获取
        EntOcApiDTO result = cache.get(ocCode);
        if (result != null) {
            return result;
        }
        // 缓存未命中，调用RPC
        result = rpcHermesEntOcClient.getOcByCode(Constants.ORG_ID, ocCode);
        if (result != null) {
            // 设置缓存，带过期时间
            cache.fastPut(ocCode, result, 5, TimeUnit.MINUTES);
        }
        return result;
    }

    /**
     * 根据网点编码查询网点信息
     */
    public List<EntOcApiDTO> getOcByCodes(List<String> ocCodes) {
        if (CollectionUtils.isEmpty(ocCodes)) {
            return Collections.emptyList();
        }
        RMapCache<String, EntOcApiDTO> cache = redissonClient.getMapCache(Constants.CacheKey.OC_CACHE_KEY);
        List<EntOcApiDTO> result = new ArrayList<>();
        List<String> missedCodes = new ArrayList<>();

        // 先从缓存获取
        for (String code : ocCodes) {
            EntOcApiDTO cached = cache.get(code);
            if (cached != null) {
                result.add(cached);
            } else {
                missedCodes.add(code);
            }
        }

        // 对未命中的编码调用RPC
        if (!missedCodes.isEmpty()) {
            List<EntOcApiDTO> rpcResult = rpcHermesEntOcClient.getOcByCodes(Constants.ORG_ID, missedCodes);
            if (!CollectionUtils.isEmpty(rpcResult)) {
                // 批量写入缓存
                Map<String, EntOcApiDTO> cacheMap = new HashMap<>();
                for (EntOcApiDTO dto : rpcResult) {
                    cacheMap.put(dto.getOcCode(), dto);
                    result.add(dto);
                }
                // 批量设置缓存
                cache.putAll(cacheMap, 5, TimeUnit.MINUTES);
            }
        }

        return result;
    }


    public Map<String, EntOcApiDTO> getOcMapByCodes(List<String> ocCodes) {
        List<EntOcApiDTO> ocByCodes = getOcByCodes(ocCodes);
        return ocByCodes.stream()
                .collect(Collectors.toMap(EntOcApiDTO::getOcCode, oc -> oc, (k1, k2) -> k1));
    }


}
