package com.imile.attendance.rule;

import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.bo.PunchConfigBO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8
 * @Description
 */
public interface PunchConfigManage {

    void configRangeUpdateOrAdd(List<PunchConfigRangeDO> updateList, List<PunchConfigRangeDO> addList);

    void configUpdateAndAdd(PunchConfigDO updateConfig,
                            PunchConfigDO addConfig);

    void configUpdateAndAdd(PunchConfigDO updateConfig,
                            PunchConfigDO addConfig,
                            List<PunchConfigRangeDO> updatedConfigRanges,
                            List<PunchConfigRangeDO> addConfigRanges);

    /**
     * 获取打卡规则配置BO
     */
    PunchConfigBO getPunchConfigBO(String configNo);

    /**
     * 获取国家的打卡规则
     */
    CountryPunchConfig getCountryConfig(String country);

    /**
     * 批量获取国家的打卡规则列表
     */
    List<CountryPunchConfig> getCountryConfigList(List<String> countries);

    /**
     * 根据用户ID列表获取打卡规则(包含国家级别处理)
     */
    Map<Long, PunchConfigDO> getConfigMapByUserIdList(List<Long> userIds);

    /**
     * 根据用户ID列表和指定时间获取打卡规则
     * 不过滤是否最新和状态
     * key: userId
     */
    Map<Long, PunchConfigDO> mapByUserIds(List<Long> userIds, Date endDate);


    /**
     * 查询用户所有打卡规则
     */
    List<RuleConfigModifyDTO> selectAllByBizId(Long bizId);
}
