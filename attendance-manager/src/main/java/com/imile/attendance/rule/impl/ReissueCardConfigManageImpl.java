package com.imile.attendance.rule.impl;

import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.bo.CountryReissueCardConfig;
import com.imile.attendance.rule.bo.ReissueCardConfigBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
@Component
public class ReissueCardConfigManageImpl implements ReissueCardConfigManage {

    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private AttendanceUserService userService;

    @Override
    @Transactional
    public void configRangeUpdateOrAdd(List<ReissueCardConfigRangeDO> updateList,
                                       List<ReissueCardConfigRangeDO> addList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            reissueCardConfigRangeDao.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            reissueCardConfigRangeDao.saveBatch(addList);
        }
    }

    @Override
    @Transactional
    public void configUpdateAndAdd(ReissueCardConfigDO updateConfig, ReissueCardConfigDO addConfig) {
        if (null != updateConfig) {
            reissueCardConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            reissueCardConfigDao.save(addConfig);
        }
    }

    @Override
    @Transactional
    public void configUpdateAndAdd(ReissueCardConfigDO updateConfig, ReissueCardConfigDO addConfig,
                                   List<ReissueCardConfigRangeDO> updatedConfigRanges, List<ReissueCardConfigRangeDO> addConfigRanges) {
        if (null != updateConfig) {
            reissueCardConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            reissueCardConfigDao.save(addConfig);
        }
        if (CollectionUtils.isNotEmpty(updatedConfigRanges)) {
            reissueCardConfigRangeDao.updateBatchById(updatedConfigRanges);
        }
        if (CollectionUtils.isNotEmpty(addConfigRanges)) {
            reissueCardConfigRangeDao.saveBatch(addConfigRanges);
        }
    }

    @Override
    public ReissueCardConfigBO getConfigBO(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigDao.getLatestByConfigNo(configNo);
        if (null == reissueCardConfigDO) {
            return null;
        }
        return ReissueCardConfigBO.of(reissueCardConfigDO,
                reissueCardConfigRangeDao.listByConfigId(reissueCardConfigDO.getId()));
    }

    @Override
    public CountryReissueCardConfig getCountryConfig(String country) {
        if (StringUtils.isEmpty(country)) {
            return CountryReissueCardConfig.empty();
        }
        List<ReissueCardConfigDO> reissueCardConfigDOList = reissueCardConfigDao.getByCountry(country);
        return CountryReissueCardConfig.of(country, reissueCardConfigDOList);
    }

    @Override
    public List<CountryReissueCardConfig> getCountryListConfig(List<String> countries) {
        List<CountryReissueCardConfig> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(countries)) {
            return list;
        }
        List<ReissueCardConfigDO> countryConfigList = reissueCardConfigDao.listByCountries(countries);
        if (CollectionUtils.isEmpty(countryConfigList)) {
            return list;
        }
        Map<String, List<ReissueCardConfigDO>> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.groupingBy(ReissueCardConfigDO::getCountry));
        countryConfigMap.forEach((country, configList) -> {
            list.add(CountryReissueCardConfig.of(country, configList));
        });
        return list;
    }

    @Override
    public Map<Long, ReissueCardConfigDO> getConfigMapByUserIdList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        // 查询用户
        List<AttendanceUser> users = userService.listUsersByIds(userIds);
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyMap();
        }
        Map<Long, ReissueCardConfigDO> userConfigMap = new HashMap<>();
        // 查询用户是否在范围表里
        List<ReissueCardConfigRangeDO> rangeList = reissueCardConfigRangeDao.listConfigRanges(userIds);
        if (CollectionUtils.isEmpty(rangeList)) {
            return userConfigMap;
        }

        // 查询<userId, ReissueCardConfigRangeDO>
        Map<Long, ReissueCardConfigRangeDO> rangeMap = rangeList.stream()
                .collect(Collectors.toMap(ReissueCardConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));

        // 查询<ruleConfigId, ReissueCardConfigDO>
        Map<Long, ReissueCardConfigDO> configMap = reissueCardConfigDao.listLatestByConfigIds(
                        rangeList.stream()
                                .map(ReissueCardConfigRangeDO::getRuleConfigId)
                                .distinct()
                                .collect(Collectors.toList())
                )
                .stream()
                .collect(Collectors.toMap(ReissueCardConfigDO::getId, Function.identity(), (a, b) -> a));

        // 查询在配置范围内的用户
        List<Long> inRangeUserIdList = new ArrayList<>(rangeMap.keySet());
        inRangeUserIdList.forEach(inRangeUserId -> {
            userConfigMap.put(inRangeUserId, configMap.get(rangeMap.get(inRangeUserId).getRuleConfigId()));
        });
        return userConfigMap;

    }

    @Override
    public Map<Long, ReissueCardConfigDO> mapByUserIds(List<Long> userIds, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        long endDateTimeStamp = endDate.getTime();
        List<ReissueCardConfigRangeDO> configRangeDOList = reissueCardConfigRangeDao.listAllRangeByUserIds(userIds)
                .stream()
                .filter(item -> item.getEffectTimestamp().compareTo(endDateTimeStamp) < 1 &&
                        item.getExpireTimestamp().compareTo(endDateTimeStamp) > -1)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(configRangeDOList)) {
            return Collections.emptyMap();
        }

        Map<Long, ReissueCardConfigRangeDO> rangeMap = configRangeDOList.stream()
                .collect(Collectors.toMap(ReissueCardConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));


        Map<Long, ReissueCardConfigDO> configMap = reissueCardConfigDao.listByConfigIds(configRangeDOList.stream()
                        .map(ReissueCardConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(ReissueCardConfigDO::getId, Function.identity(), (a, b) -> a));

        Map<Long, ReissueCardConfigDO> userConfigMap = new HashMap<>();
        for (Long userId : rangeMap.keySet()) {
            ReissueCardConfigRangeDO configRangeDO = rangeMap.get(userId);
            if (Objects.isNull(configMap.get(configRangeDO.getRuleConfigId()))) {
                continue;
            }
            userConfigMap.put(userId, configMap.get(configRangeDO.getRuleConfigId()));
        }
        return userConfigMap;
    }

    @Override
    public List<RuleConfigModifyDTO> selectAllByBizId(Long bizId) {
        List<ReissueCardConfigRangeDO> reissueCardConfigRangeDOList = reissueCardConfigRangeDao.listAllConfigRanges(bizId);
        if (CollectionUtils.isEmpty(reissueCardConfigRangeDOList)) {
            return Collections.emptyList();
        }
        List<Long> ruleConfigIdList = reissueCardConfigRangeDOList.stream().map(ReissueCardConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
        Map<Long, ReissueCardConfigDO> reissueCardConfigMap = reissueCardConfigDao.listByConfigIds(ruleConfigIdList)
                .stream().collect(Collectors.toMap(ReissueCardConfigDO::getId, Function.identity()));
        return reissueCardConfigRangeDOList.stream().map(range->{
            RuleConfigModifyDTO modifyDTO = new RuleConfigModifyDTO();
            modifyDTO.setCalendarName(reissueCardConfigMap.getOrDefault(range.getRuleConfigId(),new ReissueCardConfigDO()).getConfigName());
            modifyDTO.setStartDate(range.getEffectTime());
            modifyDTO.setEndDate(range.getExpireTime());
            modifyDTO.setCreateUserName(range.getCreateUserName());
            return modifyDTO;
        }).collect(Collectors.toList());
    }
}
