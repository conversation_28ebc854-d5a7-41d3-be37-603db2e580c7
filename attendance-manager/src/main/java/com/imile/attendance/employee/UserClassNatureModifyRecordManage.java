package com.imile.attendance.employee;

import com.imile.attendance.infrastructure.repository.employee.dao.UserClassNatureModifyRecordDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/10
 */
@Component
public class UserClassNatureModifyRecordManage {

    @Resource
    private UserClassNatureModifyRecordDao userClassNatureModifyRecordDao;

    @Transactional(rollbackFor = Exception.class)
    public void userClassNatureUpdate(UserClassNatureModifyRecordDO update,UserClassNatureModifyRecordDO add) {
        if (Objects.nonNull(update)){
            userClassNatureModifyRecordDao.updateById(update);
        }
        if (Objects.nonNull(update)){
            userClassNatureModifyRecordDao.save(add);
        }
    }
}
