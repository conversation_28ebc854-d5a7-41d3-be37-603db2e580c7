package com.imile.attendance.employee;

import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class UserDimissionRecordManage {
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;

    public Map<Long, UserDimissionRecordDO> mapByUserIds(List<Long> userIdList) {
        return userDimissionRecordDao.listByUserIds(userIdList)
                .stream().filter(entry -> Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), entry.getDimissionStatus()))
                .collect(Collectors.toMap(UserDimissionRecordDO::getUserId, Function.identity(), (v1, v2) -> v1));
    }
}
