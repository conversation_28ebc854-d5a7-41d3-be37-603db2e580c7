package com.imile.attendance.employee;

import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */

@Component
public class UserEntryRecordManage {
    @Resource
    private UserEntryRecordDao userEntryRecordDao;

    public Map<Long, UserEntryRecordDO> mapByUserIds(List<Long> userIdList) {
        return userEntryRecordDao.listByUserIds(userIdList)
                .stream().filter(entry -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), entry.getEntryStatus()))
                .collect(Collectors.toMap(UserEntryRecordDO::getUserId, Function.identity(), (v1, v2) -> v1));
    }


}
