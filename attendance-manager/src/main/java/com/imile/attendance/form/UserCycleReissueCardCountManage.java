package com.imile.attendance.form;

import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
public interface UserCycleReissueCardCountManage {

    /**
     * 查询用户的所有考勤周期的补卡次数配置
     */
    List<UserCycleReissueCardCountDO> selectByUserIdList(List<Long> userIdList);

    /**
     * 更新补卡次数规则
     */
    void batchUpdateById(List<UserCycleReissueCardCountDO> userCardConfigDOList);

    /**
     * 新增补卡次数规则
     */
    void batchSave(List<UserCycleReissueCardCountDO> userCardConfigDOList);

    /**
     * 初始化数据处理
     */
    void initCardUpdate(List<UserCycleReissueCardCountDO> addUserCardConfigDOList,
                        List<UserCycleReissueCardCountDO> updateUserCardConfigDOList);
}
