package com.imile.attendance.rescources;

import cn.hutool.core.collection.CollectionUtil;
import com.imile.attendance.enums.PermissionTypeEnum;
import com.imile.attendance.enums.ResourceTypeEnum;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 权限处理handler
 */
public abstract class ResourceHandlerAbstract {

    /**
     * 处理权限
     * 默认为处理部门及供应商权限
     *
     * @param userContext 用户上下文
     */
    public void handlerResource(UserContext userContext) {
        if (userContext == null) {
            return;
        }
        List<Long> resourceIds = this.getResourceIds(userContext.getId());
        if (CollectionUtil.isEmpty(resourceIds)) {
            return;
        }
        userContext.setOrganizationIds(resourceIds);
    }

    /**
     * 返回该类
     *
     * @return String
     */
    public abstract String getResourceHandler();

    /**
     * 获取资源类型
     *
     * @return ResourceTypeEnum
     */
    public abstract ResourceTypeEnum getResourceType();


    protected List<Long> getResourceIds(Long userId) {
        ResourceTypeEnum resourceType = this.getResourceType();
        if (resourceType == null) {
            return new ArrayList<>();
        }

        UserContext userContext = RequestInfoHolder.getLoginInfo();

        Map<String, List<String>> permissionMap = userContext.getPermissionMap();
        // 公司权限 子管理员，不予处理
        //if (resourceType.equals(ResourceTypeEnum.COMPANY_ID)) {
        //    List<String> hrmsVendor = permissionMap.get(PermissionTypeEnum.COMPANY_ID);
        //    if (CollectionUtils.isNotEmpty(hrmsVendor)) {
        //        return hrmsVendor.stream().map(Long::parseLong).collect(Collectors.toList());
        //    }
        //}
        // 部门权限
        if (resourceType.equals(ResourceTypeEnum.DEPT_ID)) {
            List<String> deptTreeEnum = permissionMap.get(PermissionTypeEnum.DEPT.getTypeCode());
            if (CollectionUtils.isEmpty(deptTreeEnum)) {
                return Collections.emptyList();
            }
            return deptTreeEnum.stream().map(Long::parseLong).collect(Collectors.toList());
        }
        // 供应商权限
        else if (resourceType.equals(ResourceTypeEnum.VENDOR_ID)) {
            List<String> hrmsVendor = permissionMap.get(PermissionTypeEnum.VENDOR.getTypeCode());
            if (CollectionUtils.isEmpty(hrmsVendor)) {
                return Collections.emptyList();
            }
            return hrmsVendor.stream().map(Long::parseLong).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
