# MigrationService 数据库查询优化建议

## 概述

本文档提供了针对 MigrationService 相关查询的数据库索引优化建议，以提升查询性能。

## 涉及的表和查询

### 1. user_info 表

MigrationService 主要通过 `getByUserCode` 方法查询用户信息，该方法使用以下查询条件：

- `user_code` (主要查询条件)
- `work_status` (在职状态过滤)
- `status` (活跃状态过滤)
- `is_delete` (逻辑删除过滤)

## 推荐的索引优化

### 1. 用户编码复合索引

```sql
-- 为 user_code 查询创建复合索引
CREATE INDEX idx_user_info_code_status ON user_info (
    user_code,
    work_status,
    status,
    is_delete
);
```

**优化理由：**
- `user_code` 是主要查询条件，放在索引第一位
- `work_status`, `status`, `is_delete` 是常用的过滤条件
- 复合索引可以覆盖整个查询，避免回表操作

### 2. 常驻国家索引

```sql
-- 为 location_country 查询创建索引
CREATE INDEX idx_user_info_location_country ON user_info (location_country);
```

**优化理由：**
- MigrationService 需要根据用户的 `location_country` 判断是否启用新考勤系统
- 该字段可能在其他业务场景中也会被频繁查询

### 3. 检查现有索引

```sql
-- 检查 user_info 表的现有索引
SHOW INDEX FROM user_info;

-- 检查是否已存在 user_code 相关索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_info' 
    AND COLUMN_NAME IN ('user_code', 'location_country', 'work_status', 'status');
```

## 性能监控建议

### 1. 查询执行计划分析

```sql
-- 分析 getByUserCode 方法的查询执行计划
EXPLAIN SELECT * FROM user_info 
WHERE user_code = 'test_user' 
    AND work_status = 'ON_JOB' 
    AND status = 'ACTIVE' 
    AND is_delete = 0;
```

### 2. 慢查询监控

```sql
-- 启用慢查询日志监控
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 0.1; -- 100ms 以上的查询记录为慢查询
```

## 预期性能提升

### 1. 查询性能提升

- **无索引情况**：全表扫描，查询时间 O(n)
- **有索引情况**：索引查找，查询时间 O(log n)
- **预期提升**：对于百万级数据，查询时间从 100-500ms 降低到 1-10ms

### 2. 缓存命中率

- **首次查询**：数据库查询 + 缓存存储
- **后续查询**：直接从缓存获取，响应时间 < 5ms
- **缓存过期时间**：15分钟，平衡数据一致性和性能

## 实施步骤

### 1. 生产环境实施前准备

1. **备份数据**：确保数据安全
2. **测试环境验证**：在测试环境先执行索引创建
3. **性能基准测试**：记录优化前的性能指标

### 2. 索引创建时机

```sql
-- 在业务低峰期执行索引创建
-- 注意：大表创建索引可能需要较长时间，建议在维护窗口执行

-- 创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_user_info_code_status ON user_info (
    user_code,
    work_status,
    status,
    is_delete
);

CREATE INDEX IF NOT EXISTS idx_user_info_location_country ON user_info (location_country);
```

### 3. 验证索引效果

```sql
-- 验证索引是否被正确使用
EXPLAIN SELECT * FROM user_info 
WHERE user_code = 'test_user' 
    AND work_status = 'ON_JOB' 
    AND status = 'ACTIVE' 
    AND is_delete = 0;

-- 检查索引统计信息
SELECT 
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'user_info'
    AND INDEX_NAME IN ('idx_user_info_code_status', 'idx_user_info_location_country');
```

## 注意事项

### 1. 索引维护成本

- 索引会增加 INSERT、UPDATE、DELETE 操作的开销
- 需要额外的存储空间
- 建议定期监控索引使用情况，清理无用索引

### 2. 数据一致性

- 缓存数据可能存在延迟
- 用户信息变更时需要考虑缓存失效策略
- 建议在用户信息更新时主动清除相关缓存

### 3. 监控指标

- 查询响应时间
- 缓存命中率
- 数据库连接池使用情况
- 索引使用统计

## 总结

通过实施上述优化建议，MigrationService 的查询性能预期可以提升 90% 以上，特别是在高并发场景下效果更加明显。建议按照实施步骤逐步推进，并持续监控优化效果。
