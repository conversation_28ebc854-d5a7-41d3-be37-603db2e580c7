package com.imile.attendance.shift.param;

import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description 班次规则变动触发自动排班入参
 */
@Data
public class UserAutoShiftParam {

    private ClassNatureEnum classNatureEnum;

    private PunchClassAddUserParam punchClassAddUserParam;

    private PunchClassRemoveUserParam punchClassRemoveUserParam;

    private Map<Long, CalendarConfigDO> userCalendarConfigMap;

    /**
     * 排班任务标记，无需传，自动在任务生成
     */
    private String taskFlag;


    @Data
    public static class PunchClassAddUserParam {
        /**
         * 班次新增人员
         */
        private List<Long> userIdList;
        /**
         * 新增人员需要删除的历史班次,<userId,List<classId>>
         * 多班次人员必传
         */
        private Map<Long, List<Long>> userNeedClearClassMap;
        /**
         * 人员绑定的目标班次id
         */
        private Long targetClassId;
        /**
         * 排班开始时间
         */
        private Long shiftStartDayId;
        /**
         * 是否只清除今日之后（包括今日）的排班，不重新排班
         */
        private Boolean isOnlyClearShift = Boolean.FALSE;
    }

    @Data
    public static class PunchClassRemoveUserParam {

        private List<PunchClassRemoveSingleUserParam> classRemoveSingleUserParams;
        /**
         * 是否只清除今日之后（包括今日）的排班，不重新排班
         */
        private Boolean isOnlyClearShift = Boolean.FALSE;
    }


    /**
     * 班次移除人员入参
     */
    @Data
    public static class PunchClassRemoveSingleUserParam {
        /**
         * 班次移除人员
         */
        private Long userId;
        /**
         * 人员绑定的目标班次id(当只清空时，为空）
         */
        private Long targetClassId;
        /**
         * 多班次人员指定清理的班次ID集合
         * 固定班次不需要传
         */
        private List<Long> clearClassIdList;
        /**
         * 排班开始时间(开始清空排班的时间)
         */
        private Long shiftStartDayId;
    }


    public List<Long> getAllUserId(UserAutoShiftParam userAutoShiftParam) {
        Set<Long> userIdSet = new HashSet<>();
        if (userAutoShiftParam.getPunchClassAddUserParam() != null) {
            userIdSet.addAll(userAutoShiftParam.getPunchClassAddUserParam().getUserIdList());
        }
        PunchClassRemoveUserParam punchClassRemoveUserParam = userAutoShiftParam.getPunchClassRemoveUserParam();
        if (punchClassRemoveUserParam != null) {
            List<PunchClassRemoveSingleUserParam> classRemoveSingleUserParams = punchClassRemoveUserParam.getClassRemoveSingleUserParams();
            if (CollectionUtils.isNotEmpty(classRemoveSingleUserParams)) {
                userIdSet.addAll(
                        classRemoveSingleUserParams.stream()
                                .map(PunchClassRemoveSingleUserParam::getUserId)
                                .collect(Collectors.toList())
                );
            }
        }
        return new ArrayList<>(userIdSet);
    }


}
