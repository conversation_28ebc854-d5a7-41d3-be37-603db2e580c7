package com.imile.attendance.shift.param;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/15 
 * @Description 固定班次自动排班续期参数
 */
@Data
public class FixedClassAutoRenewalParam {

    /**
     * 国家列表，逗号分隔
     */
    private String countryList;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 用户编码列表，逗号分隔
     */
    private String userCodes;

    /**
     * 分页大小
     */
    private Integer pageSize = 500;
}
