package com.imile.attendance.shift.excel;

import com.imile.attendance.infrastructure.excel.header.ExcelHeaderBaseService;
import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/4/25 
 * @Description 排班导出表头枚举
 */
@Getter
public enum ShiftExportHeaderEnum implements ExcelHeaderBaseService {

    EMPLOYEE_ID("Employee ID", "账号"),
    EMPLOYEE_NAME("Employee Name", "姓名"),
    COUNTRY("Country", "国家"),
    DEPARTMENT_NAME("Department Name", "部门"),
    DESIGNATION("Designation", "岗位"),
    EMPLOYEE_TYPE("Employee Type", "用工类型"),
    PUNCH_CLASS_TYPE("Punch Class Type", "班次分类"),
    MATCHED_PUNCH_CLASS("Number of matched Punch Class", "班次适配数"),
    CALENDAR("Calendar", "日历");

    private final String englishTitle;
    private final String chineseTitle;

    ShiftExportHeaderEnum(String englishTitle, String chineseTitle) {
        this.englishTitle = englishTitle;
        this.chineseTitle = chineseTitle;
    }
}
