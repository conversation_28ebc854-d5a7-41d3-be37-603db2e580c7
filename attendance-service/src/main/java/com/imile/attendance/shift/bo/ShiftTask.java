package com.imile.attendance.shift.bo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description 排班任务类
 */
@Data
public class ShiftTask {

    /**
     * 任务标识
     */
    private String taskFlag;

    /**
     * 排班来源
     */
    private ShiftSourceEnum shiftSourceEnum;

    /**
     * 用户ID列表
     */
    private List<Long> userIds;

    /**
     * 任务
     */
    private Consumer<String> consumer;

    /**
     * 是否执行成功
     */
    private Boolean taskIsSuccess;

    /**
     * 任务执行失败原因
     */
    private String errorMsg;

    /**
     * 任务开始时间
     */
    private long startTimeMillis;

    /**
     * 任务结束时间
     */
    private long endTimeMillis;


    public static ShiftTask of(String taskFlag,
                               ShiftSourceEnum shiftSourceEnum,
                               List<Long> userIds,
                               Consumer<String> consumer) {
        ShiftTask shiftTask = new ShiftTask();
        shiftTask.setTaskFlag(taskFlag);
        shiftTask.setShiftSourceEnum(shiftSourceEnum);
        shiftTask.setUserIds(userIds);
        shiftTask.setConsumer(consumer);
        return shiftTask;
    }

    /**
     * 生成排班任务标识
     */
    public static String generateShiftTaskFlag(ShiftSourceEnum shiftSourceEnum) {
        return shiftSourceEnum.getCode() + "_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
    }

    /**
     * 执行任务
     */
    public void run() {
        startTimeMillis = System.currentTimeMillis();
        consumer.accept(taskFlag);
        endTimeMillis = System.currentTimeMillis();
    }

    /**
     * 获取任务执行耗时(秒)
     */
    public int getCostSecond() {
        if (startTimeMillis == 0 || endTimeMillis == 0) {
            return 0;
        }
        return (int) ((endTimeMillis - startTimeMillis) / 1000);
    }

}
