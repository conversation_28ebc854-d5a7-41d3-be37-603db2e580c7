package com.imile.attendance.calendar.dto;

import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.ucenter.api.context.RequestInfoHolder;
import lombok.Data;

import java.util.function.Function;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class CalendarConfigRangeDTO {

    /**
     * 部门ID/员工ID
     */
    private Long bizId;
    /**
     * 部门名称
     */
    private String bizNameByLang;
    /**
     * 组织编码
     */
    @Deprecated
    private String organizationCode;
    /**
     * DEPT:部门/ ACCOUNT 用户
     */
    private String type;
    /**
     * 考勤配置名称/日历名称
     */
    private String attendanceConfigName;
    /**
     * 考勤配置id
     */
    private Long attendanceConfigId;

    public static CalendarConfigRangeDTO buildUserRangeDTO(AttendanceUser attendanceUser, boolean isChinese) {
        return of(
                RangeTypeEnum.USER,
                t -> attendanceUser.getId(),
                t -> isChinese ? attendanceUser.getUserName() : attendanceUser.getUserNameEn(),
                attendanceUser
        );
    }

    public static CalendarConfigRangeDTO buildDeptRangeDTO(AttendanceDept attendanceDept, boolean isChinese) {
        return of(
                RangeTypeEnum.DEPT,
                t -> attendanceDept.getId(),
                t -> isChinese ? attendanceDept.getDeptNameCn() : attendanceDept.getDeptNameEn(),
                attendanceDept);
    }


    public static CalendarConfigRangeDTO buildUserRangeDTO(AttendanceUser attendanceUser) {
        return of(
                RangeTypeEnum.USER,
                t -> attendanceUser.getId(),
                t -> RequestInfoHolder.isChinese() ? attendanceUser.getUserName() : attendanceUser.getUserNameEn(),
                attendanceUser
        );
    }

    public static CalendarConfigRangeDTO buildDeptRangeDTO(AttendanceDept attendanceDept) {
        return of(
                RangeTypeEnum.DEPT,
                t -> attendanceDept.getId(),
                t -> RequestInfoHolder.isChinese() ? attendanceDept.getDeptNameCn() : attendanceDept.getDeptNameEn(),
                attendanceDept);
    }

    public static <T> CalendarConfigRangeDTO of(RangeTypeEnum rangeType,
                                                Function<T, Long> idGetter,
                                                Function<T, String> nameGetter,
                                                T record) {
        CalendarConfigRangeDTO configRangeDTO = new CalendarConfigRangeDTO();
        configRangeDTO.setBizId(idGetter.apply(record));
        configRangeDTO.setBizNameByLang(nameGetter.apply(record));
        configRangeDTO.setType(rangeType.getCode());
        return configRangeDTO;
    }
}
