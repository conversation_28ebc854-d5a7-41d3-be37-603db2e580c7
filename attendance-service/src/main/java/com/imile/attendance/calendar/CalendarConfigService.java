package com.imile.attendance.calendar;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.calendar.dto.CalendarTypeDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
@Slf4j
@Service
public class CalendarConfigService {

    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarManage calendarManage;


    /**
     * 根据国家获取日历类型
     *
     * @param country 国家
     * @return 日历类型
     */
    public CalendarTypeDTO getCalendarTypeByCountry(String country) {
        Integer result = calendarConfigDao.countDefaultCalendarConfig(country);
        return CalendarTypeDTO.create(result);
    }

    /**
     * 用户部门变动需要重新匹配日历和打卡规则
     */
    public void userCalendarHandler(CalendarAndPunchHandlerDTO calendarHandlerDTO) {
        log.info("userCalendarHandler:{}", JSON.toJSON(calendarHandlerDTO));
        if (calendarHandlerDTO.getUserId() == null
                || calendarHandlerDTO.getNewDeptId() == null
                || calendarHandlerDTO.getOldDeptId() == null
                || StringUtils.isEmpty(calendarHandlerDTO.getUserCode())
                || StringUtils.isBlank(calendarHandlerDTO.getNewCountry())
                || StringUtils.isBlank(calendarHandlerDTO.getOldCountry())) {
            log.info("userCalendarHandler | 参数异常，handlerDTO:{}", JSON.toJSON(calendarHandlerDTO));
            return;
        }
        Long userId = calendarHandlerDTO.getUserId();
        String newCountry = calendarHandlerDTO.getNewCountry();
        //查询当前所有用户所用的考勤日历(旧的)
        List<CalendarConfigRangeDO> oldCalendarConfigRangeList = calendarConfigRangeDao.selectConfigRange(Collections.singletonList(userId));

        List<Long> oldCalendarConfigIdList = oldCalendarConfigRangeList.stream()
                .map(CalendarConfigRangeDO::getAttendanceConfigId)
                .collect(Collectors.toList());

        List<CalendarConfigDO> oldConfigDOList = calendarConfigDao.getByCalendarConfigIds(oldCalendarConfigIdList);

        //查询新国家下的所有日历
        List<CalendarConfigDO> allCalendarConfigDOList = calendarConfigDao.selectByCountryList(Collections.singletonList(newCountry));

        Date date = new Date();
        List<CalendarConfigRangeDO> addCalendarConfigRangeDOList = new ArrayList<>();
        List<CalendarConfigRangeDO> updateCalendarConfigRangeDOList = new ArrayList<>();

        if (CollectionUtils.isEmpty(oldCalendarConfigRangeList) || oldCalendarConfigRangeList.size() > 1) {
            log.info("userCalendarHandler | 旧的考勤日历范围或配置异常,存在多条数据，handlerDTO:{}", JSON.toJSON(calendarHandlerDTO));
            return;
        }

        CalendarConfigRangeDO oldUserCalendarConfigRange = oldCalendarConfigRangeList.get(0);

        List<CalendarConfigDO> oldUserCalendarConfigDOList = oldConfigDOList.stream()
                .filter(item -> item.getId().equals(oldUserCalendarConfigRange.getAttendanceConfigId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(oldUserCalendarConfigDOList) || oldUserCalendarConfigDOList.size() > 1) {
            log.info("userCalendarHandler | 旧的考勤日历配置异常,存在多条数据，handlerDTO:{}", JSON.toJSON(calendarHandlerDTO));
            return;
        }
        CalendarConfigDO newCalendarConfigDO;
        //国家变更(新的规则不可能是用户级别)
        if (!StringUtils.equalsIgnoreCase(calendarHandlerDTO.getNewCountry(), calendarHandlerDTO.getOldCountry())) {
            newCalendarConfigDO = matchAttendanceConfig(calendarHandlerDTO.getNewDeptId(), allCalendarConfigDOList);
            buildCalendarConfigRange(date, addCalendarConfigRangeDOList, updateCalendarConfigRangeDOList, calendarHandlerDTO.getUserId(), oldUserCalendarConfigRange, newCalendarConfigDO);
        }
        //国家不变，部门变动
        if (!StringUtils.equalsIgnoreCase(oldUserCalendarConfigRange.getRangeType(), RangeTypeEnum.USER.getCode())) {
            //看部门有没有变动
            newCalendarConfigDO = matchAttendanceConfig(calendarHandlerDTO.getNewDeptId(), allCalendarConfigDOList);
            buildCalendarConfigRange(date, addCalendarConfigRangeDOList, updateCalendarConfigRangeDOList, calendarHandlerDTO.getUserId(), oldUserCalendarConfigRange, newCalendarConfigDO);
        }
        calendarManage.calendarConfigRangeUpdate(updateCalendarConfigRangeDOList, addCalendarConfigRangeDOList);
    }

    private static void buildCalendarConfigRange(Date date,
                                                 List<CalendarConfigRangeDO> addCalendarConfigRangeDOList,
                                                 List<CalendarConfigRangeDO> updateCalendarConfigRangeDOList,
                                                 Long userId,
                                                 CalendarConfigRangeDO oldUserCalendarConfigRange,
                                                 CalendarConfigDO newCalendarConfigDO) {
        oldUserCalendarConfigRange.setEndDate(date);
        oldUserCalendarConfigRange.setIsLatest(BusinessConstant.N);
        BaseDOUtil.fillDOUpdateByUserOrSystem(oldUserCalendarConfigRange);
        updateCalendarConfigRangeDOList.add(oldUserCalendarConfigRange);

        if (Objects.isNull(newCalendarConfigDO)) {
            return;
        }
        CalendarConfigRangeDO addCalendarConfigRangeDO = new CalendarConfigRangeDO();
        addCalendarConfigRangeDO.setAttendanceConfigId(newCalendarConfigDO.getId());
        addCalendarConfigRangeDO.setAttendanceConfigNo(newCalendarConfigDO.getAttendanceConfigNo());
        addCalendarConfigRangeDO.setBizId(userId);
        String attendanceRangeType = RangeTypeEnum.DEFAULT.getCode();
        if (StringUtils.equalsIgnoreCase(newCalendarConfigDO.getType(), AttendanceTypeEnum.CUSTOM.name())) {
            attendanceRangeType = RangeTypeEnum.DEPT.getCode();
        }
        addCalendarConfigRangeDO.setRangeType(attendanceRangeType);
        addCalendarConfigRangeDO.setStartDate(date);
        addCalendarConfigRangeDO.setIsLatest(BusinessConstant.Y);
        addCalendarConfigRangeDO.setEndDate(BusinessConstant.DEFAULT_END_TIME);
        BaseDOUtil.fillDOInsertByUsrOrSystem(addCalendarConfigRangeDO);
        addCalendarConfigRangeDOList.add(addCalendarConfigRangeDO);
    }

    /**
     * 匹配对应的考勤日历
     */
    private CalendarConfigDO matchAttendanceConfig(Long deptId, List<CalendarConfigDO> allCalendarConfigDOList) {
        //先根据用户所在部门查找对应的考勤日历
        List<CalendarConfigDO> defaultConfigList = allCalendarConfigDOList.stream()
                .filter(item -> item.getType().equals(AttendanceTypeEnum.DEFAULT.name()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultConfigList)) {
            return null;
        }
        CalendarConfigDO defaultCalendarConfig = defaultConfigList.get(0);

        List<CalendarConfigDO> customList = allCalendarConfigDOList.stream()
                .filter(item -> item.getType().equals(AttendanceTypeEnum.CUSTOM.name()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customList)) {
            return defaultCalendarConfig;
        }
        for (CalendarConfigDO item : customList) {
            if (StringUtils.isEmpty(item.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = Arrays.stream(item.getDeptIds().split(BusinessConstant.DEFAULT_DELIMITER))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            if (deptIds.contains(deptId)) {
                return item;
            }
        }
        return defaultCalendarConfig;
    }
}
