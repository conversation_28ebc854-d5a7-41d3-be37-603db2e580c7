package com.imile.attendance.calendar.notify;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.calendar.notify.publish.EmployeeSchedulingEventPublisher;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.util.DateHelper;
import com.imile.util.date.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class CalendarEventService {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private EmployeeSchedulingEventPublisher publisher;

    public EmployeeSchedulingParam sendCalendarEvent(String country, Collection<Long> deptIds,
                                                      Collection<Long> userIds, Boolean reSchedule,
                                                      List<Long> updateDayIds, List<Long> updateUserIdList,
                                                      Date date) {
        // 更新日历对范围变动的人员排班(包含新增/删除人员)和未变动人员都需要按照弹窗选择项重新排班(选择否只修改自动排班，不修改手动排班)
        List<AttendanceUser> latestUserByDeptIds = userService.listOnJobUserByDeptIdList(new ArrayList<>(deptIds));
        // 查询当前规则下的人员，区分变动人员和历史范围人员
        List<Long> sendEventUserIdList = Lists.newArrayList();
        sendEventUserIdList.addAll(userIds);
        if (CollectionUtils.isNotEmpty(latestUserByDeptIds)) {
            List<Long> latestUserIdList = latestUserByDeptIds.stream()
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(latestUserIdList)) {
                sendEventUserIdList.addAll(latestUserIdList);
            }
        }
        // 去除重复人员
        sendEventUserIdList = sendEventUserIdList.stream().distinct().collect(Collectors.toList());
        // 去除变动人员(新增/删除)
        sendEventUserIdList.removeIf(updateUserIdList::contains);
        // 变动日历，则针对历史范围人员发送排班事件，只修改对应日历变动的考勤日
        if (CollectionUtils.isNotEmpty(sendEventUserIdList) && CollectionUtils.isNotEmpty(updateDayIds)) {
            EmployeeSchedulingParam eventParamForHistory = EmployeeSchedulingParam.builder()
                    .country(country)
                    .userIds(sendEventUserIdList)
                    .dayIds(updateDayIds)
                    .reSchedule(reSchedule).delAllSchedule(false)
                    .build();
            publisher.sendSchedulingEvent(eventParamForHistory);
            return eventParamForHistory;
        }
        // 变动适用范围, 则针对变动人员发送排班事件，只修改对应时间段(当前日期+180天)
        if (CollectionUtils.isNotEmpty(updateUserIdList)) {
            Date startDate = DateUtils.dayBegin(date);
            Date endDate = DateUtils.dayEnd(DateHelper.pushDate(date, BusinessConstant.MAX_AUTO_SHIFT_DAYS));
            EmployeeSchedulingParam eventParamForUpdate = EmployeeSchedulingParam.builder()
                    .country(country)
                    .userIds(updateUserIdList)
                    .startDayId(Long.parseLong(DateUtil.format(startDate, DatePattern.PURE_DATE_PATTERN)))
                    .endDayId(Long.parseLong(DateUtil.format(endDate, DatePattern.PURE_DATE_PATTERN)))
                    .reSchedule(true)
                    .delAllSchedule(true)
                    .build();
            publisher.sendSchedulingEvent(eventParamForUpdate);
            return eventParamForUpdate;
        }
        return null;
    }

    public void sendCalendarLegalLeaveChange(List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList){
        for (CalendarLegalLeaveConfigDO legalLeaveConfigDO : calendarLegalLeaveConfigList) {
            // 发送人员排班事件
            EmployeeSchedulingParam schedulingParam = EmployeeSchedulingParam.builder()
                    .country(legalLeaveConfigDO.getLocationCountry())
                    .startDayId(legalLeaveConfigDO.getLegalLeaveStartDayId())
                    .endDayId(legalLeaveConfigDO.getLegalLeaveEndDayId())
                    .reSchedule(false).delAllSchedule(false)
                    .build();
            publisher.sendSchedulingEvent(schedulingParam);
        }
    }
}
