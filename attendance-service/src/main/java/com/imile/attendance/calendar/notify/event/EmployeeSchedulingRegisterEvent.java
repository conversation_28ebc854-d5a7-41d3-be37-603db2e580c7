package com.imile.attendance.calendar.notify.event;

import com.imile.attendance.calendar.notify.EmployeeSchedulingParam;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 人员排班事件
 */
@Getter
public class EmployeeSchedulingRegisterEvent extends ApplicationEvent {

    private final EmployeeSchedulingParam data;

    public EmployeeSchedulingRegisterEvent(Object source, EmployeeSchedulingParam data) {
        super(source);
        this.data = data;
    }
}
