package com.imile.attendance.calendar.mapstruct;

import com.imile.attendance.calendar.dto.CalendarConfigDTO;
import com.imile.attendance.calendar.dto.CalendarConfigSelectDTO;
import com.imile.attendance.calendar.query.CalendarLegalLeaveConfigDetailQuery;
import com.imile.attendance.calendar.vo.LegalLeaveConfigVO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Mapper
public interface CalendarConfigServiceMapstruct {

    CalendarConfigServiceMapstruct INSTANCE = Mappers.getMapper(CalendarConfigServiceMapstruct.class);


    CalendarConfigSelectDTO mapToSelect(CalendarConfigDO calendarConfigDO);

    List<CalendarConfigSelectDTO> mapToSelect(List<CalendarConfigDO> calendarConfigDOList);



    @Mapping(target = "rangeRecords", ignore = true)
    @Mapping(target = "employeeCount", ignore = true)
    CalendarConfigDTO toCalendarConfigDTO(CalendarConfigDO calendarConfigDO);


    LegalLeaveConfigVO toLegalLeaveConfigVO(CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO);

    List<LegalLeaveConfigVO> toLegalLeaveConfigVO(List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigDOList);

    @Mapping(target = "locationCountry", source = "locationCountry")
    @Mapping(target = "year", source = "year")
    @Mapping(target = "attendanceConfigId", source = "calendarConfigId")
    CalendarLegalLeaveConfigQuery toLegalLeaveConfigQuery(CalendarLegalLeaveConfigDetailQuery leaveConfigDetailQuery);
}
