package com.imile.attendance.calendar.command;

import com.imile.attendance.calendar.dto.LegalLeaveConfigInfoParam;
import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} CalendarLegalLeaveConfigAddCommand
 * {@code @since:} 2025-02-24 15:04
 * {@code @description:}
 */
@Data
@ApiModel(description = "日历法定假期记录表保存入参")
public class CalendarLegalLeaveConfigAddCommand {
    /**
     * 常驻地国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String locationCountry;

    /**
     * 年份
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer year;

    /**
     * 国家法定假期信息参数
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<LegalLeaveConfigInfoParam> legalLeaveConfigList;
}
