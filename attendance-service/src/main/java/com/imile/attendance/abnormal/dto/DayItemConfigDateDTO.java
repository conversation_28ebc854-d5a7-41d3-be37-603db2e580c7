package com.imile.attendance.abnormal.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/5/26
 */
@Data
public class DayItemConfigDateDTO {

    private Date punchInTime;

    private Date punchOutTime;

    private Date earliestPunchInTime;

    private Date latestPunchInTime;

    private Date restStartTime;

    private Date restEndTime;

    private Date latestPunchOutTime;

    private BigDecimal itemTotalMinutes;

    private Long betweenMinutes = 0L;
}
