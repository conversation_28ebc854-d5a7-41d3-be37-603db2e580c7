package com.imile.attendance.abnormal.service.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 无排班计划考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "NoSchedulingCalculateServiceImpl")
public class NoSchedulingCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {


    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return !Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), punchConfigType) && CollectionUtils.isEmpty(userAttendancePunchConfigList);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();

        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = calculateContext.getAttendanceEmployeeDetailDOList();
        List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList = calculateContext.getUserAbnormalAttendanceDOList();

        //需要将当天已经存在的考勤全部删除，会重新计算(请假/外勤审批通过的肯定不能删除,不然会重复计算，浪费假期)
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = attendanceEmployeeDetailDOList
                .stream().filter(item -> Objects.isNull(item.getFormId())).collect(Collectors.toList());
        updateEmployeeDetailDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        //没有被审批单关联的单据，可以删除的异常
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                        || StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode())).collect(Collectors.toList());

        updateAbnormalAttendanceDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();

        //当天的起始截止时间（截止时间用第二天的开始时间，如果用当天的结束时间23.59.59，会有1分钟的误差）
        calculateHandlerDTO.setActualAttendanceStartTime(DateHelper.beginOfDay(calculateHandlerDTO.getAttendanceTime()));
        calculateHandlerDTO.setActualAttendanceEndTime(DateHelper.beginOfDay(DateHelper.pushDate(calculateHandlerDTO.getActualAttendanceStartTime(), 1)));

        BigDecimal legalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
        BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);

        //查询当天生成的正常考勤的所有分钟(请假/外勤的 正常考勤会被删除，然后根据本次运行结果，看是否生成正常还是异常考勤)
        BigDecimal usedMinutes = calculateUsedMinutes(attendanceEmployeeDetailDOList);

        //把当天正常考勤中的已经关联过审批通过的单据的正常考勤排除掉,防止重复计算
        List<Long> usedFormIdList = attendanceEmployeeDetailDOList.stream().map(AttendanceEmployeeDetailDO::getFormId).filter(Objects::nonNull).collect(Collectors.toList());
        for (AttendanceFormDetailBO attendanceFormDetailBO : calculateContext.getUserPassFormBOList()) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                log.info("无排班计划外勤申请单据：{}", JSON.toJSONString(formDO));
                log.info("无排班计划外勤申请单据明细：{}", JSON.toJSONString(attendanceFormDetailBO.getAttrDOList()));
                usedMinutes = outOffOfficeHandler(usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateContext.getUser(), formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateHandlerDTO, addEmployeeDetailDOList);
                continue;
            }

            //请假(未排班，当天请假，全部消耗假期)
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                log.info("无排班计划请假申请单据：{}", JSON.toJSONString(formDO));
                log.info("无排班计划请假申请单据明细：{}", JSON.toJSONString(attendanceFormDetailBO.getAttrDOList()));
                usedMinutes = leaveHandler(calculateContext.getUser(), usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateHandlerDTO, formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateContext.getUserLeaveDetailDOList(), calculateContext.getUserLeaveStageDetailDOList(),
                        calculateContext.getUserCompanyLeaveConfigDOList(), addEmployeeDetailDOList);
            }
        }

        //看看当天的时间是否足够法定时间了，如果不够，依然生成一条异常考勤
        if (usedMinutes.compareTo(totalMinutes) > -1) {
            attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
            return;
        }

        //审核中的异常
        List<EmployeeAbnormalAttendanceDO> inReviewAbnormalAttendanceDOList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())).collect(Collectors.toList());
        //如果当天存在审批中的异常，那么就无需再次生成异常了
        if (CollectionUtils.isEmpty(inReviewAbnormalAttendanceDOList)) {
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(calculateContext.getUser(), calculateHandlerDTO, AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode(),
                    calculateContext.getAttendanceType(), null, null, null, null);
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
        }
        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
    }
}
