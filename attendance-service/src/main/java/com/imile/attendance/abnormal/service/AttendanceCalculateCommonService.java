package com.imile.attendance.abnormal.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.AttendanceDataSourceEnum;
import com.imile.attendance.enums.StaffTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 考勤通用计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
public class AttendanceCalculateCommonService {

    @Resource
    protected DefaultIdWorker defaultIdWorker;
    @Resource
    protected AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    protected PunchClassConfigQueryService punchClassConfigQueryService;

    /**
     * 计算员工出勤明细中外勤和请假的总时长，这些时长都算当天员工考勤计算中已出勤的时长
     *
     * @param attendanceEmployeeDetailDOList 员工出勤明细
     * @return 已出勤得时长
     */
    public BigDecimal calculateUsedMinutes(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList) {
        BigDecimal usedMinutes = BigDecimal.ZERO;
        for (AttendanceEmployeeDetailDO detailDO : attendanceEmployeeDetailDOList) {
            if (detailDO.getFormId() == null) {
                continue;
            }
            if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                usedMinutes = usedMinutes.add(detailDO.getAttendanceMinutes());
            }
            if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                usedMinutes = usedMinutes.add(detailDO.getLeaveMinutes());
            }
        }
        return usedMinutes;
    }

    /**
     * 通过当天考勤开始结束时间和单据的开始结束时间计算得到外勤或请假的真正的开始结束时间
     */
    public void dayFormDateHandler(List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                   List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                   AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
            if (handlerFormDTO.getEndTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
                continue;
            }
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
                continue;
            }
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                    && handlerFormDTO.getEndTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
                handlerFormDTO.setStartTime(calculateHandlerDTO.getActualAttendanceStartTime());
                handlerFormDTO.setEndTime(calculateHandlerDTO.getActualAttendanceEndTime());
                filterFormDTOList.add(handlerFormDTO);
                continue;
            }
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) > -1
                    && handlerFormDTO.getEndTime().compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) < 1) {
                filterFormDTOList.add(handlerFormDTO);
                continue;
            }
            //交集
            if (handlerFormDTO.getStartTime().compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
                handlerFormDTO.setStartTime(calculateHandlerDTO.getActualAttendanceStartTime());
                filterFormDTOList.add(handlerFormDTO);
                continue;
            }
            handlerFormDTO.setEndTime(calculateHandlerDTO.getActualAttendanceEndTime());
            filterFormDTOList.add(handlerFormDTO);
        }
    }

    /**
     * 外勤处理
     */
    public BigDecimal outOffOfficeHandler(BigDecimal usedMinutes,
                                          BigDecimal totalMinutes,
                                          String attendanceType,
                                          UserInfoDO user,
                                          AttendanceFormDO formDO,
                                          List<AttendanceFormAttrDO> userPassFormAttrDOList,
                                          AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                          List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        List<AttendanceFormAttrDO> outOfOfficeStartDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode()))
                .collect(Collectors.toList());
        List<AttendanceFormAttrDO> outOfOfficeEndDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
            return usedMinutes;
        }
        Date outOfOfficeStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue());
        Date outOfOfficeEndDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue());

        Long outOfOfficeStartDayId = DateHelper.getDayId(outOfOfficeStartDate);
        Long outOfOfficeEndDayId = DateHelper.getDayId(outOfOfficeEndDate);
        if (calculateHandlerDTO.getAttendanceDayId().compareTo(outOfOfficeStartDayId) < 0 || calculateHandlerDTO.getAttendanceDayId().compareTo(outOfOfficeEndDayId) > 0) {
            return usedMinutes;
        }
        //没有交集，直接返回
        if (outOfOfficeEndDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                || outOfOfficeStartDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            return usedMinutes;
        }
        //外勤直接包含改天排班时间
        if (outOfOfficeStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                && outOfOfficeEndDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            usedMinutes = outOffOfficeTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), calculateHandlerDTO.getActualAttendanceEndTime(),
                    totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //改天排班时间包含外勤时间
        if (calculateHandlerDTO.getActualAttendanceStartTime().compareTo(outOfOfficeStartDate) < 1
                && calculateHandlerDTO.getActualAttendanceEndTime().compareTo(outOfOfficeEndDate) > -1) {
            usedMinutes = outOffOfficeTimeHandler(outOfOfficeStartDate, outOfOfficeEndDate, totalMinutes, usedMinutes, user, calculateHandlerDTO,
                    attendanceType, formDO, addEmployeeDetailDOList);
            return usedMinutes;
        }

        //左交集
        if (outOfOfficeStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
            usedMinutes = outOffOfficeTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), outOfOfficeEndDate, totalMinutes, usedMinutes,
                    user, calculateHandlerDTO, attendanceType, formDO, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //右交集
        usedMinutes = outOffOfficeTimeHandler(outOfOfficeStartDate, calculateHandlerDTO.getActualAttendanceEndTime(), totalMinutes, usedMinutes, user,
                calculateHandlerDTO, attendanceType, formDO, addEmployeeDetailDOList);
        return usedMinutes;
    }

    public BigDecimal outOffOfficeTimeHandler(Date actualAttendanceStartTime,
                                              Date actualAttendanceEndTime,
                                              BigDecimal totalMinutes,
                                              BigDecimal usedMinutes,
                                              UserInfoDO user,
                                              AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                              String attendanceType,
                                              AttendanceFormDO formDO,
                                              List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        BigDecimal outOfOfficeMinutes = BigDecimal.valueOf(DateUtil.between(actualAttendanceStartTime, actualAttendanceEndTime, DateUnit.MINUTE));
        //已经外勤的加上本次外勤的，已经够了当天法定工作时间,不需要再外勤
        if (outOfOfficeMinutes.add(usedMinutes).compareTo(totalMinutes) > -1) {
            outOfOfficeMinutes = totalMinutes.subtract(usedMinutes);
        }
        if (outOfOfficeMinutes.compareTo(BigDecimal.ZERO) < 1) {
            return usedMinutes;
        }
        AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.OOO.getCode(),
                BusinessConstant.Y, null, null, null, BigDecimal.ZERO, outOfOfficeMinutes, BigDecimal.ZERO, formDO.getId());
        addEmployeeDetailDOList.add(userAttendance);
        usedMinutes = usedMinutes.add(outOfOfficeMinutes);
        return usedMinutes;
    }

    /**
     * 请假处理
     */
    public BigDecimal leaveHandler(UserInfoDO user,
                                   BigDecimal usedMinutes,
                                   BigDecimal totalMinutes,
                                   String attendanceType,
                                   AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                   AttendanceFormDO formDO,
                                   List<AttendanceFormAttrDO> userPassFormAttrDOList,
                                   List<UserLeaveDetailDO> userLeaveDetailDOList,
                                   List<UserLeaveStageDetailDO> userLeaveStageDetailDOList,
                                   List<CompanyLeaveConfigDO> userCompanyLeaveConfigDOList,
                                   List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {

        List<AttendanceFormAttrDO> leaveStartDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode()))
                .collect(Collectors.toList());

        List<AttendanceFormAttrDO> leaveEndDateDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode()))
                .collect(Collectors.toList());

        List<AttendanceFormAttrDO> leaveTypeDO = userPassFormAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(leaveStartDateDO) || CollectionUtils.isEmpty(leaveEndDateDO) || CollectionUtils.isEmpty(leaveTypeDO)) {
            return usedMinutes;
        }

        List<CompanyLeaveConfigDO> companyLeaveList = userCompanyLeaveConfigDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveType(), leaveTypeDO.get(0).getAttrValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(companyLeaveList)) {
            return usedMinutes;
        }
        List<UserLeaveDetailDO> leaveDetailList = userLeaveDetailDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveType(), leaveTypeDO.get(0).getAttrValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveDetailList)) {
            return usedMinutes;
        }
        List<Long> leaveIdList = leaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        //根据百分比降序，优先使用百分比最高的
        List<UserLeaveStageDetailDO> leaveStageDetailDOList = userLeaveStageDetailDOList.stream()
                .filter(item -> leaveIdList.contains(item.getLeaveId())).sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaveStageDetailDOList)) {
            return usedMinutes;
        }

        Date leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue());
        Date leaveEndDate = DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue());

        Long leaveStartDayId = DateHelper.getDayId(leaveStartDate);
        Long leaveEndDayId = DateHelper.getDayId(leaveEndDate);

        if (calculateHandlerDTO.getAttendanceDayId().compareTo(leaveStartDayId) < 0 || calculateHandlerDTO.getAttendanceDayId().compareTo(leaveEndDayId) > 0) {
            return usedMinutes;
        }

        //没有交集，直接返回
        if (leaveEndDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                || leaveStartDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            return usedMinutes;
        }
        //请假直接包含改天排班时间
        if (leaveStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1
                && leaveEndDate.compareTo(calculateHandlerDTO.getActualAttendanceEndTime()) > -1) {
            usedMinutes = leaveTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), calculateHandlerDTO.getActualAttendanceEndTime(), totalMinutes,
                    usedMinutes, user, calculateHandlerDTO, attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //改天排班时间包含请假时间
        if (calculateHandlerDTO.getActualAttendanceStartTime().compareTo(leaveStartDate) < 1
                && calculateHandlerDTO.getActualAttendanceEndTime().compareTo(leaveEndDate) > -1) {
            usedMinutes = leaveTimeHandler(leaveStartDate, leaveEndDate, totalMinutes, usedMinutes, user, calculateHandlerDTO, attendanceType, formDO,
                    companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
            return usedMinutes;
        }

        //左交集
        if (leaveStartDate.compareTo(calculateHandlerDTO.getActualAttendanceStartTime()) < 1) {
            usedMinutes = leaveTimeHandler(calculateHandlerDTO.getActualAttendanceStartTime(), leaveEndDate, totalMinutes, usedMinutes, user, calculateHandlerDTO,
                    attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
            return usedMinutes;
        }
        //右交集
        usedMinutes = leaveTimeHandler(leaveStartDate, calculateHandlerDTO.getActualAttendanceEndTime(), totalMinutes, usedMinutes, user, calculateHandlerDTO,
                attendanceType, formDO, companyLeaveList, leaveStageDetailDOList, addEmployeeDetailDOList);
        return usedMinutes;

    }

    public BigDecimal leaveTimeHandler(Date actualAttendanceStartTime,
                                       Date actualAttendanceEndTime,
                                       BigDecimal totalMinutes,
                                       BigDecimal usedMinutes,
                                       UserInfoDO user,
                                       AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                       String attendanceType,
                                       AttendanceFormDO formDO,
                                       List<CompanyLeaveConfigDO> companyLeaveList,
                                       List<UserLeaveStageDetailDO> leaveStageDetailDOList,
                                       List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        BigDecimal leaveMinutes = BigDecimal.valueOf(DateUtil.between(actualAttendanceStartTime, actualAttendanceEndTime, DateUnit.MINUTE));
        //已经请假的加上本次外勤的，已经够了当天法定工作时间，就不会在请假了
        if (leaveMinutes.add(usedMinutes).compareTo(totalMinutes) > -1) {
            leaveMinutes = totalMinutes.subtract(usedMinutes);
        }
        if (leaveMinutes.compareTo(BigDecimal.ZERO) < 1) {
            return usedMinutes;
        }
        leaveInfoBuild(user, calculateHandlerDTO, formDO.getId(), companyLeaveList.get(0), leaveStageDetailDOList, leaveMinutes, attendanceType, addEmployeeDetailDOList);
        usedMinutes = usedMinutes.add(leaveMinutes);
        return usedMinutes;
    }

    public void leaveInfoBuild(UserInfoDO user,
                               AttendanceCalculateHandlerDTO calculateHandlerDTO,
                               Long formId,
                               CompanyLeaveConfigDO companyLeaveConfigDO,
                               List<UserLeaveStageDetailDO> leaveStageDetailDOList,
                               BigDecimal leaveMinutes,
                               String attendanceType,
                               List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        //阶梯假
        for (int i = 0; i < leaveStageDetailDOList.size(); i++) {
            UserLeaveStageDetailDO detailDO = leaveStageDetailDOList.get(i);
            //不是最后一个阶梯，并且有假期<=0
            if (i != leaveStageDetailDOList.size() - 1 && detailDO.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType,
                    companyLeaveConfigDO.getLeaveShortName(), BusinessConstant.Y, companyLeaveConfigDO.getLeaveName()
                    , detailDO.getPercentSalary(), detailDO.getStage(), leaveMinutes, BigDecimal.ZERO, BigDecimal.ZERO, formId);
            addEmployeeDetailDOList.add(userAttendance);
            break;
        }
    }

    /**
     * 组装员工出勤明细
     */
    public AttendanceEmployeeDetailDO buildUserAttendance(UserInfoDO user,
                                                          AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                                          String attendanceType,
                                                          String concreteType,
                                                          int isAttendance,
                                                          String leaveType,
                                                          BigDecimal leavePercentSalary,
                                                          Integer stage,
                                                          BigDecimal leaveMinutes,
                                                          BigDecimal attendanceMinutes,
                                                          BigDecimal overtimeMinutes,
                                                          Long formId) {
        AttendanceEmployeeDetailDO userAttendance = new AttendanceEmployeeDetailDO();
        Date date = DateHelper.transferDayIdToDate(calculateHandlerDTO.getAttendanceDayId());
        userAttendance.setId(defaultIdWorker.nextId());
        userAttendance.setUserId(user.getId());
        userAttendance.setLocationCountry(user.getLocationCountry());
        userAttendance.setYear((long) DateUtil.year(date));
        userAttendance.setMonth((long) (DateUtil.month(date) + 1));
        userAttendance.setDay(DateUtil.dayOfMonth(date));
        userAttendance.setDayId(calculateHandlerDTO.getAttendanceDayId());
        userAttendance.setDate(DateUtil.beginOfDay(date));
        userAttendance.setDataSource(AttendanceDataSourceEnum.SYSTEM.getCode());
        userAttendance.setAttendanceType(attendanceType);
        userAttendance.setConcreteType(concreteType);
        userAttendance.setIsAttendance(isAttendance);
        userAttendance.setDeptId(user.getDeptId());
        userAttendance.setPostId(user.getPostId());
        userAttendance.setLeaveType(leaveType);
        userAttendance.setLeavePercentSalary(leavePercentSalary);
        userAttendance.setStage(stage);
        userAttendance.setLeaveMinutes(leaveMinutes);
        userAttendance.setAttendanceMinutes(attendanceMinutes);
        userAttendance.setOvertimeMinutes(overtimeMinutes);
        userAttendance.setFormId(formId);
        userAttendance.setClassId(calculateHandlerDTO.getClassId());
        BaseDOUtil.fillDOInsertByUsrOrSystem(userAttendance);
        return userAttendance;
    }

    /**
     * 组装员工异常出勤
     */
    public EmployeeAbnormalAttendanceDO buildAbnormal(UserInfoDO user,
                                                      AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                                      String abnormalType,
                                                      String attendanceType,
                                                      Long punchConfigId,
                                                      Long punchClassConfigId,
                                                      Long punchClassItemConfigId,
                                                      String extend) {
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = new EmployeeAbnormalAttendanceDO();
        abnormalAttendanceDO.setId(defaultIdWorker.nextId());
        abnormalAttendanceDO.setDeptId(user.getDeptId());
        abnormalAttendanceDO.setPostId(user.getPostId());
        abnormalAttendanceDO.setDate(calculateHandlerDTO.getAttendanceTime());
        abnormalAttendanceDO.setDayId(calculateHandlerDTO.getAttendanceDayId());
        abnormalAttendanceDO.setUserId(user.getId());
        abnormalAttendanceDO.setLocationCountry(user.getLocationCountry());
        abnormalAttendanceDO.setStaffType(getStaffType(user));
        abnormalAttendanceDO.setEmployeeType(user.getEmployeeType());
        abnormalAttendanceDO.setAbnormalType(abnormalType);
        abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode());
        abnormalAttendanceDO.setAttendanceType(attendanceType);
        abnormalAttendanceDO.setPunchConfigId(punchConfigId);
        abnormalAttendanceDO.setPunchClassConfigId(punchClassConfigId);
        abnormalAttendanceDO.setPunchClassItemConfigId(punchClassItemConfigId);
        abnormalAttendanceDO.setExtend(extend);
        BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalAttendanceDO);
        return abnormalAttendanceDO;
    }

    public String getStaffType(UserInfoDO userInfoDO) {
        if (userInfoDO.getIsDriver() == 1) {
            return StaffTypeEnum.DRIVER.getCode();
        }
        if (userInfoDO.getIsWarehouseStaff() == 1) {
            return StaffTypeEnum.WAREHOUSE.getCode();
        }
        return StaffTypeEnum.OFFICE.getCode();
    }

    public List<EmployeeAbnormalAttendanceDO> filterAbnormalAttendanceList(List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList,
                                                                           List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        List<String> inReviewAbnormalTypeList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode()))
                .map(EmployeeAbnormalAttendanceDO::getAbnormalType).collect(Collectors.toList());
        return addAbnormalAttendanceDOList.stream().filter(abnormal -> !inReviewAbnormalTypeList.contains(abnormal.getAbnormalType())).collect(Collectors.toList());
    }

    /**
     * 筛选得到当天外勤或请假的单据
     */
    public void dayFormInfoBuild(List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                 List<Long> usedFormIdList,
                                 List<AttendanceFormDetailBO> userPassFormBOList) {
        for (AttendanceFormDetailBO attendanceFormDetailBO : userPassFormBOList) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            List<AttendanceFormAttrDO> userPassFormAttrDOList = attendanceFormDetailBO.getAttrDOList();
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                List<AttendanceFormAttrDO> leaveStartDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveEndDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> configIdDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.configID.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveNameDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(leaveStartDateDO) || CollectionUtils.isEmpty(leaveEndDateDO) || CollectionUtils.isEmpty(leaveNameDO)) {
                    continue;
                }

                Date leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue());
                Date leaveEndDate = DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue());

                Long leaveStartDayId = DateHelper.getDayId(leaveStartDate);
                Long leaveEndDayId = DateHelper.getDayId(leaveEndDate);

                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                if (CollectionUtils.isNotEmpty(configIdDO) && Objects.nonNull(configIdDO.get(0).getAttrValue())) {
                    dayAttendanceHandlerFormDTO.setConfigId(Long.valueOf(configIdDO.get(0).getAttrValue()));
                }
                dayAttendanceHandlerFormDTO.setLeaveType(leaveNameDO.get(0).getAttrValue());
                dayAttendanceHandlerFormDTO.setStartTime(leaveStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(leaveStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(leaveEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(leaveEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }

            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                List<AttendanceFormAttrDO> outOfOfficeStartDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> outOfOfficeEndDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
                    continue;
                }

                Date outOfOfficeStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue());
                Date outOfOfficeEndDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue());

                Long outOfOfficeStartDayId = DateHelper.getDayId(outOfOfficeStartDate);
                Long outOfOfficeEndDayId = DateHelper.getDayId(outOfOfficeEndDate);

                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(outOfOfficeStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(outOfOfficeStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(outOfOfficeEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(outOfOfficeEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
        }
    }


    public BigDecimal shiftDayLeaveMinuteHandler(DayAttendanceHandlerFormDTO handlerFormDTO,
                                                 Date punchInTime,
                                                 Date punchOutTime,
                                                 Date restStartTime,
                                                 Date restEndTime,
                                                 long betweenMinutes,
                                                 List<DayAttendanceHandlerFormDTO> filterFormDTOList) {
        BigDecimal leaveMinutes = BigDecimal.ZERO;
        //没有交集
        if (handlerFormDTO.getEndTime().compareTo(punchInTime) < 1 || handlerFormDTO.getStartTime().compareTo(punchOutTime) > -1) {
            return leaveMinutes;
        }

        // 这边根据上班时间来设置下班时间是正常的班次时间还是弹性后的时间
        Date actualDayPunchEndTime = punchOutTime;
        if (betweenMinutes != 0) {
            actualDayPunchEndTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
        }
        if (handlerFormDTO.getStartTime().compareTo(actualDayPunchEndTime) > -1) {
            return leaveMinutes;
        }

        Date actualLeaveStartTime;
        Date actualLeaveEndTime;
        DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = BeanUtils.convert(handlerFormDTO, DayAttendanceHandlerFormDTO.class);
        //请假开始时间小于正常上班时间
        if (handlerFormDTO.getStartTime().compareTo(punchInTime) < 1) {
            actualLeaveStartTime = punchInTime;
            actualLeaveEndTime = handlerFormDTO.getEndTime();
            //请假时间大于该时刻的下班时间
            if (handlerFormDTO.getEndTime().compareTo(punchOutTime) > -1) {
                actualLeaveEndTime = punchOutTime;
            }
            dayAttendanceHandlerFormDTO.setStartTime(actualLeaveStartTime);
            dayAttendanceHandlerFormDTO.setEndTime(actualLeaveEndTime);
            filterFormDTOList.add(dayAttendanceHandlerFormDTO);
            leaveMinutes = BigDecimal.valueOf(DateUtil.between(actualLeaveStartTime, actualLeaveEndTime, DateUnit.MINUTE));
            //看看有没有休息时间，有旧减去休息时间
            if (restStartTime != null) {
                if (actualLeaveEndTime.compareTo(restStartTime) < 1) {
                    return leaveMinutes;
                }
                if (actualLeaveEndTime.compareTo(restEndTime) > -1) {
                    leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
                    return leaveMinutes;
                }
                //卡住休息时间中间
                leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, actualLeaveEndTime, DateUnit.MINUTE)));
                return leaveMinutes;
            }
            return leaveMinutes;
        }

        //请假开始时间大于正常上班时间
        actualLeaveStartTime = handlerFormDTO.getStartTime();
        actualLeaveEndTime = handlerFormDTO.getEndTime();
        //请假结束时间大于该时刻的下班时间
        if (handlerFormDTO.getEndTime().compareTo(punchOutTime) > -1) {
            actualLeaveEndTime = punchOutTime;

            // 如果betweenMinutes不为0 并且请假结束时间大于正常下班时间，说明请假结束时间需要弹性调整
            if (handlerFormDTO.getEndTime().compareTo(punchOutTime) > 0) {
                if (handlerFormDTO.getEndTime().compareTo(actualDayPunchEndTime) > 0) {
                    actualLeaveEndTime = actualDayPunchEndTime;
                } else {
                    actualLeaveEndTime = handlerFormDTO.getEndTime();
                }
            }
        }

        leaveMinutes = BigDecimal.valueOf(DateUtil.between(actualLeaveStartTime, actualLeaveEndTime, DateUnit.MINUTE));
        dayAttendanceHandlerFormDTO.setStartTime(actualLeaveStartTime);
        dayAttendanceHandlerFormDTO.setEndTime(actualLeaveEndTime);
        filterFormDTOList.add(dayAttendanceHandlerFormDTO);
        //看看有没有休息时间，有旧减去休息时间
        if (restStartTime != null) {
            if (actualLeaveStartTime.compareTo(restEndTime) > -1) {
                return leaveMinutes;
            }
            if (actualLeaveEndTime.compareTo(restStartTime) < 1) {
                return leaveMinutes;
            }
            //休息时间完全被请假时间包含
            if (actualLeaveStartTime.compareTo(restStartTime) < 1 && actualLeaveEndTime.compareTo(restEndTime) > -1) {
                leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
                return leaveMinutes;
            }
            //请假时间完全被休息时间包含
            if (actualLeaveStartTime.compareTo(restStartTime) > -1 && actualLeaveEndTime.compareTo(restEndTime) < 1) {
                leaveMinutes = BigDecimal.ZERO;
                return leaveMinutes;
            }
            //左交集
            if (actualLeaveStartTime.compareTo(restStartTime) < 1) {
                leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, actualLeaveEndTime, DateUnit.MINUTE)));
                return leaveMinutes;
            }
            //右交集
            leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(actualLeaveStartTime, restEndTime, DateUnit.MINUTE)));
        }
        return leaveMinutes;
    }

    /**
     * 计算得到班次时段的每个时间
     */
    public DayItemConfigDateDTO buildDayItemConfigDateDTO(UserInfoDO user,
                                                          PunchClassItemConfigDTO itemConfigDO,
                                                          List<PunchClassItemConfigDO> itemConfigDOList,
                                                          List<UserPunchRecordBO> punchRecordList,
                                                          AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        DayItemConfigDateDTO itemConfigDateDTO = new DayItemConfigDateDTO();
        //获取当前时刻的正常时间
        DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(calculateHandlerDTO.getAttendanceDayId(), itemConfigDO.getId(), itemConfigDOList);
        if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
            log.info("error userCode:{}, date:{}, 当天排班，有明确的班次,这个用户当天真正打卡时间无法计算", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
            return null;
        }

        //获取打卡时间的所有点
        //最早上班打卡时间
        Date earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
        //上班时间早于最早打卡时间，跨天
        Date punchInTime;
        if (itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime())) {
            punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(earliestPunchInTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getPunchInTime()));
        } else {
            punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(earliestPunchInTime), DateHelper.formatHHMMSS(itemConfigDO.getPunchInTime()));
        }
        itemConfigDateDTO.setPunchInTime(punchInTime);
        itemConfigDateDTO.setEarliestPunchInTime(earliestPunchInTime);

        //最晚上班打卡时间
        Date latestPunchInTime;
        if (itemConfigDO.getLatestPunchInTime().before(itemConfigDO.getPunchInTime())) {
            latestPunchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(punchInTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getLatestPunchInTime()));
        } else {
            latestPunchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(punchInTime), DateHelper.formatHHMMSS(itemConfigDO.getLatestPunchInTime()));
        }
        itemConfigDateDTO.setLatestPunchInTime(latestPunchInTime);

        //休息开始时间
        Date restStartTime = null;
        Date restEndTime = null;
        if (itemConfigDO.getRestStartTime() != null) {
            if (itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime())) {
                restStartTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(punchInTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getRestStartTime()));
            } else {
                restStartTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(punchInTime), DateHelper.formatHHMMSS(itemConfigDO.getRestStartTime()));
            }
            itemConfigDateDTO.setRestStartTime(restStartTime);
            if (itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime())) {
                restEndTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(restStartTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getRestEndTime()));
            } else {
                restEndTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(restStartTime), DateHelper.formatHHMMSS(itemConfigDO.getRestEndTime()));
            }
            itemConfigDateDTO.setRestEndTime(restEndTime);
        }

        //最晚下班打卡时间
        Date latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();
        //下班时间
        Date punchOutTime;
        if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
            punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(latestPunchOutTime, -1)), DateHelper.formatHHMMSS(itemConfigDO.getPunchOutTime()));
        } else {
            punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(latestPunchOutTime), DateHelper.formatHHMMSS(itemConfigDO.getPunchOutTime()));
        }
        itemConfigDateDTO.setPunchOutTime(punchOutTime);
        itemConfigDateDTO.setLatestPunchOutTime(latestPunchOutTime);

        calculateHandlerDTO.setActualAttendanceStartTime(punchInTime);
        calculateHandlerDTO.setActualAttendanceEndTime(punchOutTime);

        BigDecimal itemTotalMinutes = BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE));
        if (restStartTime != null) {
            itemTotalMinutes = itemTotalMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
        }
        itemConfigDateDTO.setItemTotalMinutes(itemTotalMinutes);

        // 获取该班次最早上班打卡时间和最晚上班打卡时间之间的打卡记录
        Date finalLatestPunchInTime = latestPunchInTime;
        punchRecordList = punchRecordList.stream()
                .filter(item -> item.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && item.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1)
                .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime)).collect(Collectors.toList());
        // 获取最新的一个打卡记录
        UserPunchRecordBO userPunchRecordDTO;
        // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
        long betweenMinutes = 0;
        if (CollUtil.isNotEmpty(punchRecordList)) {
            userPunchRecordDTO = punchRecordList.get(0);
            // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
            if (userPunchRecordDTO.getFormatPunchTime().compareTo(punchInTime) > -1 && userPunchRecordDTO.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1) {
                // 获取两个时间相差分钟数
                betweenMinutes = DateUtil.between(userPunchRecordDTO.getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
            }
            itemConfigDateDTO.setBetweenMinutes(betweenMinutes);
        }
        return itemConfigDateDTO;
    }

    /**
     * 根据请假和外勤计算得到已出勤的异常
     */
    public BigDecimal calculateLeaveHandler(AttendanceCalculateContext calculateContext,
                                            List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                            List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                            AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                            DayItemConfigDateDTO itemConfigDateDTO,
                                            List<DayAttendanceHandlerFormDTO> filterFormDTOList) {
        BigDecimal usedMinutes = BigDecimal.ZERO;
        BigDecimal leaveMinutes;
        //找出和该时刻相关的所有单据
        //先生成正常考勤，所有的请假/外勤落库
        for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
            //请假时间和本时刻一定有交集，看和时刻内的休息时间的关系
            //看看本次请假/外勤的起始时间还是结束时间有没有落在上班的弹性时间中(会有一个假跨多个时段的情况出现)
            leaveMinutes = shiftDayLeaveMinuteHandler(handlerFormDTO, itemConfigDateDTO.getPunchInTime(), itemConfigDateDTO.getPunchOutTime(), itemConfigDateDTO.getRestStartTime(),
                    itemConfigDateDTO.getRestEndTime(), itemConfigDateDTO.getBetweenMinutes(), filterFormDTOList);
            //没有交集
            if (leaveMinutes.compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }
            //已经请假的加上本次外勤的，已经够了当天法定工作时间，就不会在请假了
            if (leaveMinutes.add(usedMinutes).compareTo(itemConfigDateDTO.getItemTotalMinutes()) > -1) {
                leaveMinutes = itemConfigDateDTO.getItemTotalMinutes().subtract(usedMinutes);
            }
            usedMinutes = usedMinutes.add(leaveMinutes);
            //看是不是外勤
            if (StringUtils.isBlank(handlerFormDTO.getLeaveType())) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(calculateContext.getUser(), calculateHandlerDTO, calculateContext.getAttendanceType(),
                        AttendanceConcreteTypeEnum.OOO.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, leaveMinutes,
                        BigDecimal.ZERO, handlerFormDTO.getFormId());
                addEmployeeDetailDOList.add(userAttendance);
                continue;
            }
            //请假
            List<CompanyLeaveConfigDO> companyLeaveList;
            List<UserLeaveDetailDO> leaveDetailList;
            if (Objects.nonNull(handlerFormDTO.getConfigId())) {
                companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                        .filter(item -> item.getId().equals(handlerFormDTO.getConfigId()))
                        .collect(Collectors.toList());
                leaveDetailList = calculateContext.getUserLeaveDetailDOList().stream()
                        .filter(item -> item.getConfigId().equals(handlerFormDTO.getConfigId()))
                        .collect(Collectors.toList());
            } else {
                companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), handlerFormDTO.getLeaveType()))
                        .collect(Collectors.toList());
                leaveDetailList = calculateContext.getUserLeaveDetailDOList().stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), handlerFormDTO.getLeaveType()))
                        .collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(companyLeaveList)) {
                return null;
            }
            if (CollectionUtils.isEmpty(leaveDetailList)) {
                return null;
            }

            List<Long> leaveIdList = leaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
            //根据百分比降序，优先使用百分比最高的
            List<UserLeaveStageDetailDO> leaveStageDetailDOList = calculateContext.getUserLeaveStageDetailDOList().stream()
                    .filter(item -> leaveIdList.contains(item.getLeaveId()))
                    .sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveStageDetailDOList)) {
                return null;
            }
            leaveInfoBuild(calculateContext.getUser(), calculateHandlerDTO, handlerFormDTO.getFormId(), companyLeaveList.get(0), leaveStageDetailDOList,
                    leaveMinutes, calculateContext.getAttendanceType(), addEmployeeDetailDOList);
            usedMinutes = usedMinutes.add(leaveMinutes);
        }
        return usedMinutes;
    }

    public List<UserPunchRecordBO> getEffectiveUserPunchRecord(Date latestPunchInTime,
                                                               Date latestPunchOutTime,
                                                               List<UserPunchRecordBO> punchRecordDOList) {
        return punchRecordDOList.stream()
                .filter(item -> item.getFormatPunchTime().compareTo(latestPunchInTime) > -1 && item.getFormatPunchTime().compareTo(latestPunchOutTime) < 1)
                .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime)).collect(Collectors.toList());
    }

    /**
     * 仅一次打卡记录考勤处理
     */
    public void singlePunchRecordHandler(UserInfoDO user,
                                         AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                         List<UserPunchRecordBO> itemPunchRecordList,
                                         List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                         Long punchConfigId,
                                         Long classId,
                                         Long itemConfigId,
                                         String attendanceType,
                                         Date punchInTime,
                                         Date punchOutTime,
                                         Date latestPunchInTime) {
        //2条异常，一个缺卡，一个早退/迟到
        //打了上班卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) > -1) {
                // 打卡时间大于等于上班时间，小于等于最晚上班时间也就是弹性的时间，
                long betweenMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
                // 弹性之后的下班时间：
                Date actualLeaveTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
                abnormalExtendDTO.setCorrectPunchTime(actualLeaveTime);
            }
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //打了下班卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //2条异常，一个缺卡，一个早退/迟到
        long beforeMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
        long afterMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchOutTime, DateUnit.MINUTE);
        if (beforeMinutes < afterMinutes) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

            abnormalExtendDTO.setActualPunchTime(null);
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
        abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
        EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

        abnormalExtendDTO.setActualPunchTime(null);
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
    }
}
