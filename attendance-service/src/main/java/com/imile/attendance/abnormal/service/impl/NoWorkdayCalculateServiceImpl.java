package com.imile.attendance.abnormal.service.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 非工作(周末、节假日)考勤计算
 * 非工作日一定会生成正常的员工出勤明细，如果发生了外勤或请假操作，还会生成外勤和请假的员工出勤明细
 *
 * <AUTHOR>
 * @since 2025/5/21
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "NoWorkdayCalculateServiceImpl")
public class NoWorkdayCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {

    @Autowired
    private CompanyLeaveConfigService leaveConfigService;

    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return !Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), punchConfigType)
                && CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.N, userAttendancePunchConfigList.get(0).getIsActualPunch());
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();

        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = calculateContext.getAttendanceEmployeeDetailDOList();
        List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList = calculateContext.getUserAbnormalAttendanceDOList();

        //需要将当天已经存在的考勤全部删除，会重新计算(请假/外勤审批通过的肯定不能删除,不然会重复计算，浪费假期)
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = attendanceEmployeeDetailDOList
                .stream().filter(item -> Objects.isNull(item.getFormId())).collect(Collectors.toList());
        updateEmployeeDetailDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        //没有被审批单关联的单据，可以删除的异常
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = userAbnormalAttendanceDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                        || StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode())).collect(Collectors.toList());

        updateAbnormalAttendanceDOList.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });

        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();

        //当天的起始截止时间（截止时间用第二天的开始时间，如果用当天的结束时间23.59.59，会有1分钟的误差）
        calculateHandlerDTO.setActualAttendanceStartTime(DateHelper.beginOfDay(calculateHandlerDTO.getAttendanceTime()));
        calculateHandlerDTO.setActualAttendanceEndTime(DateHelper.beginOfDay(DateHelper.pushDate(calculateHandlerDTO.getActualAttendanceStartTime(), 1)));

        BigDecimal legalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
        BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);

        //查询当天生成的正常考勤的所有分钟(请假/外勤的 正常考勤会被删除，然后根据本次运行结果，看是否生成正常还是异常考勤)
        BigDecimal usedMinutes = calculateUsedMinutes(attendanceEmployeeDetailDOList);

        //把当天正常考勤中的已经关联过审批通过的单据的正常考勤排除掉,防止重复计算
        List<Long> usedFormIdList = attendanceEmployeeDetailDOList.stream().map(AttendanceEmployeeDetailDO::getFormId).filter(Objects::nonNull).collect(Collectors.toList());

        //获取当天的排班
        String dayPunchType = calculateContext.getUserAttendancePunchConfigDTOList().get(0).getDayPunchType();
        for (AttendanceFormDetailBO attendanceFormDetailBO : calculateContext.getUserPassFormBOList()) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                log.info("非工作日外勤申请单据：{}", JSON.toJSONString(formDO));
                log.info("非工作日外勤申请单据明细：{}", JSON.toJSONString(attendanceFormDetailBO.getAttrDOList()));
                usedMinutes = outOffOfficeHandler(usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateContext.getUser(), formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateHandlerDTO, addEmployeeDetailDOList);
                continue;
            }

            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                log.info("非工作日请假申请单据：{}", JSON.toJSONString(formDO));
                log.info("非工作日请假申请单据明细：{}", JSON.toJSONString(attendanceFormDetailBO.getAttrDOList()));
                //直接这里判断是否消耗假期，如果不消耗，无需后续的所有处理，也无需落库，直接结束
                List<AttendanceFormAttrDO> configIdDO = attendanceFormDetailBO.getAttrDOList().stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveTypeDO = attendanceFormDetailBO.getAttrDOList().stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
                List<CompanyLeaveConfigDO> companyLeaveList;
                if (CollectionUtils.isNotEmpty(configIdDO) && Objects.nonNull(configIdDO.get(0).getAttrValue())) {
                    companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                            .filter(item -> item.getId().equals(Long.valueOf(configIdDO.get(0).getAttrValue())))
                            .collect(Collectors.toList());
                } else {
                    companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                            .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), leaveTypeDO.get(0).getAttrValue()))
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(companyLeaveList)
                        || !leaveConfigService.isConsumeLeave(companyLeaveList.get(0).getConsumeLeaveType(), dayPunchType)) {
                    continue;
                }
                usedMinutes = leaveHandler(calculateContext.getUser(), usedMinutes, totalMinutes, calculateContext.getAttendanceType(), calculateHandlerDTO, formDO,
                        attendanceFormDetailBO.getAttrDOList(), calculateContext.getUserLeaveDetailDOList(), calculateContext.getUserLeaveStageDetailDOList(),
                        calculateContext.getUserCompanyLeaveConfigDOList(), addEmployeeDetailDOList);
            }
        }


        //OFF/PH不用看时间够不够，当天一定会落一天OFF/PH的正常数据，如果当天有请假/外勤，在落请假/外勤数据 绝对不会生成异常考勤
        AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(calculateContext.getUser(), calculateHandlerDTO, calculateContext.getAttendanceType(), dayPunchType, BusinessConstant.Y,
                null, null, null, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null);
        addEmployeeDetailDOList.add(userAttendance);
        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, null, updateAbnormalAttendanceDOList);
    }
}
