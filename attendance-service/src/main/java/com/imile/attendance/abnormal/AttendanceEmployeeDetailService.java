package com.imile.attendance.abnormal;

import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/21
 * @Description 人员正常表相关服务
 */
@Service
public class AttendanceEmployeeDetailService {
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;

    // 通用查询相关

    /**
     * 根据申请单主键查询 (原有manage方法)
     *
     * @param formIdList
     * @return
     */
    public List<AttendanceEmployeeDetailDO> selectByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        return attendanceEmployeeDetailDao.selectByFormIdList(formIdList);
    }

}
