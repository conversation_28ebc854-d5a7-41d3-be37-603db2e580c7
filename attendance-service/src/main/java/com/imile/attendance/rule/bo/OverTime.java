package com.imile.attendance.rule.bo;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.OverTimeSubsidyWayEnum;
import com.imile.attendance.exception.BusinessLogicException;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description 加班配置
 */
@Data
public class OverTime {

    private static final List<BigDecimal> VALID_WORKING_TIME = Arrays.asList(
            new BigDecimal("0"), new BigDecimal("0.5"), new BigDecimal("1"),
            new BigDecimal("1.5"), new BigDecimal("2"), new BigDecimal("2.5"),
            new BigDecimal("3"), new BigDecimal("3.5"), new BigDecimal("4"),
            new BigDecimal("4.5"), new BigDecimal("5"), new BigDecimal("5.5"),
            new BigDecimal("6"), new BigDecimal("6.5"), new BigDecimal("7"),
            new BigDecimal("7.5"), new BigDecimal("8"), new BigDecimal("8.5"),
            new BigDecimal("9"), new BigDecimal("9.5"), new BigDecimal("10"),
            new BigDecimal("10.5"), new BigDecimal("11"), new BigDecimal("11.5"),
            new BigDecimal("12")
    );

    //最长可申请加班时长,0、0.5、1、1.5、2、2.5、3、3.5、4、4.5、5、5.5、6、6.5、7、7.5、8、8.5、9、9.5、10、10.5、11、11.5、12、12.5、13、13.5、14、14.5、15、15.5、16、16.5、17、17.5、18、18.5、19、19.5、20、20.5、21、21.5、22、22.5、23、23.5、24
    private static final List<BigDecimal> VALID_MAX_WORKING_TIME_CAN_APPLY = Arrays.asList(
            new BigDecimal("0"), new BigDecimal("0.5"), new BigDecimal("1"),
            new BigDecimal("1.5"), new BigDecimal("2"), new BigDecimal("2.5"),
            new BigDecimal("3"), new BigDecimal("3.5"), new BigDecimal("4"),
            new BigDecimal("4.5"), new BigDecimal("5"), new BigDecimal("5.5"),
            new BigDecimal("6"), new BigDecimal("6.5"), new BigDecimal("7"),
            new BigDecimal("7.5"), new BigDecimal("8"), new BigDecimal("8.5"),
            new BigDecimal("9"), new BigDecimal("9.5"), new BigDecimal("10"),
            new BigDecimal("10.5"), new BigDecimal("11"), new BigDecimal("11.5"),
            new BigDecimal("12"), new BigDecimal("12.5"), new BigDecimal("13"),
            new BigDecimal("13.5"), new BigDecimal("14"), new BigDecimal("14.5"),
            new BigDecimal("15"), new BigDecimal("15.5"), new BigDecimal("16"),
            new BigDecimal("16.5"), new BigDecimal("17"), new BigDecimal("17.5"),
            new BigDecimal("18"), new BigDecimal("18.5"), new BigDecimal("19"),
            new BigDecimal("19.5"), new BigDecimal("20"), new BigDecimal("20.5"),
            new BigDecimal("21"), new BigDecimal("21.5"), new BigDecimal("22"),
            new BigDecimal("22.5"), new BigDecimal("23"), new BigDecimal("23.5"),
            new BigDecimal("24")
    );

    /**
     * 工作加班开始时间
     */
    private BigDecimal workingOutStartTime;

    /**
     * 工作日最长有效加班时间
     */
    private BigDecimal workingEffectiveTime;

    /**
     * 工作日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String workingSubsidyType;

    //==========休息日加班时长的判定====================

    /**
     * 休息日最长有效加班时间
     *
     */
    private BigDecimal restEffectiveTime;

    /**
     * 休息日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String restSubsidyType;

    //==========节假日加班时长的判定====================

    /**
     * 节假日最长有效加班时间
     *
     */
    private BigDecimal holidayEffectiveTime;

    /**
     * 节假日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String holidaySubsidyType;

    @Builder
    public OverTime(BigDecimal workingOutStartTime, BigDecimal workingEffectiveTime,
                    String workingSubsidyType, BigDecimal restEffectiveTime, String restSubsidyType,
                    BigDecimal holidayEffectiveTime, String holidaySubsidyType) {
        setWorkingOutStartTime(workingOutStartTime);
        setWorkingEffectiveTime(workingEffectiveTime);
        setWorkingSubsidyType(workingSubsidyType);
        setRestEffectiveTime(restEffectiveTime);
        setRestSubsidyType(restSubsidyType);
        setHolidayEffectiveTime(holidayEffectiveTime);
        setHolidaySubsidyType(holidaySubsidyType);
    }


    /**
     * 设置工作加班开始时间
     * @param workingOutStartTime 工作加班开始时间，必须在 VALID_WORKING_TIME 列表中
     * @throws BusinessLogicException 当 workingOutStartTime 为 null 或不在有效范围内时抛出异常
     */
    public void setWorkingOutStartTime(BigDecimal workingOutStartTime) {
        if (null == workingOutStartTime) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "workingOutStartTime can not be null");
        }
        //支持下拉选项:0、0.5、1、1.5、2、2.5、3、3.5、4、4.5、5、5.5、6、6.5、7、7.5、8、8.5、9、9.5、10、10.5、11、11.5、12
        if (!VALID_WORKING_TIME.contains(workingOutStartTime)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "workingOutStartTime=" + workingOutStartTime + " not support");
        }
        this.workingOutStartTime = workingOutStartTime;
    }

    /**
     * 设置工作日最长有效加班时间
     * @param workingEffectiveTime 工作日最长有效加班时间，必须在 VALID_WORKING_TIME 列表中
     * @throws BusinessLogicException 当 workingEffectiveTime 为 null 或不在有效范围内时抛出异常
     */
    public void setWorkingEffectiveTime(BigDecimal workingEffectiveTime) {
        if (null == workingEffectiveTime) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "workingEffectiveTime can not be null");
        }
        if (!VALID_WORKING_TIME.contains(workingEffectiveTime)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "workingEffectiveTime=" + workingEffectiveTime + " not support");
        }
        this.workingEffectiveTime = workingEffectiveTime;
    }

    /**
     * 设置工作日加班补贴方式
     * @param workingSubsidyType 工作日补贴方式，必须不为空且为有效的补贴方式
     * @throws BusinessLogicException 当 workingSubsidyType 为 null 或无效时抛出异常
     */
    public void setWorkingSubsidyType(String workingSubsidyType) {
        if (StringUtils.isEmpty(workingSubsidyType)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "workingSubsidyType can not be null");
        }
        OverTimeSubsidyWayEnum overTimeSubsidyWayEnum = OverTimeSubsidyWayEnum.getInstance(workingSubsidyType);
        if (null == overTimeSubsidyWayEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "workingSubsidyType=" + workingSubsidyType + " not support");
        }
        this.workingSubsidyType = workingSubsidyType;
    }

    /**
     * 设置休息日最长有效加班时间
     * @param restEffectiveTime 休息日最长有效加班时间，必须在 VALID_MAX_WORKING_TIME_CAN_APPLY 列表中
     * @throws BusinessLogicException 当 restEffectiveTime 为 null 或不在有效范围内时抛出异常
     */
    public void setRestEffectiveTime(BigDecimal restEffectiveTime) {
        if (null == restEffectiveTime) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "restEffectiveTime can not be null");
        }
        if (!VALID_MAX_WORKING_TIME_CAN_APPLY.contains(restEffectiveTime)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "restEffectiveTime=" + restEffectiveTime + " not support");
        }
        this.restEffectiveTime = restEffectiveTime;
    }

    /**
     * 设置休息日加班补贴方式
     * @param restSubsidyType 休息日补贴方式，必须不为空且为有效的补贴方式
     * @throws BusinessLogicException 当 restSubsidyType 为 null 或无效时抛出异常
     */
    public void setRestSubsidyType(String restSubsidyType) {
        if (StringUtils.isEmpty(restSubsidyType)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "restSubsidyType can not be null");
        }
        OverTimeSubsidyWayEnum overTimeSubsidyWayEnum = OverTimeSubsidyWayEnum.getInstance(restSubsidyType);
        if (null == overTimeSubsidyWayEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "restSubsidyType=" + restSubsidyType + " not support");
        }
        this.restSubsidyType = restSubsidyType;
    }

    /**
     * 设置节假日最长有效加班时间
     * @param holidayEffectiveTime 节假日最长有效加班时间，必须在 VALID_MAX_WORKING_TIME_CAN_APPLY 列表中
     * @throws BusinessLogicException 当 holidayEffectiveTime 为 null 或不在有效范围内时抛出异常
     */
    public void setHolidayEffectiveTime(BigDecimal holidayEffectiveTime) {
        if (null == holidayEffectiveTime) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "holidayEffectiveTime can not be null");
        }
        if (!VALID_MAX_WORKING_TIME_CAN_APPLY.contains(holidayEffectiveTime)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "holidayEffectiveTime=" + holidayEffectiveTime + " not support");
        }
        this.holidayEffectiveTime = holidayEffectiveTime;
    }

    /**
     * 设置节假日加班补贴方式
     * @param holidaySubsidyType 节假日补贴方式，必须不为空且为有效的补贴方式
     * @throws BusinessLogicException 当 holidaySubsidyType 为 null 或无效时抛出异常
     */
    public void setHolidaySubsidyType(String holidaySubsidyType) {
        if (StringUtils.isEmpty(holidaySubsidyType)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "holidaySubsidyType can not be null");
        }
        OverTimeSubsidyWayEnum overTimeSubsidyWayEnum = OverTimeSubsidyWayEnum.getInstance(holidaySubsidyType);
        if (null == overTimeSubsidyWayEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "holidaySubsidyType=" + holidaySubsidyType + " not support");
        }
        this.holidaySubsidyType = holidaySubsidyType;
    }

    /**
     * 从字符串解析加班配置
     * @param modelStr 加班配置字符串
     * @return 加班配置
     */
    public static OverTime parseFromModelStr(String modelStr) {
        if (StringUtils.isEmpty(modelStr)) {
            return null;
        }
        return JSON.parseObject(modelStr, OverTime.class);
    }
}
