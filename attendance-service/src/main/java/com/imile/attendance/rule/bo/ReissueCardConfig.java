package com.imile.attendance.rule.bo;

import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@Setter
@Getter
public class ReissueCardConfig {


    private Long id;

    private String country;

    private String configNo;

    private String configName;

    private Integer maxRepunchNumber;

    private Integer isCountryLevel;

    private String status;

    private String deptIds;

    private Integer isLatest;

    private Date effectTime;

    private Date expireTime;

    private Long effectTimestamp;

    private Long expireTimestamp;

    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    private Integer isDelete;

    @Builder
    public ReissueCardConfig(Long id, String country, String configNo, String configName,
                             Integer maxRepunchNumber, Integer isCountryLevel, String status, String deptIds, Integer isLatest,
                             DateAndTimeZoneDate effectDateAndTimeZoneDate, DateAndTimeZoneDate expireDateAndTimeZoneDate, Date createDate, String createUserCode,
                             String createUserName, Date lastUpdDate, String lastUpdUserCode,
                             String lastUpdUserName, Integer isDelete) {
        this.id = id;
        setCountry(country);
        this.configNo = configNo;
        setConfigName(configName);
        setMaxRepunchNumber(maxRepunchNumber);
        this.isCountryLevel = isCountryLevel;
        this.status = status;
        this.deptIds = deptIds;
        this.isLatest = isLatest;
        setEffectTime(effectDateAndTimeZoneDate);
        setExpireTime(expireDateAndTimeZoneDate);
        this.createDate = createDate;
        this.createUserCode = createUserCode;
        this.createUserName = createUserName;
        this.lastUpdDate = lastUpdDate;
        this.lastUpdUserCode = lastUpdUserCode;
        this.lastUpdUserName = lastUpdUserName;
        this.isDelete = isDelete;
    }

    public void setCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "country can not be null");
        }
        this.country = country;
    }

    public void setConfigName(String configName) {
        if (StringUtils.isEmpty(configName)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configName can not be null");
        }
        if (StringUtils.length(configName) > 100) {
            //todo
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configName length can not be more than 100");
        }
        this.configName = configName;
    }

    public void setMaxRepunchNumber(Integer maxRepunchNumber) {
        //必填项，输入范围限制0-31的整数
        if (maxRepunchNumber == null) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "maxRepunchNumber can not be null");
        }
        if (maxRepunchNumber < 0 || maxRepunchNumber > 31) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "maxRepunchNumber must be between 0 and 31");
        }
        this.maxRepunchNumber = maxRepunchNumber;
    }

    public void setEffectTime(DateAndTimeZoneDate effectDateAndTimeZoneDate) {
        if (null == effectDateAndTimeZoneDate) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "effectTime can not be null");
        }
        this.effectTime = effectDateAndTimeZoneDate.getTimeZoneDate();
        this.effectTimestamp = effectDateAndTimeZoneDate.getDateTimeStamp();
    }

    public void setExpireTime(DateAndTimeZoneDate expireDateAndTimeZoneDate) {
        if (null == expireDateAndTimeZoneDate) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "expireTime can not be null");
        }
        this.expireTime = expireDateAndTimeZoneDate.getTimeZoneDate();
        this.expireTimestamp = expireDateAndTimeZoneDate.getDateTimeStamp();
    }
}
