package com.imile.attendance.rule.command;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@Data
public class OverTimeConfigAddCommand {

    /**
     * 加班规则名称
     */
    private String configName;

    /**
     * 工作加班开始时间
     */
    private BigDecimal workingOutStartTime;

    /**
     * 工作日最长有效加班时间
     */
    private BigDecimal workingEffectiveTime;

    /**
     * 工作日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String workingSubsidyType;

    /**
     * 休息日最长有效加班时间
     *
     */
    private BigDecimal restEffectiveTime;

    /**
     * 休息日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String restSubsidyType;

    /**
     * 节假日最长有效加班时间
     *
     */
    private BigDecimal holidayEffectiveTime;

    /**
     * 节假日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String holidaySubsidyType;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<Long> deptIds;

    /**
     * 适用用户
     */
    private List<Long> userIds;


    public List<Long> getDeptIds() {
        return Optional.ofNullable(deptIds)
                .map(ids -> ids.stream()
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    public Boolean isCountryLevelRangeFlag(){
        return CollectionUtils.isEmpty(deptIds) &&
                CollectionUtils.isEmpty(userIds);
    }
}
