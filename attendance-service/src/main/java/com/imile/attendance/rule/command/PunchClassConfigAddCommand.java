package com.imile.attendance.rule.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigAddCommand implements Serializable {

    /**
     * 班次ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 班次性质（FIXED_CLASS,MULTIPLE_CLASS）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String classNature;

    /**
     * 班次名称
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String className;

    /**
     * 班次类型
     */
    private Integer classType;

    /**
     * 时段数
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    @Max(value = 3, message = ValidCodeConstant.MAX)
    @Min(value = 1, message = ValidCodeConstant.MIN)
    private Integer itemNum;

    /**
     * 法定工作时长（不包含休息时间）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    @Max(value = 12, message = ValidCodeConstant.MAX)
    @Min(value = 5, message = ValidCodeConstant.MIN)
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private BigDecimal attendanceHours;

    /**
     * 国家
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String country;

    /**
     * 适用部门
     */
    private List<Long> deptIds;

    /**
     * 适用用户
     */
    private List<Long> userIdList;

    /**
     * 自动更新
     */
    private Boolean autoScheduling = Boolean.FALSE;

    /**
     * 自动清空
     */
    private Boolean clearSchedule = Boolean.FALSE;

    /**
     * 班次时段信息
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<PunchClassItemConfigCommand> classItemConfigList;
}
