package com.imile.attendance.punch.query;

import com.imile.common.query.BaseQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchCardRecordQuery extends BaseQuery {

    private String country;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 打卡开始时间
     */
    private Date startDate;
    /**
     * 打卡结束时间
     */
    private Date endDate;
}
