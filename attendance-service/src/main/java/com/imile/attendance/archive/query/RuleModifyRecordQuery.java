package com.imile.attendance.archive.query;

import com.imile.attendance.query.ResourceQuery;
import com.imile.attendance.rule.dto.RuleConfigSelectDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RuleModifyRecordQuery extends ResourceQuery {

    /**
     * 用户ID
     */
    @NotNull(message = "userId cannot be empty")
    private Long userId;

    /**
     * 模块
     */
    @NotNull(message = "module cannot be empty")
    private String module;
}
