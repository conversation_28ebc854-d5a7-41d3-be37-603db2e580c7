package com.imile.attendance.clover.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class DayAttendanceItemDTO {

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 该时段存在的异常(迟到/早退/未打上班卡/下班卡)
     */
    private List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList;

    /**
     * 该时段的最早打卡时间
     */
    private Date itemBeginPunchTime;

    /**
     * 该时段的最晚打卡时间
     */
    private Date itemEndPunchTime;
}
