package com.imile.attendance.clover.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class DayAttendanceAbnormalDTO {

    /**
     * 异常ID
     */
    private Long abnormalId;

    /**
     * 该异常属于哪个时刻的打卡规则的
     */
    private Long punchClassItemId;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 异常类型
     */
    private String abnormalTypeDesc;

    /**
     * 异常状态
     */
    private String abnormalStatus;

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;
}
