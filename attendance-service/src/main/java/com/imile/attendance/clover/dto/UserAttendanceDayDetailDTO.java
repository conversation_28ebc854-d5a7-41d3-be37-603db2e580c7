package com.imile.attendance.clover.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class UserAttendanceDayDetailDTO {

    /**
     * 今日班次名称
     */
    private String punchConfigClassName;

    /**
     * 今日打卡规则类型(前端使用)
     */
    private String punchConfigType;

    /**
     * 今日班次对应时刻的信息(打卡规则对应的固定时间)(H/OFF是没有的)
     */
    private List<DayPunchClassItemInfoDTO> correctDayClassItemInfoDTOList;

    /**
     * 今日班次对应时刻的信息(用户当天真正打卡的时间)
     */
    private List<DayPunchClassItemInfoDTO> actualDayClassItemInfoDTOList;

    /**
     * 今日对应的考勤周期起始时间
     */
    private Date dayAttendanceStartDate;

    /**
     * 今日对应的考勤周期截止时间
     */
    private Date dayAttendanceEndDate;

    /**
     * 周期内的异常个数(未处理)
     */
    private Integer abnormalUntreatedCount;

    /**
     * 当天审批通过的单据信息(请假/外勤单据)
     */
    private List<DayAttendancePassFormDTO> dayAttendancePassFormDTOList;

    /**
     * 用户该天每个时段内的信息(包含异常和正常的打卡时间)(当天是固定/班次/自由打卡规则)
     */
    private List<DayAttendanceItemDTO> dayAttendanceItemDTOList;

    /**
     * 是否有考勤
     */
    private Integer hasAttendance;
}
