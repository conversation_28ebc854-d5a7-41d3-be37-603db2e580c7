package com.imile.attendance.clover.bo;

import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 封装构建日详情所需的所有数据
 *
 * <AUTHOR> chen
 * @since 2025/1/20
 */
@Data
@AllArgsConstructor
public class DayDetailData {
    
    /**
     * 打卡记录列表（3天范围内的所有打卡记录）
     */
    private List<EmployeePunchRecordDO> punchRecords;
    
    /**
     * 用户排班配置列表（3天范围内的排班配置）
     */
    private List<UserShiftConfigDO> userShiftConfigs;
    
    /**
     * 当天的排班配置
     */
    private UserShiftConfigDO userDayShiftConfig;
    
    /**
     * 打卡规则配置
     */
    private PunchConfigDO punchConfig;
    
    /**
     * 班次配置列表
     */
    private List<PunchClassItemConfigDO> classItemConfigs;
    
    /**
     * 异常考勤列表
     */
    private List<EmployeeAbnormalAttendanceDO> abnormalAttendances;
    
    /**
     * 考勤详情列表
     */
    private List<AttendanceEmployeeDetailDO> attendanceDetails;
    
    /**
     * 审批通过的表单列表
     */
    private List<AttendanceFormDO> passedForms;
}
