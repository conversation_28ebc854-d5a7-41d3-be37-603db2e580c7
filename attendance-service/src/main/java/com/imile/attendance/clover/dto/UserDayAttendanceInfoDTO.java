package com.imile.attendance.clover.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class UserDayAttendanceInfoDTO {

    /**
     * 是否出勤
     */
    private Integer isAttendance;

    /**
     * 具体类型  AL/OFF/P/A/PH
     */
    private String concreteType;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 假期阶段
     */
    private Integer stage;

    /**
     * 请假类型-百分比日薪
     */
    private BigDecimal leavePercentSalary;

    /**
     * 请假时长(分钟)
     */
    private BigDecimal leaveMinutes;

    /**
     * 出勤时长(分钟)
     */
    private BigDecimal attendanceMinutes;

    /**
     * 加班时长(分钟)
     */
    private BigDecimal overtimeMinutes;

    /**
     * 分钟 无值？
     */
    private BigDecimal concreteTypeMinutes;
}
