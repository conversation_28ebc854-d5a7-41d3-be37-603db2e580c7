package com.imile.attendance.clover;

import com.imile.attendance.clover.dto.UserAttendanceCalendarDTO;
import com.imile.attendance.clover.dto.UserAttendanceCycleDetailDTO;
import com.imile.attendance.clover.dto.UserAttendanceDayDetailDTO;
import com.imile.attendance.clover.dto.UserAttendanceGenerateCheckDTO;
import com.imile.attendance.clover.query.UserAttendanceCalendarQuery;
import com.imile.attendance.clover.query.UserAttendanceCycleDetailQuery;
import com.imile.attendance.clover.query.UserAttendanceDayDetailQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description clover考勤服务
 */
public interface CloverAttendanceService {


    /**
     * 获取用户考勤周期内信息
     */
    UserAttendanceCycleDetailDTO getUserAttendanceCycleDetail(UserAttendanceCycleDetailQuery cycleDetailQuery);

    /**
     * 用户考勤日历展示
     */
    UserAttendanceCalendarDTO getUserAttendanceCalendar(UserAttendanceCalendarQuery calendarQuery);

    /**
     * 用户每日日历详情
     */
    UserAttendanceDayDetailDTO getUserAttendanceDayDetail(UserAttendanceDayDetailQuery dayDetailQuery);

    /**
     * 获取用户考勤未计算的日期
     */
    UserAttendanceGenerateCheckDTO getUserAttendanceGenerateCheck();



}
