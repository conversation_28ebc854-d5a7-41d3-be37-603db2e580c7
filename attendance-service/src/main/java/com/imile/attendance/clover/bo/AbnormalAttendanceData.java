package com.imile.attendance.clover.bo;

import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/26 
 * @Description
 */
@Data
@AllArgsConstructor
public class AbnormalAttendanceData {

    private final List<EmployeeAbnormalAttendanceDO> abnormalAttendances;
    private final List<AttendanceFormRelationDO> formRelations;
    private final List<AttendanceFormDO> inReviewForms;

}
