package com.imile.attendance.clover.bo;

import com.imile.attendance.util.DateHelper;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * 日详情时间范围数据
 *
 * 封装日详情查询所需的时间相关数据，包括当天及前后一天的日期信息，
 * 以及3天时间范围的开始和结束时间
 *
 * <AUTHOR> chen
 * @since 2025/1/20
 */
@Data
@AllArgsConstructor
public class DayDetailTimeRange {
    
    /**
     * 当天日期
     */
    private Date dayDate;
    
    /**
     * 前一天的dayId
     */
    private Long beforeDayId;
    
    /**
     * 后一天的dayId
     */
    private Long afterDayId;
    
    /**
     * 3天时间范围的开始日期（前一天的开始时间）
     */
    private Date startDate;
    
    /**
     * 3天时间范围的结束日期（后一天的结束时间）
     */
    private Date endDate;
    
    /**
     * 获取当天的dayId
     */
    public Long getDayId() {
        return DateHelper.getDayId(dayDate);
    }
}
