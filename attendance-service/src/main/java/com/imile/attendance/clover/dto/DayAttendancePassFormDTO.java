package com.imile.attendance.clover.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class DayAttendancePassFormDTO {

    /**
     * 申请单据ID
     */
    private Long formId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据类型
     */
    private String formTypeDesc;

    /**
     * 审批单号ID(审批中心的ID)
     */
    private Long approvalId;

    /**
     * 请假/外勤开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 请假/外勤结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;
}
