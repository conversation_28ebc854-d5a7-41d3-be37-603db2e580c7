package com.imile.attendance.cycleConfig.job;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.form.UserCycleReissueCardCountManage;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户考勤周期补卡次数配置定时任务处理器
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>为指定用户或国家的在职非司机员工初始化考勤周期补卡次数配置</li>
 *   <li>根据用户的考勤周期配置，为每个周期创建对应的补卡次数记录</li>
 *   <li>避免重复创建同一周期的补卡次数配置</li>
 * </ul>
 *
 * <p>业务场景：</p>
 * <ul>
 *   <li>新员工入职时需要初始化补卡次数配置</li>
 *   <li>定期检查并补充缺失的周期补卡次数配置</li>
 *   <li>支持按用户编码或国家范围进行批量处理</li>
 * </ul>
 *
 * <p>执行逻辑：</p>
 * <ol>
 *   <li>解析任务参数，获取目标用户列表和初始化日期</li>
 *   <li>查询用户的考勤周期配置</li>
 *   <li>检查是否已存在当前周期的补卡次数配置</li>
 *   <li>为缺失配置的用户创建新的补卡次数记录</li>
 * </ol>
 *
 * <AUTHOR> chen
 * @date 2025/5/25
 * @see UserCycleReissueCardCountDO 用户周期补卡次数实体
 * @see AttendanceCycleConfigService 考勤周期配置服务
 */
@Slf4j
@Component
public class UserReissueCardConfigHandler {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserCycleReissueCardCountManage userCycleReissueCardCountManage;
    @Resource
    private AttendanceCycleConfigService cycleConfigService;
    @Resource
    private DefaultIdWorker defaultIdWorker;


    /**
     * 用户考勤周期补卡次数配置定时任务主方法
     *
     *
     * @param content 任务参数JSON字符串，包含以下可选参数：
     *                <ul>
     *                  <li>initDayId: 初始化日期ID（格式：yyyyMMdd），默认为当前日期</li>
     *                  <li>userCodeList: 用户编码列表，逗号分隔，指定特定用户</li>
     *                  <li>countryList: 国家列表，逗号分隔，处理指定国家的所有在职非司机用户</li>
     *                </ul>
     * @return ReturnT<String> XXL-Job执行结果，SUCCESS表示执行成功
     */
    @XxlJob(BusinessConstant.JobHandler.USER_REISSUE_CARD_CONFIG_HANDLER)
    public ReturnT<String> userReissueCardConfigHandler(String content) {
        var param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, UserReissueCardConfigHandler.UserCardConfigHandlerParam.class) :
                new UserReissueCardConfigHandler.UserCardConfigHandlerParam();

        // 设置默认初始化日期为当前日期
        if (param.getInitDayId() == null) {
            param.setInitDayId(DateHelper.getDayId(new Date()));
        }

        // 根据参数获取目标用户列表
        List<AttendanceUser> userList;
        if (StringUtils.isNotBlank(param.getUserCodeList())) {
            // 按用户编码查询
            List<String> userCodeList = Arrays.asList(param.getUserCodeList().split(","));
            userList = userService.listOnJobNonDriverUserByCodes(userCodeList);
        } else {
            // 按国家查询：验证国家参数不能为空
            if (StringUtils.isEmpty(param.getCountryList())) {
                XxlJobLogger.log("公司不能为空,content:{}", content);
                return ReturnT.SUCCESS;
            }

            // 解析逗号分隔的国家列表，查询指定国家的在职非司机用户
            List<String> countryList = Arrays.asList(param.getCountryList().split(","));
            userList = userService.listOnJobNonDriverUserByCountries(countryList);
        }

        // 用户列表为空,直接返回
        if (CollectionUtils.isEmpty(userList)) {
            XxlJobLogger.log("用户列表为空,不处理");
            return ReturnT.SUCCESS;
        }

        List<Long> userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        // 查询所有用户的现有周期补卡次数记录，按周期开始时间排序，按用户ID分组
        Map<Long, List<UserCycleReissueCardCountDO>> existUserCardConfigMap = userCycleReissueCardCountManage.selectByUserIdList(userIdList)
                .stream()
                .sorted(Comparator.comparing(UserCycleReissueCardCountDO::getCycleStartDate))
                .collect(Collectors.groupingBy(UserCycleReissueCardCountDO::getUserId));

        // 存储需要新增的补卡配置记录
        List<UserCycleReissueCardCountDO> addUserCardConfigDOList = new ArrayList<>();

        // 按国家分组用户，同一国家的用户共享相同的考勤周期配置，避免重复查询
        Map<String, List<AttendanceUser>> usersByCountryMap = userList.stream()
                .collect(Collectors.groupingBy(user ->
                        StringUtils.isNotBlank(user.getLocationCountry()) ? user.getLocationCountry() : "UNKNOWN"));

        // 国家级别的配置缓存，避免重复查询相同国家的配置
        Map<String, AttendanceCycleConfigDO> countryConfigCache = new HashMap<>();
        Map<String, AttendanceDayCycleDTO> countryCycleCache = new HashMap<>();

        // 按国家组处理用户
        for (Map.Entry<String, List<AttendanceUser>> countryEntry : usersByCountryMap.entrySet()) {
            String country = countryEntry.getKey();
            List<AttendanceUser> countryUsers = countryEntry.getValue();

            // 处理国家为空或未知的用户
            if ("UNKNOWN".equals(country)) {
                XxlJobLogger.log("发现{}个用户的常驻国为空，将逐个处理", countryUsers.size());
                // 对于国家为空的用户，仍需要逐个查询配置
                processUsersWithIndividualConfig(countryUsers, param, existUserCardConfigMap, addUserCardConfigDOList);
                continue;
            }

            // 为当前国家获取考勤周期配置（每个国家只查询一次）
            AttendanceCycleConfigDO countryConfigDO = getCountryConfig(country, countryConfigCache);
            if (countryConfigDO == null) {
                XxlJobLogger.log("国家{}的考勤周期配置获取失败，跳过该国家{}个用户的处理", country, countryUsers.size());
                continue;
            }

            // 为当前国家计算周期起止时间（每个国家只计算一次）
            AttendanceDayCycleDTO countryCycleDTO = getCountryCycle(country, param.getInitDayId(), countryConfigDO, countryCycleCache);
            if (countryCycleDTO == null) {
                XxlJobLogger.log("国家{}的考勤周期计算失败，跳过该国家{}个用户的处理", country, countryUsers.size());
                continue;
            }

            XxlJobLogger.log("开始处理国家{}的{}个用户，周期时间：{} - {}",
                    country, countryUsers.size(),
                    DateHelper.formatPureDate(countryCycleDTO.getAttendanceStartDate()),
                    DateHelper.formatPureDate(countryCycleDTO.getAttendanceEndDate()));

            // 处理该国家的所有用户
            processCountryUsers(countryUsers, countryCycleDTO, existUserCardConfigMap, addUserCardConfigDOList);
        }

        // 批量保存新创建的补卡配置记录
        userCycleReissueCardCountManage.batchSave(addUserCardConfigDOList);

        XxlJobLogger.log("用户考勤周期补卡次数配置处理完成，共处理用户{}个，新增配置{}条",
                userList.size(), addUserCardConfigDOList.size());

        return ReturnT.SUCCESS;
    }

    /**
     * 获取国家的考勤周期配置（带缓存）
     *
     * @param country 国家代码
     * @param configCache 配置缓存
     * @return 考勤周期配置，如果获取失败返回null
     */
    private AttendanceCycleConfigDO getCountryConfig(String country, Map<String, AttendanceCycleConfigDO> configCache) {
        // 先从缓存中获取
        if (configCache.containsKey(country)) {
            return configCache.get(country);
        }

        try {
            // 直接调用基于国家的配置获取方法，避免通过用户ID查询
            AttendanceCycleConfigDO cycleConfigDO = cycleConfigService.getByCountryAndCycleType(
                    country, AttendanceCycleTypeEnum.MONTH.getType());

            // 将结果放入缓存
            configCache.put(country, cycleConfigDO);

            XxlJobLogger.log("成功获取国家{}的考勤周期配置:{}", country, cycleConfigDO);
            return cycleConfigDO;
        } catch (Exception e) {
            XxlJobLogger.log("获取国家{}的考勤周期配置失败", country, e.getMessage());
            // 缓存失败结果，避免重复尝试
            configCache.put(country, null);
            return null;
        }
    }

    /**
     * 获取国家的考勤周期起止时间（带缓存）
     *
     * @param country 国家代码
     * @param initDayId 初始化日期ID
     * @param configDO 考勤周期配置
     * @param cycleCache 周期缓存
     * @return 考勤周期起止时间，如果计算失败返回null
     */
    private AttendanceDayCycleDTO getCountryCycle(String country, Long initDayId,
                                                  AttendanceCycleConfigDO configDO,
                                                  Map<String, AttendanceDayCycleDTO> cycleCache) {
        // 构建缓存键，包含国家和日期信息
        String cacheKey = country + "_" + initDayId;

        // 先从缓存中获取
        if (cycleCache.containsKey(cacheKey)) {
            return cycleCache.get(cacheKey);
        }

        try {
            // 计算周期起止时间
            AttendanceDayCycleDTO cycleDTO = cycleConfigService.getUserAttendanceCycleConfigDay(initDayId, configDO);

            // 将结果放入缓存
            cycleCache.put(cacheKey, cycleDTO);

            if (cycleDTO != null) {
                XxlJobLogger.log("计算国家{}的考勤周期时间：{} - {}", country,
                        DateHelper.formatPureDate(cycleDTO.getAttendanceStartDate()),
                        DateHelper.formatPureDate(cycleDTO.getAttendanceEndDate()));
            }

            return cycleDTO;
        } catch (Exception e) {
            log.error("计算国家{}的考勤周期时间失败", country, e);
            // 缓存失败结果，避免重复尝试
            cycleCache.put(cacheKey, null);
            return null;
        }
    }

    /**
     * 处理同一国家的所有用户
     *
     * @param countryUsers 该国家的用户列表
     * @param countryCycleDTO 该国家的考勤周期信息
     * @param existUserCardConfigMap 现有用户补卡配置映射
     * @param addUserCardConfigDOList 待新增的补卡配置列表
     */
    private void processCountryUsers(List<AttendanceUser> countryUsers,
                                     AttendanceDayCycleDTO countryCycleDTO,
                                     Map<Long, List<UserCycleReissueCardCountDO>> existUserCardConfigMap,
                                     List<UserCycleReissueCardCountDO> addUserCardConfigDOList) {
        for (AttendanceUser user : countryUsers) {
            Long userId = user.getId();

            // 获取用户现有的补卡配置记录
            List<UserCycleReissueCardCountDO> userCardList = existUserCardConfigMap.getOrDefault(userId, Collections.emptyList());

            // 新入职用户或首次处理的用户，直接创建补卡配置
            if (CollectionUtils.isEmpty(userCardList)) {
                addCardConfigBuild(userId, countryCycleDTO, addUserCardConfigDOList);
                continue;
            }

            // 检查当前周期是否已存在补卡配置
            // 通过比较周期开始日期判断是否为同一周期
            List<UserCycleReissueCardCountDO> existUserCardConfigList = userCardList.stream()
                    .filter(item -> DateHelper.formatPureDate(item.getCycleStartDate())
                            .equals(DateHelper.formatPureDate(countryCycleDTO.getAttendanceStartDate())))
                    .collect(Collectors.toList());

            // 如果当前周期已存在配置，跳过处理
            if (CollectionUtils.isNotEmpty(existUserCardConfigList)) {
                continue;
            }

            // 当前周期不存在配置，创建新的补卡配置记录
            addCardConfigBuild(userId, countryCycleDTO, addUserCardConfigDOList);
        }
    }

    /**
     * 处理常驻国为空的用户（逐个查询配置）
     *
     * @param users 用户列表
     * @param param 任务参数
     * @param existUserCardConfigMap 现有用户补卡配置映射
     * @param addUserCardConfigDOList 待新增的补卡配置列表
     */
    private void processUsersWithIndividualConfig(List<AttendanceUser> users,
                                                  UserCardConfigHandlerParam param,
                                                  Map<Long, List<UserCycleReissueCardCountDO>> existUserCardConfigMap,
                                                  List<UserCycleReissueCardCountDO> addUserCardConfigDOList) {
        for (AttendanceUser user : users) {
            Long userId = user.getId();

            try {
                // 获取用户的考勤周期配置
                AttendanceCycleConfigDO userCycleConfigDO = cycleConfigService.getUserAttendanceCycleConfigUserCard(userId);

                // 根据初始化日期和用户周期配置，计算当前周期的起止时间
                AttendanceDayCycleDTO userDayCycleDTO = cycleConfigService.getUserAttendanceCycleConfigDay(
                        param.getInitDayId(), userCycleConfigDO);

                // 如果用户未配置考勤周期，跳过处理
                if (userDayCycleDTO == null) {
                    log.info("用户：{} 未配置考勤周期,不处理", user.getUserCode());
                    continue;
                }

                // 获取用户现有的补卡配置记录
                List<UserCycleReissueCardCountDO> userCardList = existUserCardConfigMap.get(userId);

                // 新入职用户或首次处理的用户，直接创建补卡配置
                if (CollectionUtils.isEmpty(userCardList)) {
                    addCardConfigBuild(userId, userDayCycleDTO, addUserCardConfigDOList);
                    continue;
                }

                // 检查当前周期是否已存在补卡配置
                // 通过比较周期开始日期判断是否为同一周期
                List<UserCycleReissueCardCountDO> existUserCardConfigList = userCardList.stream()
                        .filter(item -> DateHelper.formatPureDate(item.getCycleStartDate())
                                .equals(DateHelper.formatPureDate(userDayCycleDTO.getAttendanceStartDate())))
                        .collect(Collectors.toList());

                // 如果当前周期已存在配置，跳过处理
                if (CollectionUtils.isNotEmpty(existUserCardConfigList)) {
                    continue;
                }

                // 当前周期不存在配置，创建新的补卡配置记录
                addCardConfigBuild(userId, userDayCycleDTO, addUserCardConfigDOList);
            } catch (Exception e) {
                log.error("处理用户{}的补卡配置时发生异常", user.getUserCode(), e);
            }
        }
    }

    /**
     * 构建用户周期补卡配置记录
     *
     * @param userId 用户ID
     * @param userDayCycleDTO 用户考勤周期信息，包含周期起止时间
     * @param addUserCardConfigDOList 待新增的补卡配置记录列表，新创建的记录将添加到此列表中
     */
    private void addCardConfigBuild(Long userId,
                                    AttendanceDayCycleDTO userDayCycleDTO,
                                    List<UserCycleReissueCardCountDO> addUserCardConfigDOList) {
        UserCycleReissueCardCountDO reissueCardCountDO = new UserCycleReissueCardCountDO();
        reissueCardCountDO.setId(defaultIdWorker.nextId());
        reissueCardCountDO.setUserId(userId);
        reissueCardCountDO.setUsedReissueCardCount(BusinessConstant.ZERO);
        reissueCardCountDO.setCycleStartDate(userDayCycleDTO.getAttendanceStartDate());
        // 设置周期结束时间（减去999毫秒，避免时间精度问题）
        reissueCardCountDO.setCycleEndDate(DateUtil.offset(userDayCycleDTO.getAttendanceEndDate(), DateField.MILLISECOND, -999));
        BaseDOUtil.fillDOUpdateByUserOrSystem(reissueCardCountDO);
        addUserCardConfigDOList.add(reissueCardCountDO);
    }


    /**
     * 用户补卡配置定时任务参数类
     *
     * <p>用于接收和解析定时任务的JSON参数，支持以下两种执行模式：</p>
     * <ul>
     *   <li>按用户编码执行：指定userCodeList，处理特定用户</li>
     *   <li>按国家执行：指定countryList，处理指定国家的所有在职非司机用户</li>
     * </ul>
     */
    @Data
    private static class UserCardConfigHandlerParam {

        /**
         * 初始化日期ID
         *
         * <p>格式：yyyyMMdd（如：20250525）</p>
         * <p>用于确定要处理的考勤周期，系统会根据此日期计算对应的考勤周期起止时间。</p>
         * <p>如果不指定，默认使用当前日期。</p>
         */
        private Long initDayId;

        /**
         * 国家列表
         *
         * <p>格式：逗号分隔的国家代码（如："CN,US,UK"）</p>
         * <p>当不指定userCodeList时，系统会处理这些国家下的所有在职非司机用户。</p>
         * <p>此参数与userCodeList互斥，优先使用userCodeList。</p>
         */
        private String countryList;

        /**
         * 用户编码列表
         *
         * <p>格式：逗号分隔的用户编码（如："EMP001,EMP002,EMP003"）</p>
         * <p>指定要处理的具体用户，适用于针对特定用户的补卡配置初始化。</p>
         * <p>此参数优先级高于countryList。</p>
         */
        private String userCodeList;
    }
}
