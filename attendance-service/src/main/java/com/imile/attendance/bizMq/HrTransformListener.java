package com.imile.attendance.bizMq;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.bizMq.constant.UserInfoFieldEnum;
import com.imile.attendance.bizMq.mapstruct.UserInfoMapstruct;
import com.imile.attendance.bizMq.sync.SyncHrToAttendanceTableService;
import com.imile.attendance.calendar.CalendarConfigService;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.infrastructure.mq.helper.OperatorHelper;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.RuleConfigRangeService;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.shift.dto.EmployeeSchedulingHandlerDTO;
import com.imile.attendance.third.ThirdZktecoService;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.vacation.DispatchUserRecordService;
import com.imile.hrms.api.base.component.DifferField;
import com.imile.hrms.api.user.param.UserEventParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/5/24
 * @Description 监听用户派遣消息通知
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.topic}",
        consumerGroup = "${rocket.mq.hr.transform.group}",
        selectorExpression = "${rocket.mq.hr.transform.tag}",
        consumeThreadMax = 4)
public class HrTransformListener extends BaseRocketMQListener {

    @Resource
    private DispatchUserRecordService dispatchUserRecordService;

    @Override
    public void doOnMessage(MessageExt messageExt) {
        UserEventParam<?> param = JSON.parseObject(new String(messageExt.getBody()), UserEventParam.class);
        log.info("收到派遣人员变动消息,msgId:{},topic:{},tags:{},param:{}",
                messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));

        UserEventParam.TransformNotice transform = JSON.parseObject(JSON.toJSONString(param.getBody()), UserEventParam.TransformNotice.class);
        dispatchUserRecordService.handleDispatchNotice(transform);
    }
}
