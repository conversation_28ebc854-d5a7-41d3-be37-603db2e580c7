package com.imile.attendance.driver.dto;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceOperateRecordParam
 * {@code @since:} 2024-01-22 13:56
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description="司机打卡记录详情入参")
public class DriverAttendanceOperateRecordParam extends ResourceQuery {
    /**
     * 国家
     */
    @NotEmpty(message = ValidCodeConstant.NOT_BLANK)
    private String country;
    /**
     * 查询起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date startTime;


    /**
     * 查询结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date endTime;
}
