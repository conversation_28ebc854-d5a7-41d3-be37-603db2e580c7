package com.imile.attendance.driver.vo;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.OutWithTimeZone;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.annon.WithDictSeparator;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.DriverAttendanceTypeEnum;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceHolidayDTO;
import com.imile.common.enums.StatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailMonthVO
 * {@code @since:} 2024-01-24 20:54
 * {@code @description:}
 */
@Data
public class DriverAttendanceDetailMonthVO implements Serializable {

    private static final long serialVersionUID = -1844432964949719499L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID
     */
    private Long userId;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 英文名
     */
    private String userNameEn;

    /**
     * 工作状态 在职、离职等
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    private String workStatus;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatusDesc;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;


    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private Integer attendanceType;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private String attendanceTypeString;

    /**
     * 轨迹打卡次数
     */
    private Long locusNumber;

    /**
     * 司机签收次数
     */
    private Long dldNumber;

    /**
     * 所属国
     */
    private String originCountry;

    /**
     * 常驻国家
     */
    private String locationCountry;

    /**
     * 供应商编码
     */
    private String vendorCode;
    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 网点code
     */
    private String ocCode;

    /**
     * 网点类型
     */
    @WithDictSeparator(typeCode = BusinessConstant.SysDictDataTypeConstant.OC_TYPE, tSeparator = "|", ref = "ocTypeNames")
    private String ocType;

    /**
     * 网点类型名称
     */
    private String ocTypeNames;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 工作岗位id
     */
    private Long postId;

    /**
     * 工作岗位名称
     */
    private String postName;

    /**
     * 汇报上级编码
     */
    private Long leaderId;

    /**
     * 汇报上级名称
     */
    private String leaderName;


    /**
     * 员工性质
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 员工性质名称
     */
    private String employeeTypeDesc;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualDimissionDate;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间
     */
    @OutWithTimeZone
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastOperatingTime;

    /**
     * 最近操作时间：取司机打卡记录表某人最近的操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date localLastOperatingTime;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;

    /**
     * 申请单id
     */
    private Long formId;

    /**
     * 考勤范围
     */
    private String attendanceRange;

    /**
     * 应出勤天数
     */
    private Long attendanceDays;

    /**
     * 实际出勤天数
     */
    private Long actualAttendanceDays;

    /**
     * 实际缺勤天数
     */
    private Long actualAbsentDays;

    /**
     * 出勤率
     */
    private String attendanceRate;

    /**
     * 国家下面假期类型，动态数据返回
     */
    private List<DriverAttendanceHolidayDTO> holidayTypeList;

    public String getAttendanceTypeString() {
        if (ObjectUtil.isNull(DriverAttendanceTypeEnum.getByType(this.attendanceType))) {
            return "";
        }
        return DriverAttendanceTypeEnum.getByType(this.attendanceType).getDescEn();
    }

    public String getStatusDesc() {
        if (ObjectUtil.isNull(StatusEnum.getStatusEnum(this.status))) {
            return "";
        }
        return RequestInfoHolder.isChinese() ?
                StatusEnum.getStatusEnum(this.status).getValue() : StatusEnum.getStatusEnum(this.status).getCode();
    }
}
