package com.imile.attendance.user.dto;

import lombok.Data;

import java.util.List;

@Data
public class UserLeaveDetailDTO {

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 假期规则配置id
     */
    private Long configId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 是否派遣假
     */
    private Integer isDispatch;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 是否永久有效
     */
    private Integer isInvalid;

    /**
     * 假期更新周期
     */
    private String useCycle;

    /**
     * 假期可用的开始时间
     */
    private String useStartDate;

    /**
     * 假期可用的结束时间
     */
    private String useEndDate;

    /**
     * 使用状态 (是否可用: 1可用, 0不可用)
     */
    private Integer availability;

    /**
     * 使用条件 (是否可用: 1入职后, 2转正后, 3入职一年后, 4入职两年后)
     */
    private Integer useCondition;

    /**
     * 用户假期阶段列表
     */
    List<UserLeaveStageDetailDTO> userLeaveStageList;
}
