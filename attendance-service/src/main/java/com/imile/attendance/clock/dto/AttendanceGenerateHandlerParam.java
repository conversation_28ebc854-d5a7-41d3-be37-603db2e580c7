package com.imile.attendance.clock.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/26 
 * @Description
 */
@Data
public class AttendanceGenerateHandlerParam {

    /**
     * 入参:国家
     */
    private String countryList;

    /**
     * 入参:需要计算的考勤天
     */
    private Long attendanceDayId;

    /**
     * 入参:用户编码   考勤审批通过后会通过这个字段来
     */
    private String userCodes;

    /**
     * 界面无法输入，开始时间  yyyy-MM-dd HH:mm:ss  周期的开始时间
     */
    private Date startTime;

    /**
     * 界面无法输入，结束时间  yyyy-MM-dd HH:mm:ss  周期的结束时间
     */
    private Date endTime;

    /**
     * 界面无法输入，开始时间  yyyyMMdd  周期的开始时间的long
     */
    private Long startDayId;

    /**
     * 界面无法输入，结束时间  yyyyMMdd  周期的结束时间的long
     */
    private Long endDayId;

    /**
     * 界面无法输入，考勤时间，当天的结束时间  yyyy-MM-dd HH:mm:ss
     */
    private Date attendanceTime;

    /**
     * 界面无法输入，改天考勤真正的起始时间 yyyy-MM-dd HH:mm:ss
     */
    private Date actualAttendanceStartTime;

    /**
     * 界面无法输入，改天考勤真正的结束时间  yyyy-MM-dd HH:mm:ss
     */
    private Date actualAttendanceEndTime;

    /**
     * 界面无法输入，当天的考勤真正开始/结束时间是否存在交集
     */
    private Integer isIntersection;
}
