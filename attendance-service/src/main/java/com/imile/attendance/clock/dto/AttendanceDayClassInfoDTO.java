package com.imile.attendance.clock.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 考勤日和班次信息DTO
 * 用于替代 getAttendanceDayAndClassId 方法中的 Map<String, Object> 返回类型
 * 包含考勤日ID、班次ID和班次详情ID
 *
 * <AUTHOR> chen
 * @Date 2025/5/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDayClassInfoDTO {

    /**
     * 考勤日ID
     */
    private Long attendanceDayId;

    /**
     * 班次ID
     */
    private Long punchClassId;

    /**
     * 班次详情ID
     */
    private Long punchClassItemId;

    /**
     * 判断对象是否为空（所有字段都为null）
     * 用于替代原来的 Map.isEmpty() 调用
     *
     * @return 如果所有字段都为null，返回true；否则返回false
     */
    public boolean isEmpty() {
        return attendanceDayId == null && punchClassId == null && punchClassItemId == null;
    }
}
