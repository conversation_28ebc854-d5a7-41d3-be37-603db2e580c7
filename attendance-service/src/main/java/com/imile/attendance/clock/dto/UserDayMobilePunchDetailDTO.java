package com.imile.attendance.clock.dto;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDayMobilePunchDetailDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户常驻国
     */
    private String locationCountry;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 员工手机打卡等规则配置
     */
    private UserMobileRuleConfigDTO ruleConfigDTO;

    /**
     * 班次配置
     */
    private PunchClassConfigDTO punchClassConfigDTO;

    /**
     * 班次详情配置
     */
    private List<PunchClassItemConfigDTO> punchClassItemConfigDTO;

    /**
     * 打卡记录
     */
    private List<MobilePunchCardRecordDTO> punchCardRecordDTO;

    /**
     * 请假/外勤记录
     */
    private List<MobileFormDTO> mobileFormDTOList;

    /**
     * 考勤异常类型(枚举值)
     */
    private List<MobileAbnormalDTO> abnormalPunchList;
}
