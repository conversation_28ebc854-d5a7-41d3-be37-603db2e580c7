package com.imile.attendance.clock.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.clock.dto.AttendanceGenerateHandlerParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/26 
 * @Description 用户未打卡人员每日考勤生成，每一个小时执行一次
 */
@Slf4j
@Component
public class AttendanceDayGenerateHandler {

    @Resource
    private CountryService countryService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;


    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_DAY_GENERATE_HANDLER)
    public ReturnT<String> attendanceDayGenerateHandler(String param) {

        AttendanceGenerateHandlerParam handlerParam = StringUtils.isNotBlank(param) ?
                JSON.parseObject(param, AttendanceGenerateHandlerParam.class) :
                new AttendanceGenerateHandlerParam();
        String countryList = handlerParam.getCountryList();
        if (StringUtils.isBlank(countryList)) {
            XxlJobLogger.log("国家不存在,不处理");
            return ReturnT.FAIL;
        }
        // 处理未打卡人员每日考勤
        List<String> countries = Arrays.asList(countryList.split(","));
        // 获取对应国家的当前时间
        Map<String, Date> countryCurrentDateMap = countryService.getCountryCurrentDate(countries);
        XxlJobLogger.log("getCountryCurrentDate: 国家对应的当天时间为:{}", countryCurrentDateMap);

        for (String country : countries) {
            Long attendanceDayId = handlerParam.getAttendanceDayId();
            Date countryCurrentDate = countryCurrentDateMap.get(country);
            if (Objects.isNull(countryCurrentDate)) {
                XxlJobLogger.log("未获取到当前国家对应时间:{}", country);
                continue;
            }
            if (Objects.isNull(attendanceDayId) || attendanceDayId <= 0) {
                attendanceDayId = DateHelper.getDayId(countryCurrentDate);
                XxlJobLogger.log("当前国家:{} 考勤日:{}", country, attendanceDayId);
            }
            log.info("当前国家: {}, date:{}", country, DateHelper.formatYYYYMMDDHHMMSS(countryCurrentDate));
            Long preDayId = DateHelper.getPreviousDayId(attendanceDayId);

            UserDaoQuery userQuery = UserDaoQuery.builder()
                    .locationCountry(country)
                    .isDriver(BusinessConstant.N)
                    .build();
            if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
                List<String> userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
                userQuery.setUserCodes(userCodeList);
            }
            int currentPage = 1;
            int pageSize = 5000;
            Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
            PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
            // 总记录数
            List<UserInfoDO> pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                log.info("country: {},pageUserInfoList size:{}，pageUserInfoList：{}", country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                generateAttendance(attendanceDayId, preDayId, pageUserInfoList, countryCurrentDate, country);
            }
            log.info("country：{},pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", country, currentPage, pageSize, pageInfo.getTotal());

            while (currentPage < pageInfo.getPages()) {
                log.info("country：{},进入while循环", country);
                currentPage++;
                log.info("country：{},currentPage：{}，pages：{}", country, currentPage, pageInfo.getPages());
                page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
                pageUserInfoList = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                    log.info("country：{},while循环：pageUserInfoList size:{}，pageUserInfoList：{}",
                            country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                    generateAttendance(attendanceDayId, preDayId, pageUserInfoList, countryCurrentDate, country);
                }
                log.info("country：{},while循环：pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", country, currentPage, pageSize, pageInfo.getTotal());
                log.info("country：{},currentPage {}，while循环结束", country, currentPage);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void generateAttendance(Long attendanceDayId,
                                    Long preDayId,
                                    List<UserInfoDO> userInfoDOList,
                                    Date countryCurrentDate,
                                    String country) {

        List<String> userCodes = userInfoDOList.stream()
                .map(UserInfoDO::getUserCode)
                .collect(Collectors.toList());
        List<Long> userIds = userInfoDOList.stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());
        List<Long> dayIds = Arrays.asList(preDayId, attendanceDayId);

        List<EmployeePunchRecordDO> userPunchRecords = employeePunchRecordManage.getUsersPunchRecordsInTimeRange(userCodes,
                dayIds.stream().map(String::valueOf).collect(Collectors.toList()));

    }

}
