package com.imile.attendance.clock;

import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.clock.command.PunchInAddCommand;
import com.imile.attendance.clock.command.PunchInEncryptAddCommand;
import com.imile.attendance.clock.dto.UserDayMobilePunchDetailDTO;
import com.imile.attendance.clock.external.ClockExternalService;
import com.imile.attendance.clock.query.MobileDayPunchDetailQuery;
import com.imile.attendance.clock.query.MobilePunchDetailQuery;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.deviceConfig.application.AttendanceMobileConfigApplicationService;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigAddCommand;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.punch.PunchCardTypeSearchEnum;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.util.AesUtil;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.util.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Slf4j
@Service
public class MobilePunchService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private MobilePunchDetailQueryService mobilePunchDetailQueryService;
    @Resource
    private ClockExternalService clockExternalService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceMobileConfigApplicationService mobileConfigApplicationService;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;

    @Value("${punch.in.secretKey}")
    private String punchInSecretKey;

    /**
     * 通过经纬度获取时间
     */
    public Date getTimeZone(String lat, String lng) {
        return clockExternalService.getTimeZone(lat, lng);
    }

    /**
     * 个人考勤详情
     */
    public UserDayMobilePunchDetailDTO userDetail(MobilePunchDetailQuery query) {
        return mobilePunchDetailQueryService.userDetail(query);
    }

    /**
     * 个人指定天的考勤详情
     */
    public UserDayMobilePunchDetailDTO userDetailByDayId(MobileDayPunchDetailQuery query){
        return mobilePunchDetailQueryService.userDetailByDayId(query);
    }

    /**
     * 详情后置处理
     */
    public void afterPostProcessor(UserDayMobilePunchDetailDTO detailDTO) {
        // 查询当前用户的请假/外勤/补卡 流程
        mobilePunchDetailQueryService.dealAttendanceFormList(detailDTO);
        // 计算工时（暂时取原先考勤月报接口上的出勤小时）
        // calcWorkHours(detailDTO);
    }

    /**
     * 移动打卡报文解密
     */
    public PunchInAddCommand punchInDecrypt(PunchInEncryptAddCommand punchInEncryptAddCommand){
        String decrypt = AesUtil.decrypt(
                punchInEncryptAddCommand.getContent(),
                punchInSecretKey,
                punchInEncryptAddCommand.getIvStr()
        );
        PunchInAddCommand punchInAddCommand;
        try {
            punchInAddCommand = JSONObject.parseObject(decrypt, PunchInAddCommand.class);
        } catch (Exception e) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PUNCH_IN_CONTENT_DECRYPT_ERROR);
        }
        //校验punchInAddCommand
        String validateMessage = ValidationUtil.validate(punchInAddCommand);
        if (StringUtils.isNotBlank(validateMessage)) {
            log.error("PunchInEncryptAddCommand校验失败,validateMessage:{}", validateMessage);
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), validateMessage);
        }
        if (!validateTimestamp(punchInAddCommand.getCurrentTimeStamp())) {
            log.warn("PunchInEncryptAddCommand校验失败,时间差 (毫秒): {}",
                    Instant.now().toEpochMilli() - punchInAddCommand.getCurrentTimeStamp());
        }
        return punchInAddCommand;
    }

    /**
     * 移动打卡
     */
    @Transactional(rollbackFor = Exception.class)
    public void punchIn(PunchInAddCommand addCommand) {
        AttendanceUser attendanceUser = userService.getByUserId(addCommand.getUserId());
        if (Objects.isNull(attendanceUser)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        // 添加设备记录
        Long mobileConfigId = addCommand.getMobileConfigId();
        AttendanceMobileConfigAddCommand mobileConfigAddCommand;
        if (Objects.isNull(addCommand.getMobileConfigId()) || addCommand.getMobileConfigId() <= 0) {
            mobileConfigAddCommand = buildMobileConfig(attendanceUser, addCommand);
            mobileConfigId = mobileConfigAddCommand.getId();
        }
        // 添加打卡记录
        buildUserPunchRecord(attendanceUser, addCommand, mobileConfigId);
        // 实时计算异常
        AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = AttendanceCalculateHandlerDTO.builder()
                .attendanceDayId(addCommand.getDayId())
                .userCodes(attendanceUser.getUserCode())
                .build();
        attendanceGenerateService.attendanceCalculateHandler(dayAttendanceHandlerDTO);
    }

    /**
     * 构建打卡记录
     */
    private void buildUserPunchRecord(AttendanceUser attendanceUser, PunchInAddCommand addCommand, Long mobileConfigId) {
        //打卡表新增打卡记录
        EmployeePunchRecordDO userPunchRecordDO = new EmployeePunchRecordDO();
        userPunchRecordDO.setId(defaultIdWorker.nextId());
        userPunchRecordDO.setSourceType(SourceTypeEnum.USER.name());
        userPunchRecordDO.setUserCode(attendanceUser.getUserCode());
        userPunchRecordDO.setDayId(String.valueOf(addCommand.getDayId()));
        userPunchRecordDO.setPunchTime(addCommand.getDateTime());
        userPunchRecordDO.setDeptId(attendanceUser.getDeptId());
        userPunchRecordDO.setEmployeeType(attendanceUser.getEmployeeType());
        userPunchRecordDO.setMobileConfigId(mobileConfigId);
        userPunchRecordDO.setCountry(attendanceUser.getLocationCountry());
        if (Objects.nonNull(addCommand.getWifiConfigId())) {
            userPunchRecordDO.setPunchCardType(PunchCardTypeSearchEnum.WIFI.name());
            userPunchRecordDO.setWifiConfigId(addCommand.getWifiConfigId());
            userPunchRecordDO.setWifiConfigName(addCommand.getWifiConfigName());
            userPunchRecordDO.setPunchArea(addCommand.getWifiConfigCity());
        }
        //gps的优先级更高，会覆盖wifi
        if (Objects.nonNull(addCommand.getGpsConfigId())) {
            userPunchRecordDO.setPunchCardType(PunchCardTypeSearchEnum.GPS.name());
            userPunchRecordDO.setGpsConfigId(addCommand.getGpsConfigId());
            userPunchRecordDO.setGpsConfigName(addCommand.getGpsConfigName());
            userPunchRecordDO.setPunchArea(addCommand.getGpsConfigCity());
        }
        if (Objects.nonNull(addCommand.getGpsConfigId()) && StringUtils.isBlank(addCommand.getGpsConfigCity())) {
            log.info("buildEmployeePunchRecord | 打卡记录有异常，可能存在打卡作弊行为！, userCode:{}", attendanceUser.getUserCode());
            if (RequestInfoHolder.isChinese()) {
                throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR, "员工编码:" + attendanceUser.getUserCode() + "员工姓名:" + attendanceUser.getUserName() + "已记录，可能存在打卡作弊行为!");
            }
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR, "userCode:" + attendanceUser.getUserCode() + "userName:" + attendanceUser.getUserNameEn() + "It's been documented. Possible clocking cheating!");
        }
        userPunchRecordDO.setLongitude(Optional.ofNullable(addCommand.getLongitude()).orElse(BigDecimal.ZERO));
        userPunchRecordDO.setLatitude(Optional.ofNullable(addCommand.getLatitude()).orElse(BigDecimal.ZERO));
        BaseDOUtil.fillDOInsert(userPunchRecordDO);
        employeePunchRecordDao.save(userPunchRecordDO);
    }

    /**
     * 构建手机配置
     */
    private AttendanceMobileConfigAddCommand buildMobileConfig(AttendanceUser userInfo, PunchInAddCommand punchInAddCommand) {
        AttendanceMobileConfigAddCommand addCommand = AttendanceMobileConfigAddCommand.builder()
                .id(defaultIdWorker.nextId())
                .mobileModel(punchInAddCommand.getMobileModel())
                .userCode(userInfo.getUserCode())
                .mobileUnicode(punchInAddCommand.getMobileUnicode())
                .mobileBranch(punchInAddCommand.getMobileBranch())
                .mobileVersion(punchInAddCommand.getMobileVersion())
                .build();
        mobileConfigApplicationService.add(addCommand);
        return addCommand;
    }

    /**
     * 验证客户端时间戳是否在指定的过期时间内。
     *
     * @param clientTimestamp 客户端时间戳（毫秒）
     * @return 如果客户端时间戳在过期时间内，则返回true，否则返回false
     */
    private Boolean validateTimestamp(long clientTimestamp) {
        long timeDiff = Instant.now().toEpochMilli() - clientTimestamp;
        return timeDiff <= BusinessConstant.EXPIRE_TIME_INTERNAL_MILLISECONDS;
    }
}
