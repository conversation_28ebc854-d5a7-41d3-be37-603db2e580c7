package com.imile.attendance.vacation.job.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIsCarryOverEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIsInvalidEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigCarryOverService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.attendance.vacation.param.UserLeaveInvalidParam;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 假期失效service实现
 *
 * <AUTHOR>
 * @since 2025/04/27
 */
@Slf4j
@Service
public class UserLeaveInvalidService {

    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Resource
    private CountryService countryService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserLeaveDetailManage userLeaveDetailManage;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendanceUserService userInfoService;

    private static final Integer PAGE_SIZE_DEFAULT = 5000;

    public void userLeaveInvalidHandler(UserLeaveInvalidParam param) {
        List<String> countryList;
        if (CollectionUtils.isEmpty(param.getCountryArrayList())) {
            CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(companyLeaveQuery);
            if (companyLeaveConfigList.isEmpty()) {
                XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】companyLeaveConfigList is Empty");
                return;
            }
            // 获取通过假期分组
            countryList = companyLeaveConfigList.stream()
                    .map(CompanyLeaveConfigDO::getCountry)
                    .collect(Collectors.toList());
        } else {
            countryList = param.getCountryArrayList();
        }
        // 查询人员
        int currentPage = 1, pageSize = PAGE_SIZE_DEFAULT;
        Page<AttendanceUser> page = PageHelper.startPage(currentPage, pageSize, true);
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .locationCountryList(countryList)
                .userCodes(param.getUserCodeList())
                .employeeTypes(param.getEmployeeTypeList())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDelete(IsDeleteEnum.NO.getCode())
                .build();
        PageInfo<AttendanceUser> pageInfo = page.doSelectPageInfo(() -> userInfoService.listUsersByQuery(userDaoQuery));
        int userCount = BusinessConstant.ZERO.intValue();
        // 总记录数
        List<AttendanceUser> pageUserInfoList = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
            userCount = pageUserInfoList.size();
            handleUserInvalid(pageUserInfoList, countryList, param);
        }
        XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】 | currentPage:{},pageSize:{},total:{},pages：{}"
                , currentPage, pageSize, pageInfo.getTotal(), pageInfo.getPages());
        while (currentPage < pageInfo.getPages()) {
            XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】 | 进入while循环");
            currentPage++;
            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> userInfoService.listUsersByQuery(userDaoQuery));
            pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】 | while循环：pageUserInfoList size:{}，pageUserInfoList：{}"
                        , pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                userCount = userCount + pageUserInfoList.size();
                handleUserInvalid(pageUserInfoList, countryList, param);
            }
            XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】 | while循环：currentPage:{},pageSize:{},total:{}"
                    , currentPage, pageSize, pageInfo.getTotal());
        }
        XxlJobLogger.log("xxl-job【userLeaveInvalidHandler】 | currentPage {}，userCount {} while循环结束", currentPage, userCount);
    }

    private void handleUserInvalid(List<AttendanceUser> userInfoList,
                                   List<String> countryList,
                                   UserLeaveInvalidParam param) {
        // 查询用户假期详情
        List<Long> userIds = userInfoList.stream().map(AttendanceUser::getId).collect(Collectors.toList());
        UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                .userIds(userIds)
                .build();
        // 获取该国家所有员工拥有的假期
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
        // 获取该国家所有员工拥有的假期的主键
        List<Long> leaveIdList = userLeaveDetailDOList.stream()
                .filter(item -> Objects.nonNull(item.getConfigId()))
                .map(UserLeaveDetailDO::getId)
                .collect(Collectors.toList());
        // 通过用户详情获取假期规则主键及假期规则配置
        List<Long> configIdList = userLeaveDetailDOList.stream()
                .filter(item -> Objects.nonNull(item.getConfigId()))
                .map(UserLeaveDetailDO::getConfigId)
                .distinct()
                .collect(Collectors.toList());
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.getByIdList(configIdList);
        // 获取该国家所有员工拥有的生效的并且结转假期详情数据
        UserLeaveStageDetailQuery userLeaveStageDetailQuery = new UserLeaveStageDetailQuery();
        userLeaveStageDetailQuery.setIsInvalid(WhetherEnum.NO.getKey());
        userLeaveStageDetailQuery.setLeaveMark(WhetherEnum.YES.getKey());
        userLeaveStageDetailQuery.setLeaveIdList(leaveIdList);
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByCondition(userLeaveStageDetailQuery);
        // 由于需要获取时区，所以这边获取国家的列表
        List<String> countryInfoList = companyLeaveConfigList.stream().map(CompanyLeaveConfigDO::getCountry).distinct().collect(Collectors.toList());
        // 重构开始：获取假期结转规则
        List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList = companyLeaveConfigCarryOverService.selectByLeaveId(configIdList);
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        if (CollectionUtils.isEmpty(countryList)) {
            // 如果没有指定国家，则查询有设置假期的所有国家
            countryQuery.setCountryNames(countryInfoList);
        }
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));
        // 获取当前时间
        Date now = this.getNowDateByParam(param);
        // 更新用户假期详情集合
        List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList = Lists.newArrayList();
        // 新增用户假期操作记录集合
        List<UserLeaveRecordDO> addUserLeaveRecordList = Lists.newArrayList();

        // 分组通过国家处理
        for (String country : countryList) {
            // 获取国家对应时区
            String timeZone = countryConfigMap.getOrDefault(country, "");
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("该国家:{},不存在国家时区", country);
                continue;
            }
            XxlJobLogger.log("当前国家:{},时区:{}", country, timeZone);
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            if (param.getIsUseCustomLocalTime()) {
                // 自定义时间，不根据国家时区来计算
                dateTime = now;
            }
            if (Objects.isNull(dateTime)) {
                XxlJobLogger.log("该国家:{},未获取到转换时间", country);
                continue;
            }
            // 格式：2024-01-01 monthDay = 101 ，格式：2024-10-11 monthDay = 1011，为什么使用三位，如果使用两位，那么1月21日和12月1日的月日是相同的
            Long monthDay = Long.valueOf(DateUtil.format(dateTime, "Mdd"));
            XxlJobLogger.log("【当前时间明天的时间】，北京时间:{},当前国家:{},当地时间:{}", now, country, dateTime);
            int year = DateUtil.year(dateTime);
            int month = DateUtil.month(dateTime) + 1;
            int day = DateUtil.dayOfMonth(dateTime);
            XxlJobLogger.log("当前国家:{},年:{},月:{},日:{}", country, year, month, day);

            // 过滤国家下员工
            List<AttendanceUser> userInfoByCountry = userInfoList.stream()
                    .filter(user -> country.equals(user.getLocationCountry()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userInfoByCountry)) {
                XxlJobLogger.log("该国家:{},不存在人员", country);
                continue;
            }
            List<Long> userIdByCountry = userInfoByCountry.stream().map(AttendanceUser::getId).collect(Collectors.toList());
            Map<Long, List<AttendanceUser>> userMapByCountry = userInfoByCountry.stream().collect(Collectors.groupingBy(AttendanceUser::getId));
            // 过滤国家下员工对应得假期详情
            List<UserLeaveDetailDO> leaveDetailByCountry = userLeaveDetailDOList.stream()
                    .filter(detail -> userIdByCountry.contains(detail.getUserId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveDetailByCountry)) {
                XxlJobLogger.log("该国家:{},不存在人员假期详情", country);
                continue;
            }
            // 过滤员工对应得假期详情id
            List<Long> userLeaveDetailIdList = leaveDetailByCountry.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
            // 过滤员工对应得假期规则
            List<Long> userConfigIdList = leaveDetailByCountry.stream().map(UserLeaveDetailDO::getConfigId).collect(Collectors.toList());
            List<CompanyLeaveConfigDO> userCompanyLeaveConfig = companyLeaveConfigList.stream()
                    .filter(config -> userConfigIdList.contains(config.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userCompanyLeaveConfig)) {
                XxlJobLogger.log("该国家:{},不存在对应人员假期规则", country);
                continue;
            }
            // 过滤员工对应得假期结转规则
            List<Long> userLeaveConfigId = userCompanyLeaveConfig.stream().map(CompanyLeaveConfigDO::getId).collect(Collectors.toList());
            List<CompanyLeaveConfigCarryOverDO> userCarryOverList = companyLeaveConfigCarryOverList.stream()
                    .filter(carry -> userLeaveConfigId.contains(carry.getLeaveId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userCarryOverList)) {
                XxlJobLogger.log("该国家:{},不存在对应人员假期结转规则", country);
                continue;
            }
            // 将国家的假期结转规则根据leaveId转为map
            Map<Long, CompanyLeaveConfigCarryOverDO> userLeaveConfigCarryOverMap = userCarryOverList.stream()
                    .collect(Collectors.toMap(CompanyLeaveConfigCarryOverDO::getLeaveId, Function.identity()));

            // 获取假期余额详情:生效且结转数据
            String finalDateTime = DateUtils.date2Str(dateTime, DateFormatterUtil.FORMAT_YYYYMMDD);
            List<UserLeaveStageDetailDO> userLeaveStageDetailByCountry = userLeaveStageDetailList.stream()
                    .filter(item -> userLeaveDetailIdList.contains(item.getLeaveId())
                            && WhetherEnum.YES.getKey().equals(item.getLeaveMark())
                            && WhetherEnum.NO.getKey().equals(item.getIsInvalid())
                            && StringUtils.isNotBlank(item.getInvalidDate())
                            && finalDateTime.equals(item.getInvalidDate())
                    )
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userLeaveStageDetailByCountry)) {
                XxlJobLogger.log("该国家:{},不存在对应人员假期生效且结转到期数据", country);
                continue;
            }
            Map<Long, List<UserLeaveStageDetailDO>> userLeaveStageDetailMap = userLeaveStageDetailByCountry.stream()
                    .collect(Collectors.groupingBy(UserLeaveStageDetailDO::getLeaveId));

            // 遍历人员对应得假期配置
            for (CompanyLeaveConfigDO companyLeaveConfig : userCompanyLeaveConfig) {
                Long configId = companyLeaveConfig.getId();
                String configName = companyLeaveConfig.getLeaveName();
                DateTime date = DateUtil.date();
                CompanyLeaveConfigCarryOverDO companyLeaveConfigCarryOver = userLeaveConfigCarryOverMap.get(configId);
                // 校验完成，开始获取假期余额处理用户假期结转数据
                List<UserLeaveDetailDO> userConfigLeaveDetail = leaveDetailByCountry.stream()
                        .filter(detail -> configId.equals(detail.getConfigId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userConfigLeaveDetail)) {
                    XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},不存在该假期的记录", country, configId, configName);
                    continue;
                }
                if (ObjectUtil.isNull(companyLeaveConfigCarryOver)) {
                    XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},不存在该假期的结转数据", country, configId, configName);
                    continue;
                }
                // 只有非永久有效，可结转的假期才需要处理
                if (ObjectUtil.equal(companyLeaveConfigCarryOver.getIsInvalid(), LeaveConfigIsInvalidEnum.NO.getType()) &&
                        ObjectUtil.equal(companyLeaveConfigCarryOver.getIsCarryOver(), LeaveConfigIsCarryOverEnum.YES.getType())) {
                    // 获取失效日期
                    Long invalidDate = companyLeaveConfigCarryOver.getInvalidDate();
                    if (ObjectUtil.isNull(invalidDate)) {
                        XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},不存在假期失效日期", country, configId, configName);
                        continue;
                    }
                    if (ObjectUtil.equal(invalidDate, 0)) {
                        XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},假期失效日期为0", country, configId, configName);
                        continue;
                    }
//                    // 校验是否到失效日期，未到失效日期不处理
//                    if (ObjectUtil.notEqual(monthDay, invalidDate)) {
//                        XxlJobLogger.log("当前国家:{},假期配置id:{},假期名称:{},当前日期不是假期失效日期", country, configId, configName);
//                        continue;
//                    }

                    for (UserLeaveDetailDO userLeaveDetailDO : userConfigLeaveDetail) {
                        // 获取该假期的用户id
                        Long userId = userLeaveDetailDO.getUserId();
                        AttendanceUser userInfo = CollectionUtils.isEmpty(userMapByCountry.get(userId)) ? null : userMapByCountry.get(userId).get(0);
                        if (ObjectUtil.isNull(userInfo)) {
                            XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},用户id:{},不存在该用户", country, configId, configName, userId);
                            continue;
                        }
                        List<UserLeaveStageDetailDO> userLeaveStageDetailByLeaveId = userLeaveStageDetailMap.get(userLeaveDetailDO.getId());
                        if (CollectionUtils.isEmpty(userLeaveStageDetailByLeaveId)) {
                            XxlJobLogger.log("error：当前国家:{},假期配置id:{},假期名称:{},用户id:{},该用户不存在失效假期余额", country, configId, configName, userId);
                            continue;
                        }
                        // 获取该假期之前总的分钟数
                        BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                        for (UserLeaveStageDetailDO leaveStageDetail : userLeaveStageDetailByLeaveId) {
                            leaveStageDetail.setIsInvalid(WhetherEnum.YES.getKey());
                            leaveStageDetail.setLastUpdDate(date);
                            leaveStageDetail.setLastUpdUserCode("xxl-job");
                            leaveStageDetail.setLastUpdUserName("xxl-job");
                            updateUserLeaveStageDetailList.add(leaveStageDetail);
                            oldTotalMinutes = oldTotalMinutes.add(leaveStageDetail.getLeaveResidueMinutes());
                        }
                        // 新增操作记录
                        if (oldTotalMinutes.compareTo(BigDecimal.ZERO) >= 0) {
                            addLeaveRecord(userInfo, configId, oldTotalMinutes, addUserLeaveRecordList, "false"
                                    , dateTime, companyLeaveConfig);
                        }
                    }
                }
            }
        }
        // 落库
        userLeaveDetailManage.userLeaveBalanceDaysUpdate(null, null,
                addUserLeaveRecordList, updateUserLeaveStageDetailList, null);
    }

    /**
     * 增加假期调整操作记录
     *
     * @param userInfo               用户信息
     * @param leaveConfigId          假期id
     * @param totalMinutes           假期总分钟数
     * @param addUserLeaveRecordList 假期操作记录集合
     * @param flag                   是否销假【true:销假，false:请假】
     */
    private void addLeaveRecord(AttendanceUser userInfo, Long leaveConfigId, BigDecimal totalMinutes
            , List<UserLeaveRecordDO> addUserLeaveRecordList, String flag, Date now
            , CompanyLeaveConfigDO companyLeaveConfig) {
        UserLeaveRecordDO recordDO = new UserLeaveRecordDO();
        recordDO.setId(defaultIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(now);
        recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        recordDO.setConfigId(leaveConfigId);
        recordDO.setLeaveName(companyLeaveConfig.getLeaveName());
        recordDO.setLeaveType(companyLeaveConfig.getLeaveType());
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
        } else {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
        }
        recordDO.setLeaveMinutes(totalMinutes);
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setRemark("系统初始化【假期数据失效】/System initialization");
        } else {
            recordDO.setRemark("系统初始化【用户假期数据失效-删除失效数据】/System initialization");
        }
        BaseDOUtil.fillDOInsert(recordDO);
        // 防止查询时间相同，导致根据创建时间排序出现先后顺序问题【根据创建时间倒序之后，过期清零在新年初始化上面就比较奇怪】，所以这边新年初始化创建时间需要在过期清零后面，
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setCreateDate(DateUtil.offsetSecond(new Date(), 1));
            recordDO.setLastUpdDate(DateUtil.offsetSecond(new Date(), 1));
        }
        recordDO.setCreateUserCode("xxl-job");
        recordDO.setCreateUserName("xxl-job");
        recordDO.setLastUpdUserCode("xxl-job");
        recordDO.setLastUpdUserName("xxl-job");
        recordDO.setOperationUserCode("xxl-job");
        recordDO.setOperationUserName("xxl-job");
        addUserLeaveRecordList.add(recordDO);
    }

    /**
     * 获取当前时间
     *
     * @param param
     * @return
     */
    private Date getNowDateByParam(UserLeaveInvalidParam param) {
        Date now = new Date();
        // 获取当前时间，明天
        if (param.getIsUseCustomLocalTime()) {
            // 根据当前时间年份月日时生成日期
            String localDate = param.getLocalYear() +
                    "-" + param.getLocalMonth() +
                    "-" + param.getLocalDay() +
                    " " + param.getLocalHour() + ":00:00";
            now = DateUtil.parse(localDate);
        }
        return now;
    }
}
