package com.imile.attendance.vacation;

import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigIssueRuleDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigIssueRuleRangeDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveJourneyConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigIssueRuleQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * {@code @author:} allen
 * {@code @className:} CompanyLeaveConfigIssueRuleService
 * {@code @since:} 2024-04-10 14:32
 * {@code @description:}
 */
@Service
@Slf4j
public class CompanyLeaveConfigIssueRuleService {

    @Resource
    private CompanyLeaveConfigIssueRuleDao companyLeaveConfigIssueRuleDao;
    @Resource
    private CompanyLeaveConfigIssueRuleRangeDao companyLeaveConfigIssueRuleRangeDao;
    @Resource
    private CompanyLeaveJourneyConfigDao companyLeaveJourneyConfigDao;

    /**
     * 根据条件获取假期配置发放规则
     *
     * @param issueRuleQuery 假期发放规则条件
     * @return 假期配置发放规则
     */
    public List<CompanyLeaveConfigIssueRuleDO> getLeaveConfigIssueRuleList(LeaveConfigIssueRuleQuery issueRuleQuery) {
        return companyLeaveConfigIssueRuleDao.getLeaveConfigIssueRuleList(issueRuleQuery);
    }

    /**
     * 获取假期配置发放规则
     *
     * @param leaveConfigIdList 假期配置id
     * @return 假期配置发放规则
     */
    public List<CompanyLeaveConfigIssueRuleDO> selectByLeaveId(List<Long> leaveConfigIdList) {
        return companyLeaveConfigIssueRuleDao.selectByLeaveId(leaveConfigIdList);
    }

    /**
     * 根据假期主表主键获取发放规则
     *
     * @param leaveId
     * @return
     */
    public List<CompanyLeaveConfigIssueRuleDO> getLeaveConfigIssueRuleListById(Long leaveId) {
        if (Objects.isNull(leaveId)) {
            return Lists.newArrayList();
        }
        return companyLeaveConfigIssueRuleDao.getLeaveConfigIssueRuleListById(leaveId);
    }

    /**
     * 根据发放规则主键获取发放规则范围
     *
     * @param issueRuleId
     * @return
     */
    public List<CompanyLeaveConfigIssueRuleRangeDO> getLeaveConfigIssueRuleRangeList(Long issueRuleId) {
        if (Objects.isNull(issueRuleId)) {
            return Lists.newArrayList();
        }
        return companyLeaveConfigIssueRuleRangeDao.getLeaveConfigIssueRuleRangeList(issueRuleId);
    }

    /**
     * 获取假期配置发放规则范围
     *
     * @param issueRuleIdList 假期配置发放规则id
     * @return 假期配置发放规则范围
     */
    public List<CompanyLeaveConfigIssueRuleRangeDO> selectByIssueRuleId(List<Long> issueRuleIdList) {
        return companyLeaveConfigIssueRuleRangeDao.selectByIssueRuleId(issueRuleIdList);
    }

    /**
     * 根据发放规则主键获取路程假范围
     *
     * @param issueRuleId 用户id
     * @return 假期配置范围
     */
    public List<CompanyLeaveJourneyConfigDO> getLeaveJourneyConfigList(Long issueRuleId) {
        if (Objects.isNull(issueRuleId)) {
            return Lists.newArrayList();
        }
        return companyLeaveJourneyConfigDao.getLeaveJourneyConfigList(issueRuleId);
    }

    public List<CompanyLeaveJourneyConfigDO> selectJourneyRangByIssueRuleId(List<Long> issueRuleIdList) {
        return companyLeaveJourneyConfigDao.selectByIssueRuleId(issueRuleIdList);
    }
}
