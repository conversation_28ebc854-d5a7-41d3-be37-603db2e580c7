package com.imile.attendance.vacation;

import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigRangDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigRangQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * {@code @author:} allen
 * {@code @className:} CompanyLeaveConfigRangService
 * {@code @since:} 2024-04-09 10:26
 * {@code @description:}
 */
@Service
@Slf4j
public class CompanyLeaveConfigRangService {

    @Resource
    private CompanyLeaveConfigRangDao companyLeaveConfigRangDao;

    /**
     * 获取假期配置范围
     *
     * @param userIdList 用户id
     * @return 假期配置范围
     */
    public List<CompanyLeaveConfigRangDO> selectRangByUserCode(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Lists.newArrayList();
        }
        return companyLeaveConfigRangDao.selectRangByUserCode(userIdList);
    }

    /**
     * 根据条件获取假期配置范围
     *
     * @param leaveConfigRangQuery
     * @return
     */
    public List<CompanyLeaveConfigRangDO> getLeaveConfigRangList(LeaveConfigRangQuery leaveConfigRangQuery) {
        if (Objects.isNull(leaveConfigRangQuery)) {
            return Lists.newArrayList();
        }
        return companyLeaveConfigRangDao.getLeaveConfigRangList(leaveConfigRangQuery);
    }

    /**
     * 获取假期配置范围
     *
     * @param leaveConfigIdList 假期配置id
     * @return 假期配置范围
     */
    public List<CompanyLeaveConfigRangDO> selectByLeaveId(List<Long> leaveConfigIdList) {
        return companyLeaveConfigRangDao.selectByLeaveId(leaveConfigIdList);
    }

    public void handlerCompanyLeaveConfigRang(List<CompanyLeaveConfigRangDO> addLeaveRang,
                                              List<CompanyLeaveConfigRangDO> updateLeaveRang) {
        companyLeaveConfigRangDao.handlerCompanyLeaveConfigRang(addLeaveRang, updateLeaveRang);
    }
}
