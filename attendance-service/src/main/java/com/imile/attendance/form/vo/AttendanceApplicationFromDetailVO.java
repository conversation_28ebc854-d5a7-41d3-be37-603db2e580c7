package com.imile.attendance.form.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.user.dto.AttachmentDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-26
 * @version: 1.0
 */
@Data
public class AttendanceApplicationFromDetailVO {
    /**
     * 被申请人ID
     */
    private Long userId;

    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人部门名称
     */
    private String deptName;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人岗位名称
     */
    private String postName;

    /**
     * 被申请人国家
     */
    private String country;

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据ID
     */
    private Long applicationFormId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 假期类型
     */
    private Long configId;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 假期类型描述(多语转换)
     */
    private String leaveTypeByLang;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 假期剩余时间(分钟)
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 节假日是否消耗假期 (LeaveConsumeTypeEnum)
     */
    private String consumeLeaveType;

    /**
     * 是否上传附件
     */
    private Integer isUploadAttachment;

    /**
     * 请假开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveStartDate;

    /**
     * 请假结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveEndDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 用户预计请假时长
     */
    private String expectedLeaveTime;

    /**
     * 冲突单据信息
     */
    private List<ClashApplicationInfoDTO> clashApplicationInfoDTOList;

    /**
     * 每天时长计算明细
     */
    private List<DayDurationInfoDTO> dayDurationInfoDTOList;

    /**
     * 用户预计外勤时长
     */
    private String expectedOutOfOfficeTime;

    /**
     * 外勤开始时间
     */
    private Date outOfOfficeStartDate;

    /**
     * 外勤结束时间
     */
    private Date outOfOfficeEndDate;

    /**
     * 补卡日期(即要处理的哪天的异常考勤日期)
     */
    private Long reissueCardDayId;

    /**
     * 补卡类型
     */
    private String reissueCardType;

    /**
     * 剩余可用补卡次数
     */
    private Integer residueReissueCardCount;

    /**
     * 当前补卡日期对应的考勤周期起始时间
     */
    private Date attendanceStartDate;

    /**
     * 当前补卡日期对应的考勤周期截止时间
     */
    private Date attendanceEndDate;

    /**
     * 打卡规则对应的班次的所有的时刻信息
     */
    private String punchConfigClassItemInfo;

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期名称描述(多语转换)
     */
    private String leaveNameByLang;
}
