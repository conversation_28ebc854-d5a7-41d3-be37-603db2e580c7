package com.imile.attendance.form.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9 
 * @Description
 */
@Data
public class OverTimeCalcParam {

    /**
     * 加班开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date overTimeStartDate;

    /**
     * 加班结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date overTimeEndDate;

}
