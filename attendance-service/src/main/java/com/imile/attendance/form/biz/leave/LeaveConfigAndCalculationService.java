package com.imile.attendance.form.biz.leave;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.param.DurationDetailParam;
import com.imile.attendance.form.vo.DurationDetailVO;
import com.imile.attendance.user.vo.UserLeaveResidualVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9 
 * @Description 假期配置和计算服务
 */
@Slf4j
@Service
public class LeaveConfigAndCalculationService {


    /**
     * 获取用户假期余额信息
     */
    public List<UserLeaveResidualVO> selectUserLeaveResidual(Long userId){
        return null;
    }

    /**
     * 请假/外勤获取冲突单据和具体时长计算信息
     */
    public DurationDetailVO durationDetail(DurationDetailParam param){
        return null;
    }

    /**
     * 外勤/请假-每天时长计算明细 todo 参数
     */
    public void dayDurationInfoHandler0(){}

    /**
     * 获取请假总时长-单位分钟
     */
    public BigDecimal handlerLeaveTotalTime0(List<DayDurationInfoDTO> dayDurationInfoList){
        return null;
    }

//    /**
//     * 查询国家的假期配置
//     */
//    public HrmsCompanyLeaveConfigDO getCountryLeaveTypeConfig(String country, String leaveType) {
//        return getLeaveTypeDO(country, leaveType);
//    }

//    /**
//     * 通过人员编码关联假期范围查询国家的假期配置
//     *
//     * @param userCodeList 用户编码
//     * @param leaveName    假期名称
//     * @return HrmsCompanyLeaveConfigDO 假期配置
//     */
//    public HrmsCompanyLeaveConfigDO getUserLeaveTypeConfig(List<String> userCodeList, String leaveName) {
//        return selectByNameAndUserCode(userCodeList, leaveName);
//    }


//    /**
//     * 通过人员编码关联假期范围查询国家的假期配置
//     *
//     * @param userCodeList 用户编码
//     * @param leaveName    假期名称
//     * @return HrmsCompanyLeaveConfigDO 假期配置
//     */
//    public HrmsCompanyLeaveConfigDO getUserLeaveTypeConfig(List<String> userCodeList, String leaveName) {
//        return selectByNameAndUserCode(userCodeList, leaveName);
//    }

//    /**
//     * @param formDetailBO                 假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
//     * @param userLeaveStageDetailInfoList 用来接收-需要修改的假期详情数据列表
//     * @param remark                       备注
//     * @return HrmsUserLeaveRecordDO
//     */
//    public HrmsUserLeaveRecordDO handlerUserLeaveStageDetailList0(AttendanceFormDetailBO formDetailBO,
//                                                                  List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList,
//                                                                  String remark) {
//        return handlerUserLeaveStageDetailList(formDetailBO, userLeaveStageDetailInfoList, remark);
//    }

//    /**
//     * 通过人员查询假期
//     */
//    private HrmsCompanyLeaveConfigDO selectByNameAndUserCode(List<String> userCodeList, String leaveName) {
//        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
//        companyLeaveQuery.setLeaveName(leaveName);
//        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());
//        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.selectConfigByUserCodeAndQueryList(userCodeList, companyLeaveQuery);
//        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
//            throw BusinessException.get(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc()));
//        }
//        List<HrmsCompanyLeaveConfigDO> sameTypeList = companyLeaveConfigDOList.stream().filter(item -> leaveName.equals(item.getLeaveName())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(sameTypeList)) {
//            return sameTypeList.get(0);
//        }
//        return companyLeaveConfigDOList.get(0);
//    }


    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    public BigDecimal handlerLeaveTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历每一天的请假时长
        for (DayDurationInfoDTO dayDurationInfo : dayDurationInfoList) {

            // 计算一天的请假时长-分钟（天 * 法定工作时长 8h * 一小时的分钟数）【这里面的法定时长为什么给8h：因为，假期给的时候一天是按照8h给，所以请一天假，需要扣假期8h】
            totalMinutes = totalMinutes.add(dayDurationInfo.getDays().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));

            // 换算之后的小时数【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
            BigDecimal realHours = dayDurationInfo.getHours().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP);
            // 计算小时的请假时长-分钟（小时 * 一小时的分钟数）
            totalMinutes = totalMinutes.add(realHours.multiply(BusinessConstant.MINUTES));

            /*
                 计算分钟的请假时长-分钟【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
                    算法1：分钟/60min/工作时长h * 8h = 分钟转换成8h的分钟数
                    //BigDecimal realMinutesHours = dayDurationInfo.getMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
                    算法2：分钟 * 8h * 60min / 工作时长h * 60min
                    算法1 存在很大精度问题，因为有多次除法，每一次除法都会保留两位小数，然后四舍五入，所以会存在精度问题
                    算法2 只有一次除法，所以精度问题 小很多
            */

            BigDecimal realMinutes = dayDurationInfo.getMinutes().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES).divide(dayDurationInfo.getLegalWorkingHours().multiply(BusinessConstant.MINUTES), 2, RoundingMode.HALF_UP);

            totalMinutes = totalMinutes.add(realMinutes);
        }
        return totalMinutes;
    }

//    /**
//     * 查询假期
//     */
//    private HrmsCompanyLeaveConfigDO getLeaveTypeDO(String country, String leaveType) {
//        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
//        companyLeaveQuery.setCountry(country);
//        companyLeaveQuery.setLeaveType(leaveType);
//        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());
//        List<HrmsCompanyLeaveConfigDO> companyLeaveConfigDOList = hrmsCompanyLeaveConfigManage.selectCompanyLeave(companyLeaveQuery);
//        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
//            throw BusinessException.get(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getCode(), I18nUtils.getMessage(HrmsErrorCodeEnums.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc()));
//        }
//        return companyLeaveConfigDOList.get(0);
//    }


//    /**
//     * 构建假期详情表数据
//     *
//     * @param detailBO                     假期申请单数据、假期申请单详情数据、考勤申请单关联表数据
//     * @param userLeaveStageDetailInfoList 用来接收-需要修改的假期详情数据列表
//     * @param remark                       用来接收-需要修改的假期详情数据列表
//     * @return HrmsUserLeaveRecordDO
//     */
//    public HrmsUserLeaveRecordDO handlerUserLeaveStageDetailList(AttendanceFormDetailBO detailBO,
//                                                                 List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailInfoList,
//                                                                 String remark) {
//        // 假期申请单信息
//        AttendanceFormDO formInfo = detailBO.getFormDO();
//        // 假期申请单详情数据
//        List<AttendanceFormAttrDO> attrInfo = detailBO.getAttrDOList();
//        // 获取这笔销假申请所关联的原始申请单
//        List<AttendanceFormRelationDO> relationInfo = detailBO.getRelationDOList();
//        // 获取申请单详情数据-假期配置主键信息
//        List<AttendanceFormAttrDO> configIdInfo = attrInfo.stream().
//                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.configID.getLowerCode())).
//                collect(Collectors.toList());
//        // 获取申请单详情数据-请假名称信息
//        List<AttendanceFormAttrDO> leaveNameInfo = attrInfo.stream().
//                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveType.getLowerCode())).
//                collect(Collectors.toList());
//        // 获取申请单详情数据-请假开始时间
//        List<AttendanceFormAttrDO> leaveStartDateInfo = attrInfo.stream().
//                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).
//                collect(Collectors.toList());
//        // 获取申请单详情数据-请假结束时间
//        List<AttendanceFormAttrDO> leaveEndDateInfo = attrInfo.stream().
//                filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), HrAttendanceApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).
//                collect(Collectors.toList());
//        // 如果请假名称集合为空，直接返回
//        if (CollectionUtils.isEmpty(leaveNameInfo)) {
//            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THERE_IS_NO_LEAVE_TYPE_INFORMATION_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM);
//        }
//        // 如果请假开始时间、结束时间集合为空，直接返回
//        if (CollectionUtils.isEmpty(leaveStartDateInfo) || CollectionUtils.isEmpty(leaveEndDateInfo)) {
//            throw BusinessLogicException.getException(HrmsErrorCodeEnums.THERE_IS_NO_LEAVE_START_OR_END_TIME_FOR_THE_DETAILS_OF_THE_APPLICATION_FORM);
//        }
//        // 转日期格式
//        Date startDate = DateUtil.parse(leaveStartDateInfo.get(0).getAttrValue(), DatePattern.NORM_DATETIME_PATTERN);
//        Date endDate = DateUtil.parse(leaveEndDateInfo.get(0).getAttrValue(), DatePattern.NORM_DATETIME_PATTERN);
//
//        // 查询该假期配置
//        // 修改为根据人员范围查询
//        HrmsCompanyLeaveConfigDO companyLeaveConfigDO;
//        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
//            companyLeaveConfigDO = hrmsCompanyLeaveConfigManage.getById(Long.valueOf(configIdInfo.get(0).getAttrValue()));
//        } else {
//            // 存在历史数据保留此方法
//            companyLeaveConfigDO = selectByNameAndUserCode(Arrays.asList(formInfo.getUserCode()), leaveNameInfo.get(0).getAttrValue());
//        }
//        // 重新计算每天请假时间
//        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
//        userBaseInfoService.dayDurationInfoHandler(formInfo.getUserId(), startDate, endDate, companyLeaveConfigDO, dayDurationInfoDTOList, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
//        // 计算请假总时长-单位分钟
//        BigDecimal totalLeaveTime = handlerLeaveTotalTime(dayDurationInfoDTOList);
//        // 用户请假记录数据 需要总的请假时长，这边重新接收一下。保证不会变化
//        BigDecimal userLeaveRecordTotalLeaveTime = totalLeaveTime;
//
//        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
//        // param参数在前面已经校验了
//        query.setUserId(formInfo.getUserId());
//        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
//            query.setConfigId(Long.valueOf(configIdInfo.get(0).getAttrValue()));
//        } else {
//            query.setLeaveName(leaveNameInfo.get(0).getAttrValue());
//        }
//        List<HrmsUserLeaveDetailDO> userLeaveDetail = hrmsUserLeaveDetailManage.selectUserLeaveDetail(query);
//        List<Long> leaveIdList = userLeaveDetail.stream().map(HrmsUserLeaveDetailDO::getId).collect(Collectors.toList());
//        List<HrmsUserLeaveStageDetailDO> userLeaveStageDetailList = hrmsUserLeaveStageDetailManage.selectByLeaveId(leaveIdList);
//        // 先返还非结转的假期，然后再返还结转的假期余额,将假期先按照是否结转排序后再按照阶段排序
//        // 将假期按照阶段正序，优先返还比率低（其实就是请假扣钱多的）的假期余额
//        List<HrmsUserLeaveStageDetailDO> orthodoxUserLeaveStageDetailList = userLeaveStageDetailList.stream()
//                .sorted(Comparator.comparingInt(HrmsUserLeaveStageDetailDO::getLeaveMark)
//                        .thenComparing(HrmsUserLeaveStageDetailDO::getPercentSalary))
//                .collect(Collectors.toList());
//        // 遍历正序的假期详情信息数据
//        for (HrmsUserLeaveStageDetailDO stageDetailInfo : orthodoxUserLeaveStageDetailList) {
//            // 不是最后一个阶梯，并且有假期<=0
//            // 当前逻辑 ：1. 不是最后一个阶梯，判断假期余额是否小于等于0，如果小于等于0则跳过该假期阶梯。2. 如果是最后一个阶梯，直接使用最后一个阶梯
//            //if (i != reversedUserLeaveStageDetailList.size() - 1 && stageDetailInfo.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) < 1) {
//            //    continue;
//            //}
//
//            // 如果当前阶段所使用假期余额 <= 0，则表示无法扣减当前阶段已使用假期余额（也表示之前请假扣减假期余额、增加已使用假期余额，没有执行到该阶段假期）
//            if (stageDetailInfo.getLeaveUsedMinutes().compareTo(BigDecimal.ZERO) <= 0) {
//                continue;
//            }
//
//            /*
//                当前阶段(请假时长 - 已使用假期余额)
//                    大于0:该阶段已使用假期不足以返还用户全部请假时间，需要加上下一个阶段的已使用假期余额
//                    等于0:正好完全返还该阶段的请假时长
//                    小于0:可以完全返还该阶段的请假时长
//            */
//            BigDecimal usedDifference = totalLeaveTime.subtract(stageDetailInfo.getLeaveUsedMinutes());
//            if (usedDifference.compareTo(BigDecimal.ZERO) <= 0) {
//                stageDetailInfo.setLeaveUsedMinutes(stageDetailInfo.getLeaveUsedMinutes().subtract(totalLeaveTime));
//                stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().add(totalLeaveTime));
//                stageDetailInfo.setLastUpdDate(new Date());
//                stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
//                stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
//                userLeaveStageDetailInfoList.add(stageDetailInfo);
//                break;
//            }
//            // 提前存储该阶段假期的已使用余额，为后续循环扣减使用
//            BigDecimal subtractLeaveUsedMinutes = stageDetailInfo.getLeaveUsedMinutes();
//            // 大于0：直接该阶段假期余额 = 假期余额 + 已使用假期余额，已使用假期余额变成0
//            stageDetailInfo.setLeaveResidueMinutes(stageDetailInfo.getLeaveResidueMinutes().add(stageDetailInfo.getLeaveUsedMinutes()));
//            stageDetailInfo.setLeaveUsedMinutes(BigDecimal.ZERO);
//            stageDetailInfo.setLastUpdDate(new Date());
//            stageDetailInfo.setLastUpdUserCode(RequestInfoHolder.getUserCode());
//            stageDetailInfo.setLastUpdUserName(RequestInfoHolder.getUserName());
//            userLeaveStageDetailInfoList.add(stageDetailInfo);
//            // 更新请假时间，为后续循环准备
//            totalLeaveTime = totalLeaveTime.subtract(subtractLeaveUsedMinutes);
//        }
//
//        // 封装请假记录表
//        LeaveAddParam leaveAddParam = new LeaveAddParam();
//        leaveAddParam.setUserId(formInfo.getUserId());
//        leaveAddParam.setUserCode(formInfo.getUserCode());
//        if (CollectionUtils.isNotEmpty(configIdInfo) && StringUtils.isNotBlank(configIdInfo.get(0).getAttrValue())) {
//            leaveAddParam.setConfigId(Long.valueOf(configIdInfo.get(0).getAttrValue()));
//        }
//        leaveAddParam.setLeaveType(companyLeaveConfigDO.getLeaveType());
//        leaveAddParam.setLeaveName(leaveNameInfo.get(0).getAttrValue());
//        leaveAddParam.setLeaveStartDate(startDate);
//        leaveAddParam.setLeaveEndDate(endDate);
//
//        //查询假期结转配置，如果请假开始时间小于结转时间，当前时间大于等于结转时间，则代表当前假期已经结转，收入记录备注存在结转假期
//        //如果请假开始时间小于失效时间，当前时间大于等于失效时间，则代表当前结转假期已经失效，收入记录备注存在失效假期
//        Integer carryOverStatus = hrmsCompanyLeaveConfigCarryOverService.checkIfCarryOver(companyLeaveConfigDO.getId(), formInfo.getCreateDate(), companyLeaveConfigDO.getCountry());
//        switch (carryOverStatus) {
//            case 1:
//                remark = "【假期已经结转，存在部分假期不返还】 The holiday has been carried forward, and some data may not be returned";
//                break;
//            case 2:
//                remark = "【假期已经失效，存在部分假期不返还】 The holiday has expired, some data may not be returned";
//                break;
//            default:
//                break;
//        }
//
//        return buildUserLeaveRecord(userLeaveRecordTotalLeaveTime, leaveAddParam, LeaveTypeEnum.CANCEL.getCode(), remark);
//    }


//    /**
//     * 构建用户请假记录数据
//     *
//     * @param param  入参
//     * @param remark 备注
//     * @param type   请假类型
//     */
//    public HrmsUserLeaveRecordDO buildUserLeaveRecord(BigDecimal totalLeaveTime, LeaveAddParam param, String type, String remark) {
//        HrmsUserLeaveRecordDO userLeaveRecord = new HrmsUserLeaveRecordDO();
//        userLeaveRecord.setId(iHrmsIdWorker.nextId());
//        userLeaveRecord.setUserId(param.getUserId());
//        userLeaveRecord.setUserCode(param.getUserCode());
//        userLeaveRecord.setDate(new Date());
//        userLeaveRecord.setDayId(DateHelper.getDayId(new Date()));
//        userLeaveRecord.setConfigId(param.getConfigId());
//        userLeaveRecord.setLeaveName(param.getLeaveName());
//        userLeaveRecord.setLeaveType(param.getLeaveType());
//        userLeaveRecord.setType(type);
//        userLeaveRecord.setLeaveStartDay(param.getLeaveStartDate());
//        userLeaveRecord.setLeaveEndDay(param.getLeaveEndDate());
//        userLeaveRecord.setLeaveMinutes(totalLeaveTime);
//        userLeaveRecord.setRemark(remark);
//        BaseDOUtil.fillDOInsert(userLeaveRecord);
//        userLeaveRecord.setOperationUserCode(RequestInfoHolder.getUserCode());
//        userLeaveRecord.setOperationUserName(RequestInfoHolder.getUserName());
//        return userLeaveRecord;
//    }
}
