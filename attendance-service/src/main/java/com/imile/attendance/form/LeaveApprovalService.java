package com.imile.attendance.form;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.vacation.LeaveUnitEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.param.LeaveAddParam;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.user.vo.UserLeaveResidualVO;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/7
 * @Description 请假审批服务
 */
@Slf4j
@Service
public class LeaveApprovalService {

    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private UserLeaveService userLeaveService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private IdWorkUtils idWorkUtils;

    /**
     * 请假申请(点击新增按钮调用 新增/暂存)
     */
    //todo 需要关联异常表
//    public ApprovalResultVO leaveAdd(LeaveAddParam param) {
//        log.info("leaveAdd | LeaveAddParam :{}", JSON.toJSONString(param));
//        ApprovalResultVO resultVO = new ApprovalResultVO();
//        // 现在该接口不存在operationType = 2的情况，预览是新的接口了
//        if (param.getOperationType() == 2) {
//            return resultVO;
//        }
//        commonFormOperationService.userBaseInfoBuild(param, null, null);
//        //暂存不校验任何信息，直接落库成功，提交时校验
//        AttendanceFormDO formDO = new AttendanceFormDO();
//        List<AttendanceFormAttrDO> hrmsApplicationFormAttrDOArrayList = new ArrayList<>();
//        List<AttendanceFormRelationDO> hrmsApplicationFormRelationDOArrayList = new ArrayList<>();
//        HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO = new HrmsEmployeeAbnormalOperationRecordDO();
//        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
//        // 需要扣减假期余额、增加已使用余额的假期详情数据列表
//        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
//        // 用户请假记录数据
//        UserLeaveRecordDO userLeaveRecord = null;
//        if (param.getOperationType() == 1) {
//            //异常判断
//            abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
//            // 新增返回请假总时长-单位分钟
//            BigDecimal totalLeaveTime = leaveAddDataCheck(param);
//            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
//            // 创建用户请假详情信息数据
//            commonFormOperationService.buildUserLeaveStageDetailList(userLeaveStageDetailList, param, totalLeaveTime);
//            // 创建用户请假记录数据
//            userLeaveRecord = commonFormOperationService.buildUserLeaveRecord(totalLeaveTime, param, LeaveTypeEnum.LEAVE.getCode(), "【hrms或clover】新增请假申请入口");
//        }
//        //注意，不仅需要构建审批表信息，正常考勤表也需要落库，状态为未生效,注意假期比例也是为空，只有审批通过，才会扣除假期
//        this.leaveDataAddBuild(param, formDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
//        //暂存不需要调用bpm
//        if (param.getOperationType() == 0) {
//            //暂存不用落正常考勤表/异常表
//            hrmsAttendanceApprovalManage.formAdd(formDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, null, null, userLeaveStageDetailList, null);
//            return resultVO;
//        }
//        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
//        leaveAddApprovalDataBuild(initInfoApiDTO, formDO, hrmsApplicationFormAttrDOArrayList);
//        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmCreateApprovalService.addApprovalInfo(initInfoApiDTO);
//        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
//        hrmsAttendanceApprovalManage.formAdd(formDO, hrmsApplicationFormRelationDOArrayList, hrmsApplicationFormAttrDOArrayList, hrmsEmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, userLeaveRecord);
//        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
//        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
//        return resultVO;
//    }
    private BigDecimal leaveAddDataCheck(LeaveAddParam param) {
        if (StringUtils.isBlank(param.getLeaveName())) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_NAME_IS_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEAVE_NAME_IS_NOT_EMPTY.getDesc()));
        }
        if (param.getLeaveStartDate() == null) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEAVE_START_DATE_NOT_EMPTY.getDesc()));
        }
        if (param.getLeaveEndDate() == null) {
            throw BusinessException.get(ErrorCodeEnum.LEAVE_END_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEAVE_END_DATE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isEmpty(param.getRemark())) {
            throw BusinessException.get(ErrorCodeEnum.REMARK_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.REMARK_NOT_EMPTY.getDesc()));
        }
        param.setDayAttendanceHours(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
        //查询是否超过请假时间
        Date nowDate = new Date();
        // 考勤周期
        AttendanceCycleConfigDO userAttendanceCycleConfig = attendanceCycleConfigService.getUserAttendanceCycleConfig(param.getUserId());
        attendanceCycleConfigService.confirmCycleCheck(nowDate, userAttendanceCycleConfig, param.getLeaveStartDate());

        // 查询该假期配置
        // 修改为通过人员范围查询假期配置
        CompanyLeaveConfigDO companyLeaveConfig = companyLeaveConfigService.getById(param.getConfigId());
        param.setLeaveUnit(companyLeaveConfig.getLeaveUnit());
        param.setMiniLeaveDuration(companyLeaveConfig.getMiniLeaveDuration());
        param.setLeaveShortName(companyLeaveConfig.getLeaveShortName());
        param.setIsUploadAttachment(companyLeaveConfig.getIsUploadAttachment());
        param.setUploadAttachmentCondition(companyLeaveConfig.getUploadAttachmentCondition());
        param.setAttachmentUnit(companyLeaveConfig.getAttachmentUnit());

        // 查询该用户假期，获取用户该假期剩余可用余额
        List<UserLeaveResidualVO> userLeaveResidualVOS = userLeaveService.selectUserLeaveResidual(param.getUserId());
        List<UserLeaveResidualVO> userLeaveList = userLeaveResidualVOS.stream()
                .filter(item -> Objects.equals(item.getConfigId(), param.getConfigId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userLeaveList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_THIS_LEAVE_TYPE.getDesc()));
        }
        param.setLeaveResidueMinutes(userLeaveList.get(0).getLeaveResidueMinutes());

        //判断是否还有冲突
        List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
        commonFormOperationService.selectClashApplication(param.getUserId(), param.getLeaveStartDate(), param.getLeaveEndDate(), clashApplicationInfoDTOList);
        if (CollectionUtils.isNotEmpty(clashApplicationInfoDTOList)) {
            throw BusinessException.get(ErrorCodeEnum.EXIST_CLASH_TIME_PERIOD.getCode(), I18nUtils.getMessage(ErrorCodeEnum.EXIST_CLASH_TIME_PERIOD.getDesc()));
        }
        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        commonFormOperationService.dayDurationInfoHandler(param.getUserId(), param.getLeaveStartDate(), param.getLeaveEndDate(), companyLeaveConfig, dayDurationInfoDTOList, param.getDayAttendanceHours());
        //过滤不占用请假时间的日期
        dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                        || item.getHours().compareTo(BigDecimal.ZERO) > 0
                        || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        param.setDayDurationInfoDTOList(dayDurationInfoDTOList);
        // 计算请假总时长-单位分钟
        BigDecimal totalLeaveTime = handlerLeaveTotalTime(dayDurationInfoDTOList);
        // 校验最小请假时长及附件条件
        checkLeaveCondition(param, dayDurationInfoDTOList);
        // 校验请假时长与请假余额
        checkLeaveTime(param, userLeaveList, totalLeaveTime);

        return totalLeaveTime;
    }

    /**
     * 获取请假总时长-单位分钟
     *
     * @param dayDurationInfoList 每天请假时间
     */
    private BigDecimal handlerLeaveTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历每一天的请假时长
        for (DayDurationInfoDTO dayDurationInfo : dayDurationInfoList) {

            // 计算一天的请假时长-分钟（天 * 法定工作时长 8h * 一小时的分钟数）【这里面的法定时长为什么给8h：因为，假期给的时候一天是按照8h给，所以请一天假，需要扣假期8h】
            totalMinutes = totalMinutes.add(dayDurationInfo.getDays().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES));

            // 换算之后的小时数【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
            BigDecimal realHours = dayDurationInfo.getHours().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP);
            // 计算小时的请假时长-分钟（小时 * 一小时的分钟数）
            totalMinutes = totalMinutes.add(realHours.multiply(BusinessConstant.MINUTES));

            /*
                 计算分钟的请假时长-分钟【这边要根据实际工作时长来换算成8h的时间】【保留两位小数，四舍五入】
                    算法1：分钟/60min/工作时长h * 8h = 分钟转换成8h的分钟数
                    //BigDecimal realMinutesHours = dayDurationInfo.getMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP).divide(dayDurationInfo.getLegalWorkingHours(), 2, RoundingMode.HALF_UP).multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
                    算法2：分钟 * 8h * 60min / 工作时长h * 60min
                    算法1 存在很大精度问题，因为有多次除法，每一次除法都会保留两位小数，然后四舍五入，所以会存在精度问题
                    算法2 只有一次除法，所以精度问题 小很多
            */

            BigDecimal realMinutes = dayDurationInfo.getMinutes().multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES).divide(dayDurationInfo.getLegalWorkingHours().multiply(BusinessConstant.MINUTES), 2, RoundingMode.HALF_UP);

            totalMinutes = totalMinutes.add(realMinutes);
        }
        return totalMinutes;
    }

    /**
     * 请假天数校验
     *
     * @param param
     * @param dayDurationInfoDTOList
     */
    private static void checkLeaveCondition(LeaveAddParam param,
                                            List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //校验天假不能请假小于1天
        if (CollectionUtils.isEmpty(dayDurationInfoDTOList)) {
            return;
        }
        // 获取请假单位及最小请假时长
        String leaveUnit = param.getLeaveUnit();
        Integer miniLeaveDuration = Objects.isNull(param.getMiniLeaveDuration())
                ? BusinessConstant.ZERO : param.getMiniLeaveDuration();
        BigDecimal totalDays = BigDecimal.ZERO;
        BigDecimal totalHours = BigDecimal.ZERO;
        BigDecimal totalMinutes = BigDecimal.ZERO;
        for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
            // 总天数
            totalDays = totalDays.add(dayDurationInfoDTO.getDays());
            BigDecimal legalWorkingHours = dayDurationInfoDTO.getLegalWorkingHours();
            // 总小时数
            BigDecimal dayHours = dayDurationInfoDTO.getDays().multiply(legalWorkingHours)
                    .add(dayDurationInfoDTO.getHours());
            totalHours = totalHours.add(dayHours);
            // 总分钟数
            BigDecimal dayMinutes = dayHours.multiply(BusinessConstant.MINUTES).add(dayDurationInfoDTO.getMinutes());
            totalMinutes = totalMinutes.add(dayMinutes);
            // 天假特殊校验 不能小于一天
            if (LeaveUnitEnum.DAYS.getCode().equals(leaveUnit) && dayDurationInfoDTO.getDays().compareTo(BigDecimal.ZERO) == 0) {
                throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DAY_CANNOT_BE_ZERO);
            }
        }
        // 校验请假时长必须大于最小请假时长
        // 最小请假单位是天
        if (LeaveUnitEnum.DAYS.getCode().equals(leaveUnit)
                && totalDays.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 最小请假单位为小时
        if (LeaveUnitEnum.HOURS.getCode().equals(leaveUnit)
                && totalHours.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 最小请假单位为分钟
        if (LeaveUnitEnum.MINUTES.getCode().equals(leaveUnit)
                && totalMinutes.intValue() < miniLeaveDuration) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_DURATION_CANNOT_BE_LESS_THAN_MINI);
        }
        // 校验附件是否必填
        if (!BusinessConstant.Y.equals(param.getIsUploadAttachment())) {
            return;
        }
        // 获取附件单位
        String attachmentUnit = param.getAttachmentUnit();
        // 上传附件单位是天
        if (LeaveUnitEnum.DAYS.getCode().equals(attachmentUnit)
                && totalDays.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTACHMENT_NOT_EMPTY);
        }
        // 上传附件单位为小时
        if (LeaveUnitEnum.HOURS.getCode().equals(attachmentUnit)
                && totalHours.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTACHMENT_NOT_EMPTY);
        }
        // 上传附件单位为分钟
        if (LeaveUnitEnum.MINUTES.getCode().equals(attachmentUnit)
                && totalMinutes.longValue() >= param.getUploadAttachmentCondition()
                && CollectionUtils.isEmpty(param.getAttachmentList())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTACHMENT_NOT_EMPTY);
        }

    }

    /**
     * 校验请假时长与余额
     *
     * @param param          入参
     * @param userLeaveList  用户假期列表
     * @param totalLeaveTime 请假总时长
     */
    private static void checkLeaveTime(LeaveAddParam param,
                                       List<UserLeaveResidualVO> userLeaveList,
                                       BigDecimal totalLeaveTime) {
        // 请假时长不能为0
        if (totalLeaveTime.compareTo(BigDecimal.ZERO) == 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.LEAVE_TIME_CANNOT_BE_ZERO);
        }
        // 获取请假时长
        long leaveTime = DateUtil.between(param.getLeaveStartDate(), param.getLeaveEndDate(), DateUnit.MINUTE, false);
        // 请假结束时间必须在开始时间之后
        if (leaveTime < 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_LEAVE_END_TIME_MUST_BE_AFTER_THE_START_TIME);
        }

        // 1. 过滤假期余额小于等于0的历史数据： 不能请假
        if (userLeaveList.get(0).getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_LEAVE_BALANCE_IS_INSUFFICIENT);
        }
        // 过滤完1. 在过滤请假余额不能小于请假时长，才会准确
        if (userLeaveList.get(0).getLeaveResidueMinutes().compareTo(totalLeaveTime) < 0) {
            throw BusinessLogicException.getException(ErrorCodeEnum.THE_BALANCE_OF_LEAVE_CANNOT_BE_LESS_THAN_THE_LENGTH_OF_LEAVE);
        }
    }

//    private void leaveDataAddBuild(LeaveAddParam param,
//                                   AttendanceFormDO formDO,
//                                   List<AttendanceFormRelationDO> hrmsApplicationFormRelationDOArrayList,
//                                   List<AttendanceFormAttrDO> hrmsApplicationFormAttrDOArrayList,
//                                   HrmsEmployeeAbnormalOperationRecordDO hrmsEmployeeAbnormalOperationRecordDO,
//                                   HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
//        formDO.setId(IdWorkerUtil.getId());
//        formDO.setApplyUserId(param.getApplyUserId());
//        formDO.setUserId(param.getUserId());
//        formDO.setUserCode(param.getUserCode());
//        formDO.setUserName(param.getUserName());
//        formDO.setDeptId(param.getDeptId());
//        formDO.setPostId(param.getPostId());
//        formDO.setCountry(param.getCountry());
//        formDO.setOriginCountry(param.getOriginCountry());
//        formDO.setIsWarehouseStaff(param.getIsWarehouseStaff());
//        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.LEAVE));
//        formDO.setFormType(ApplicationFormTypeEnum.LEAVE.getCode());
//        if (StringUtils.isBlank(formDO.getFormStatus())) {
//            //为暂存
//            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
//        }
//        BaseDOUtil.fillDOInsert(formDO);
//        if (Objects.nonNull(param.getConfigId())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.configID.getLowerCode(), String.valueOf(param.getConfigId())));
//        }
//        if (StringUtils.isNotBlank(param.getLeaveName())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode(), param.getLeaveName()));
//        }
//        if (StringUtils.isNotBlank(param.getLeaveShortName())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveShortName.getLowerCode(), param.getLeaveShortName()));
//        }
//        if (StringUtils.isNotBlank(param.getLeaveUnit())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveUnit.getLowerCode(), param.getLeaveUnit()));
//        }
//        if (param.getLeaveStartDate() != null) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode(), DateUtil.format(param.getLeaveStartDate(), "yyyy-MM-dd HH:mm:ss")));
//        }
//        if (param.getLeaveEndDate() != null) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode(), DateUtil.format(param.getLeaveEndDate(), "yyyy-MM-dd HH:mm:ss")));
//        }
//        if (param.getLeaveResidueMinutes() != null) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.leaveResidueMinutes.getLowerCode(), param.getLeaveResidueMinutes().toString()));
//        }
//        if (StringUtils.isNotBlank(param.getRemark())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
//        }
//        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
//        }
//        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
//            hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
//        }
//
//        //默认是没有被撤销
//        hrmsApplicationFormAttrDOArrayList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));
//
//        //把异常ID也关联下
//        if (param.getAbnormalId() != null) {
//            AttendanceFormRelationDO relationDO = new AttendanceFormRelationDO();
//            relationDO.setId(IdWorkerUtil.getId());
//            relationDO.setFormId(formDO.getId());
//            relationDO.setRelationId(param.getAbnormalId());
//            relationDO.setRelationType(ApplicationRelationTypeEnum.ABNORMAL.getCode());
//            BaseDOUtil.fillDOInsert(relationDO);
//            hrmsApplicationFormRelationDOArrayList.add(relationDO);
//
//            hrmsEmployeeAbnormalOperationRecordDO.setId(IdWorkerUtil.getId());
//            hrmsEmployeeAbnormalOperationRecordDO.setFormId(formDO.getId());
//            hrmsEmployeeAbnormalOperationRecordDO.setAbnormalId(param.getAbnormalId());
//            hrmsEmployeeAbnormalOperationRecordDO.setOperationType(AttendanceAbnormalOperationTypeEnum.LEAVE.getCode());
//            BaseDOUtil.fillDOInsert(formDO);
//        }
//
//        if (abnormalAttendanceDO != null) {
//            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
//            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
//        }
//
//    }

}
