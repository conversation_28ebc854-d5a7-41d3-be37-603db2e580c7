package com.imile.attendance.form.param;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class AttendanceApprovalInfoParam extends ResourceQuery {
    /**
     * 被申请人姓名/账号
     */
    private String userCodeOrName;

    /**
     * 被申请人部门
     */
    private Long deptId;


    /**
     * 被申请人部门列表
     */
    private List<Long> deptIds;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 单据编码(模糊查询)
     */
    private String applicationFormCode;

    /**
     * 单据状态
     */
    private String formStatus;

    private String country;

    /**
     * 考勤审批类型
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<String> formTypeList;

    /**
     * 页面操作来源  HRMS CLOVER ATTENDANCE
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String dataSource;


    /**
     * 导出使用
     */
    private String deptIdString;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 补卡类型
     */
    private String reissueCardType;

}
