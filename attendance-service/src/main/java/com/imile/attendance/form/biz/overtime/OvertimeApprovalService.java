package com.imile.attendance.form.biz.overtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ApprovalNoPrefixEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.approval.OverTimeCustomFieldEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.AttendanceApprovalFormManage;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.overtime.param.OverTimeAddParam;
import com.imile.attendance.form.biz.overtime.param.OverTimeListParam;
import com.imile.attendance.form.biz.overtime.param.UserOverTimeAddParam;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFromDetailVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFromListVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFromVO;
import com.imile.attendance.form.bo.AttendanceApprovalFormDetailBO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.UserAuthDTO;
import com.imile.attendance.form.mapstruct.AttendanceFormMapstruct;
import com.imile.attendance.form.param.UserAuthParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormUserInfoDao;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormUserInfoQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.PageUtil;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/15
 * @Description 加班审批服务
 */
@Slf4j
@Service
public class OvertimeApprovalService {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private RpcBpmApprovalClient bpmApprovalClient;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private AttendanceApprovalFormManage formManage;
    @Resource
    private AttendanceApprovalFormDao approvalFormDao;
    @Resource
    private AttendanceApprovalFormUserInfoDao approvalFormUserInfoDao;
    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private EmployeePunchRecordService employeePunchRecordService;
    @Resource
    private IdWorkUtils idWorkUtils;

    /**
     * 加班列表
     *
     * @param param
     * @return
     */
    public PaginationResult<OverTimeApprovalFromListVO> list(OverTimeListParam param) {
        log.info("list | param :{}", JSON.toJSONString(param));
        // 设置权限
        OverTimeListQuery query = buildQuery(param);
        // 构建查询参数
        ApprovalFormQuery approvalFormQuery = buildApprovalFormQuery(query);
        if (Objects.isNull(approvalFormQuery)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        Page<AttendanceApprovalFormDO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<AttendanceApprovalFormDO> pageInfo = page.doSelectPageInfo(() -> approvalFormDao.selectByCondition(approvalFormQuery));
        List<AttendanceApprovalFormDO> approvalFormList = pageInfo.getList();
        if (CollectionUtils.isEmpty(approvalFormList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<String> applyUserCodeList = approvalFormList.stream().map(AttendanceApprovalFormDO::getApplyUserCode).collect(Collectors.toList());
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(applyUserCodeList);
        // 将userInfoList按照userCode 转为map
        Map<String, AttendanceUser> userCodeMap = userInfoList.stream().collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));

        List<OverTimeApprovalFromListVO> targetList = Lists.newArrayList();
        for (AttendanceApprovalFormDO approvalForm : approvalFormList) {
            OverTimeApprovalFromListVO overTimeApprovalFromList = BeanUtils.convert(approvalForm, OverTimeApprovalFromListVO.class);

            AttendanceUser userInfo = userCodeMap.get(approvalForm.getApplyUserCode());
            if (ObjectUtil.isNotNull(userInfo)) {
                overTimeApprovalFromList.setApplyUserName(userInfo.getUserName());
            }
            targetList.add(overTimeApprovalFromList);
        }
        PageInfo<OverTimeApprovalFromListVO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(targetList);

        return PageUtil.getPageResult(targetList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 加班列表(clover)
     *
     * @param param
     * @return
     */
    public PaginationResult<OverTimeApprovalListDTO> cloverList(OverTimeListParam param) {
        OverTimeListQuery query = buildQuery(param);

        Page<OverTimeApprovalListDTO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<OverTimeApprovalListDTO> pageInfo = page.doSelectPageInfo(() -> formManage.selectListByCondition(query));

        List<OverTimeApprovalListDTO> pageInfoList = pageInfo.getList();
        if (CollUtil.isEmpty(pageInfoList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<Long> deptIdList = pageInfoList
                .stream()
                .map(item -> item.getDeptId())
                .distinct()
                .collect(Collectors.toList());
        List<AttendanceDept> deptDOList = deptService.selectDeptByIds(deptIdList);
        Map<Long, List<AttendanceDept>> deptIdMap = deptDOList
                .stream()
                .collect(Collectors.groupingBy(AttendanceDept::getId));

        List<Long> postIdList = pageInfo.getList()
                .stream()
                .map(item -> item.getPostId())
                .distinct()
                .collect(Collectors.toList());
        List<AttendancePost> entPostDOList = postService.listByPostList(postIdList);
        Map<Long, List<AttendancePost>> postIdMap = entPostDOList.stream().collect(Collectors.groupingBy(AttendancePost::getId));

        for (OverTimeApprovalListDTO listDTO : pageInfoList) {

            List<AttendanceDept> userDeptList = deptIdMap.get(listDTO.getDeptId());
            if (CollectionUtils.isNotEmpty(userDeptList)) {
                listDTO.setDeptName(RequestInfoHolder.isChinese()
                        ? userDeptList.get(0).getDeptNameCn()
                        : userDeptList.get(0).getDeptNameEn());
            }

            List<AttendancePost> userPostList = postIdMap.get(listDTO.getPostId());
            if (CollectionUtils.isNotEmpty(userPostList)) {
                listDTO.setPostName(RequestInfoHolder.isChinese()
                        ? userPostList.get(0).getPostNameCn()
                        : userPostList.get(0).getPostNameEn());
            }

            if (StringUtils.isNotBlank(listDTO.getFormStatus())) {
                FormStatusEnum statusEnum = FormStatusEnum.getInstance(listDTO.getFormStatus());
                listDTO.setFormStatusDesc(Objects.isNull(statusEnum) ? "" : statusEnum.getDesc());
            }

            listDTO.setApplicationFormId(listDTO.getId());
            listDTO.setEstimateDuration(Objects.isNull(listDTO.getEstimateDuration()) ? null : listDTO.getEstimateDuration().divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
        }

        return PageUtil.getPageResult(pageInfoList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 构建查询条件
     *
     * @param param 入参
     * @return OverTimeListQuery
     */
    private OverTimeListQuery buildQuery(OverTimeListParam param) {
        OverTimeListQuery query = BeanUtils.convert(param, OverTimeListQuery.class);
        if (ObjectUtil.equal(param.getSystemSource(), "HRMS")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = commonFormOperationService.userDeptAuthList(userAuthParam);
            if (!this.checkDeptAuth(query, userAuthDTO)) {
                return null;
            }
        }
        if (ObjectUtil.equal(param.getSystemSource(), "CLOVER")) {
            query.setUserCode(RequestInfoHolder.getUserCode());
        }

        return query;
    }

    /**
     * 构建查询条件
     *
     * @param query 入参
     * @return ApprovalFormQuery
     */
    private ApprovalFormQuery buildApprovalFormQuery(OverTimeListQuery query) {
        if (Objects.isNull(query)) {
            return null;
        }
        ApprovalFormQuery approvalFormQuery = AttendanceFormMapstruct.INSTANCE.mapQueryToApprovalFormQuery(query);

        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfo = formManage.selectByCondition(query);
        if (CollectionUtils.isEmpty(approvalFormUserInfo)) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(approvalFormUserInfo)) {
            List<Long> formIdList = approvalFormUserInfo
                    .stream()
                    .map(AttendanceApprovalFormUserInfoDO::getFormId)
                    .distinct()
                    .collect(Collectors.toList());
            approvalFormQuery.setFormIdList(formIdList);
        }
        return approvalFormQuery;
    }

    /**
     * 加班申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO overTimeAdd(OverTimeAddParam param) {
        log.info("overTimeAdd | overTimeAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1.构建基本信息
        UserOverTimeAddParam userOverTimeAddParam = this.userBaseInfoBuild(param);

        AttendanceApprovalFormDO formDO = new AttendanceApprovalFormDO();
        List<AttendanceApprovalFormUserInfoDO> formUserInfoList = Lists.newArrayList();

        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 1)) {
            // 2.保存校验
            overTimeAddDataCheck(userOverTimeAddParam);
            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        // 3.构建单据对象
        this.overTimeDataAddBuild(userOverTimeAddParam, formDO, formUserInfoList);

        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 0)) {
            // 暂存功能，预留，后续如果需要可添加逻辑
            attendanceApprovalManage.approvalFormAdd(formDO, formUserInfoList);
            return resultVO;
        }
        // 4.构建审批信息
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.overTimeAddApprovalDataBuild(initInfoApiDTO, formDO, formUserInfoList, userOverTimeAddParam);
        // 5.创建审批流
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 6.落库
        attendanceApprovalManage.approvalFormAdd(formDO, formUserInfoList);
        // 设置返回值
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 加班申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO overTimeUpdate(OverTimeAddParam param) {
        log.info("overTimeUpdate | overTimeUpdateParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1.构建用户基本信息
        UserOverTimeAddParam userOverTimeAddParam = this.userBaseInfoBuild(param);
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(param.getFormId());
        AttendanceApprovalFormDO approvalForm = approvalFormDetail.getApprovalForm();
        if (ObjectUtil.isNull(approvalForm)) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }

        List<AttendanceApprovalFormUserInfoDO> formUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();
        // 2.保存
        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 1)) {
            // 保存功能
            this.overTimeAddDataCheck(userOverTimeAddParam);
            approvalForm.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        // 构建对象
        this.overTimeDataUpdateBuild(userOverTimeAddParam, approvalForm, formUserInfoList);

        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 0)) {
            // 暂存功能，预留，后续如果需要可添加逻辑
            attendanceApprovalManage.approvalFormAddAndUpdate(approvalForm, formUserInfoList);
            return resultVO;
        }
        // 构建审批信息
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        List<AttendanceApprovalFormUserInfoDO> formUserInfoList_effect = formUserInfoList
                .stream()
                .filter(item -> IsDeleteEnum.NO.getCode() == item.getIsDelete())
                .collect(Collectors.toList());
        this.overTimeAddApprovalDataBuild(initInfoApiDTO, approvalForm, formUserInfoList_effect, userOverTimeAddParam);
        //本次保存是否是驳回后重提
        if (approvalForm.getApprovalId() != null
                && StringUtils.isNotBlank(approvalForm.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(approvalForm.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null
                    && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(approvalForm.getApprovalId());
            }
        }
        // 创建审批流
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        approvalForm.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 落库
        attendanceApprovalManage.approvalFormAddAndUpdate(approvalForm, formUserInfoList);
        // 设置返回值
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 加班详情
     *
     * @param approvalFormId
     * @return
     */
    public OverTimeApprovalFromVO getApprovalFromDetail(Long approvalFormId) {
        log.info("getApprovalFromDetail | approvalFormId :{}", approvalFormId);
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(approvalFormId);
        AttendanceApprovalFormDO approvalForm = approvalFormDetail.getApprovalForm();
        if (ObjectUtil.isNull(approvalForm)) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();
        if (CollectionUtils.isEmpty(approvalFormUserInfoList)) {
            approvalFormUserInfoList = Lists.newArrayList();
        }
        for (AttendanceApprovalFormUserInfoDO formUserInfoDO : approvalFormUserInfoList) {
            if (Objects.isNull(formUserInfoDO.getEstimateDuration())) continue;
            formUserInfoDO.setEstimateDuration(formUserInfoDO.getEstimateDuration().divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
        }
        // 获取所有的用户code
        List<String> userCodeList = approvalFormUserInfoList
                .stream()
                .map(AttendanceApprovalFormUserInfoDO::getUserCode)
                .collect(Collectors.toList());
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(userCodeList);
        Map<String, AttendanceUser> userCodeMap = userInfoList
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));
        // 获取部门信息
        List<Long> deptIdList = userInfoList
                .stream()
                .map(AttendanceUser::getDeptId)
                .collect(Collectors.toList());
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
        Map<Long, AttendanceDept> deptMap = deptList.stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
        // 获取岗位信息
        List<Long> postIdList = userInfoList
                .stream()
                .map(AttendanceUser::getPostId)
                .collect(Collectors.toList());
        List<AttendancePost> postList = postService.listByPostList(postIdList);
        Map<Long, AttendancePost> postMap = postList
                .stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

        // 获取人员绑定的打卡规则
        // TODO 获取打卡规则
//        List<Long> userIdList = userInfoList.stream().map(AttendanceUser::getId).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigRangeDO> punchConfigRangeList = punchConfigDaoFacade.getRangeAdapter().selectConfigRangeByBizId(userIdList);
//        // 将punchConfigRangeList按照bizId转为map
//        Map<Long, HrmsAttendancePunchConfigRangeDO> punchConfigRangeMap = punchConfigRangeList.stream().collect(Collectors.toMap(HrmsAttendancePunchConfigRangeDO::getBizId, Function.identity()));
//        List<Long> punchConfigIdList = punchConfigRangeList.stream().map(HrmsAttendancePunchConfigRangeDO::getPunchConfigId).collect(Collectors.toList());
//        List<HrmsAttendancePunchConfigDO> punchConfigList = punchConfigDaoFacade.getConfigAdapter().selectPunchConfigByIdList(punchConfigIdList);
//        // 将punchConfigList转为map
//        Map<Long, HrmsAttendancePunchConfigDO> punchConfigMap = punchConfigList.stream().collect(Collectors.toMap(HrmsAttendancePunchConfigDO::getId, Function.identity()));


        // 封装数据
        OverTimeApprovalFromVO approvalFromVo = BeanUtils.convert(approvalForm, OverTimeApprovalFromVO.class);
        List<OverTimeApprovalFromDetailVO> detail = Lists.newArrayList();
        String remark = "";
        for (AttendanceApprovalFormUserInfoDO formUserInfo : approvalFormUserInfoList) {
            // 获取打卡记录
            EmployeePunchCardRecordQuery employeePunchCardRecordQuery = EmployeePunchCardRecordQuery
                    .builder()
                    .userCode(String.valueOf(formUserInfo.getDayId()))
                    .dayId(String.valueOf(formUserInfo.getDayId()))
                    .build();
            List<EmployeePunchRecordDO> employeePunchRecordList = employeePunchRecordService.listRecords(employeePunchCardRecordQuery);

            AttendanceUser userInfo = userCodeMap.get(formUserInfo.getUserCode());
            OverTimeApprovalFromDetailVO fromDetailVo = BeanUtils.convert(formUserInfo, OverTimeApprovalFromDetailVO.class);
            remark = formUserInfo.getRemark();
            if (ObjectUtil.isNotNull(userInfo)) {
                boolean chinese = RequestInfoHolder.isChinese();
                fromDetailVo.setUserId(userInfo.getId());
                fromDetailVo.setUserName(chinese ? userInfo.getUserName() : userInfo.getUserNameEn());
                fromDetailVo.setDeptId(userInfo.getDeptId());
                AttendanceDept entDept = deptMap.get(userInfo.getDeptId());
                if (ObjectUtil.isNotNull(entDept)) {
                    fromDetailVo.setDeptName(chinese ? entDept.getDeptNameCn() : entDept.getDeptNameEn());
                }
                fromDetailVo.setPostId(userInfo.getPostId());
                AttendancePost entPost = postMap.get(userInfo.getPostId());
                if (ObjectUtil.isNotNull(entPost)) {
                    fromDetailVo.setPostName(chinese ? entPost.getPostNameCn() : entPost.getPostNameEn());
                }
                // TODO 查询打卡规则 打卡规则范围
//                HrmsAttendancePunchConfigRangeDO punchConfigRange = punchConfigRangeMap.get(userInfo.getId());
//                // 打卡规则
//                if (ObjectUtil.isNotNull(punchConfigRange)) {
//                    HrmsAttendancePunchConfigDO punchConfig = punchConfigMap.get(punchConfigRange.getPunchConfigId());
//                    if (ObjectUtil.isNotNull(punchConfig)) {
//                        String overtimeConfig = punchConfig.getOvertimeConfig();
//                        HrmsAttendancePunchOverTimeConfigDTO attendancePunchOverTimeConfig = JSONObject.parseObject(overtimeConfig, HrmsAttendancePunchOverTimeConfigDTO.class);
//                        if (ObjectUtil.isNotNull(attendancePunchOverTimeConfig)) {
//                            fromDetailVo.setWorkingOutStartTime(attendancePunchOverTimeConfig.getWorkingOutStartTime());
//
//                            fromDetailVo.setWorkingEffectiveTime(attendancePunchOverTimeConfig.getWorkingEffectiveTime());
//                            fromDetailVo.setWorkingSubsidyType(attendancePunchOverTimeConfig.getWorkingSubsidyType());
//
//                            fromDetailVo.setRestEffectiveTime(attendancePunchOverTimeConfig.getRestEffectiveTime());
//                            fromDetailVo.setRestSubsidyType(attendancePunchOverTimeConfig.getRestSubsidyType());
//
//                            fromDetailVo.setHolidayEffectiveTime(attendancePunchOverTimeConfig.getHolidayEffectiveTime());
//                            fromDetailVo.setHolidaySubsidyType(attendancePunchOverTimeConfig.getHolidaySubsidyType());
//                        }
//                    }
//                }
                // 打卡记录
                if (CollectionUtils.isNotEmpty(employeePunchRecordList)) {
                    if (employeePunchRecordList.size() > 1) {
                        // 获取最早最晚打卡记录
                        // 将employeePunchRecordList按照打卡时间正序
                        employeePunchRecordList.sort((o1, o2) -> o1.getPunchTime().compareTo(o2.getPunchTime()));
                        EmployeePunchRecordDO earliestEmployeePunchRecord = employeePunchRecordList.get(0);
                        EmployeePunchRecordDO latestEmployeePunchRecord = employeePunchRecordList.get(employeePunchRecordList.size() - 1);
                        if (ObjectUtil.isNotNull(earliestEmployeePunchRecord)) {
                            fromDetailVo.setEarliestPunchTime(DateUtil.format(earliestEmployeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                        }
                        if (ObjectUtil.isNotNull(latestEmployeePunchRecord)) {
                            fromDetailVo.setLatestPunchTime(DateUtil.format(latestEmployeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                        }
                    } else {
                        EmployeePunchRecordDO employeePunchRecord = employeePunchRecordList.get(0);
                        if (ObjectUtil.isNotNull(employeePunchRecord)) {
                            fromDetailVo.setEarliestPunchTime(DateUtil.format(employeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                            fromDetailVo.setLatestPunchTime(DateUtil.format(employeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                        }
                    }
                }
            }
            detail.add(fromDetailVo);
        }
        approvalFromVo.setRemark(remark);
        approvalFromVo.setDetail(detail);

        return approvalFromVo;
    }

    /**
     * 加班预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> overtimePreview(OverTimeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        //校验必填数据
        UserOverTimeAddParam userOverTimeAddParam = userBaseInfoBuild(param);
        overTimeAddDataCheck(userOverTimeAddParam);
        AttendanceApprovalFormDO formDO = new AttendanceApprovalFormDO();
        List<AttendanceApprovalFormUserInfoDO> hrmsApprovalFormUserInfoList = Lists.newArrayList();
        //暂存不校验任何信息，直接落库成功，提交时校验
        overTimeDataAddBuild(userOverTimeAddParam, formDO, hrmsApprovalFormUserInfoList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        overTimeAddApprovalDataBuild(initInfoApiDTO, formDO, hrmsApprovalFormUserInfoList, userOverTimeAddParam);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    /**
     * 加班取消
     *
     * @param formId
     */
    public void cancel(Long formId) {
        //查询当前单据是否存在
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(formId);
        AttendanceApprovalFormDO formDO = approvalFormDetail.getApprovalForm();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //非这些状态的单据不处理
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.IN_REVIEW.getCode(), formDO.getFormStatus())
                && !StringUtils.equalsIgnoreCase(FormStatusEnum.REJECT.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.FORM_STATUS_NOT_CANCEL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.FORM_STATUS_NOT_CANCEL.getDesc()));
        }
        bpmApprovalClient.backApply(formDO.getApprovalId());
        formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
        BaseDOUtil.fillDOUpdate(formDO);

        attendanceApprovalManage.approvalFormUpdate(formDO, null);
    }

    /**
     * 加班删除
     *
     * @param formId
     */
    public void delete(Long formId) {
        //查询当前单据是否存在
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(formId);
        AttendanceApprovalFormDO formDO = approvalFormDetail.getApprovalForm();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //只有暂存状态的单据可以被删除
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.STAGING.getCode(),
                formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getDesc()));
        }
        formDO.setIsDelete(BusinessConstant.Y);
        BaseDOUtil.fillDOUpdate(formDO);
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();
        approvalFormUserInfoList.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(item);
        });
        attendanceApprovalManage.approvalFormUpdate(formDO, approvalFormUserInfoList);
    }

    /**
     * 保存功能校验
     *
     * @param param 入参
     */
    private void overTimeAddDataCheck(UserOverTimeAddParam param) {
        // 1. 校验该用户加班当天是不是工作日，如果是工作日，则不允许提加班申请
        log.info("userId：{}", param.getUserId());
        // TODO 查询员工排班
//        List<UserClassConfigDO> userClassConfigDOList = userClassConfigAdapter.selectRecordByDayListNew(param.getUserId(), Collections.singletonList(param.getDayId()));
//        if (CollectionUtils.isNotEmpty(userClassConfigDOList)) {
//            // 这里查询的是一天的数据，每个人一天只会存在一个排班
//            UserClassConfigDO userClassConfigDO = userClassConfigDOList.get(0);
//            log.info("day_id：{},班次为：{}", param.getDayId(), userClassConfigDO.getDayPunchType());
//            if (ObjectUtil.isNotNull(userClassConfigDO) && ObjectUtil.isNotNull(userClassConfigDO.getClassId())) {
//                // 说明排班了，不能提加班申请
//                throw BusinessLogicException.getException(ErrorCodeEnum.OVER_TIME_ON_WEEKDAYS_ERROR);
//            }
//        }
        // 2. 校验当天是否提交过加班申请
        ApprovalFormUserInfoQuery approvalFormUserInfoQuery = new ApprovalFormUserInfoQuery();
        approvalFormUserInfoQuery.setUserCode(param.getUserCode());
        approvalFormUserInfoQuery.setDayId(param.getDayId());

        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormUserInfoDao.selectByCondition(approvalFormUserInfoQuery);
        if (CollectionUtils.isNotEmpty(approvalFormUserInfoList)) {
            List<Long> formIdList = approvalFormUserInfoList.stream().map(AttendanceApprovalFormUserInfoDO::getFormId).distinct().collect(Collectors.toList());
            List<AttendanceApprovalFormDO> FormDOList = formManage.selectExistForm(formIdList);
            if (CollectionUtils.isNotEmpty(FormDOList)) {
                // 说明该天已经提交过加班申请了
                throw BusinessLogicException.getException(ErrorCodeEnum.OVER_TIME_REPEAT_ERROR);
            }
        }
    }

    /**
     * 校验选中部门和用户部门权限
     *
     * @param query
     * @param userAuthDTO
     * @return
     */
    private Boolean checkDeptAuth(OverTimeListQuery query, UserAuthDTO userAuthDTO) {
        // 系统管理员
        if (userAuthDTO.getIsAdmin()) {
            return Boolean.TRUE;
        }
        List<Long> deptAuthList = userAuthDTO.getDeptIds();
        // 非系统管理员且无部门权限
        if (CollectionUtils.isEmpty(deptAuthList)) {
            return Boolean.FALSE;
        }
        // 普通用户
        List<Long> deptIds = query.getDeptIdList();
        if (CollectionUtils.isEmpty(deptIds)) {
            query.setDeptIdList(deptAuthList);
            return Boolean.TRUE;
        }
        List<Long> interSectionList = deptAuthList.stream().filter(item -> deptIds.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(interSectionList)) {
            return Boolean.FALSE;
        }
        query.setDeptIdList(interSectionList);
        return Boolean.TRUE;
    }

    // 构建实体

    /**
     * 构建用户基本信息
     *
     * @param param 入参
     */
    private UserOverTimeAddParam userBaseInfoBuild(OverTimeAddParam param) {
        UserOverTimeAddParam userOverTimeAddParam = BeanUtils.convert(param, UserOverTimeAddParam.class);

        if (ObjectUtil.isNull(param)) {
            throw BusinessException.get(ErrorCodeEnum.PARAM_NOT_NULL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PARAM_NOT_NULL.getDesc()));
        }
        if (ObjectUtil.equal(param.getUserCode(), "") || ObjectUtil.equal(param.getApplyUserCode(), "")) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(Arrays.asList(param.getUserCode(),
                param.getApplyUserCode()));
        // 被申请人
        List<AttendanceUser> userCodeInfo = userInfoList.stream()
                .filter(e -> ObjectUtil.equal(e.getUserCode()
                        , param.getUserCode()))
                .collect(Collectors.toList());
        // 申请人
        List<AttendanceUser> applyUserCodeInfo = userInfoList.stream()
                .filter(e -> ObjectUtil.equal(e.getUserCode()
                        , param.getApplyUserCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoList) || CollectionUtils.isEmpty(userCodeInfo) || CollectionUtils.isEmpty(applyUserCodeInfo)) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        // 校验加班开始时间、结束时间的年月日是否相同，如果不相同，返回fail
        if (!DateUtil.isSameDay(param.getOverTimeStartDate(), param.getOverTimeEndDate())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.OVER_TIME_NOT_ON_THE_SAME_DAY);
        }
        // 如果是同一天，则设置day_id
        String dayIdString = DateUtil.format(param.getOverTimeStartDate(), DatePattern.PURE_DATE_PATTERN);
        // 设置dayId 不由excel传入
        param.setDayId(Long.parseLong(dayIdString));

        // 被申请人
        AttendanceUser userInfo = userCodeInfo.get(0);
        // 申请人
        AttendanceUser applyUserInfo = applyUserCodeInfo.get(0);

        userOverTimeAddParam.setUserId(userInfo.getId());
        userOverTimeAddParam.setUserCode(userInfo.getUserCode());
        userOverTimeAddParam.setDayId(param.getDayId());
        userOverTimeAddParam.setUserName(userInfo.getUserName());
        userOverTimeAddParam.setDeptId(userInfo.getDeptId());
        userOverTimeAddParam.setPostId(userInfo.getPostId());
        userOverTimeAddParam.setUserCountry(userInfo.getLocationCountry());
        userOverTimeAddParam.setUserOriginCountry(userInfo.getOriginCountry());
        userOverTimeAddParam.setApplyUserCountry(applyUserInfo.getLocationCountry());

        return userOverTimeAddParam;
    }

    /**
     * 构建对象
     *
     * @param userOverTimeAddParam 入参
     * @param formDO               对象
     * @param formUserInfoList     列表
     */
    private void overTimeDataAddBuild(UserOverTimeAddParam userOverTimeAddParam,
                                      AttendanceApprovalFormDO formDO,
                                      List<AttendanceApprovalFormUserInfoDO> formUserInfoList) {
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplyUserCode(userOverTimeAddParam.getUserCode());
        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.OVER_TIME));
        formDO.setFormType(FormTypeEnum.OVER_TIME.getCode());
        formDO.setDataSource(0);
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(formDO);

        // 构建详情表
        formUserInfoList.add(this.buildApprovalFormUserInfo(formDO, userOverTimeAddParam));
    }

    /**
     * 构建更新对象
     *
     * @param userOverTimeAddParam         入参
     * @param formDO                       对象
     * @param hrmsApprovalFormUserInfoList 列表
     */
    private void overTimeDataUpdateBuild(UserOverTimeAddParam userOverTimeAddParam,
                                         AttendanceApprovalFormDO formDO,
                                         List<AttendanceApprovalFormUserInfoDO> hrmsApprovalFormUserInfoList) {
        formDO.setFormType(FormTypeEnum.OVER_TIME.getCode());
        formDO.setDataSource(0);
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(formDO);
        //删除旧用户详情表数据
        for (AttendanceApprovalFormUserInfoDO oldUserInfoDO : hrmsApprovalFormUserInfoList) {
            oldUserInfoDO.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(oldUserInfoDO);
        }
        // 构建详情表
        hrmsApprovalFormUserInfoList.add(buildApprovalFormUserInfo(formDO, userOverTimeAddParam));
    }

    /**
     * 构建申请单详情信息
     *
     * @param formDO               申请单表单
     * @param userOverTimeAddParam 入参
     * @return HrmsApprovalFormUserInfoDO
     */
    private AttendanceApprovalFormUserInfoDO buildApprovalFormUserInfo(AttendanceApprovalFormDO formDO,
                                                                       UserOverTimeAddParam userOverTimeAddParam) {
        AttendanceApprovalFormUserInfoDO approvalFormUserInfo = new AttendanceApprovalFormUserInfoDO();
        approvalFormUserInfo.setId(IdWorkerUtil.getId());
        approvalFormUserInfo.setFormId(formDO.getId());
        approvalFormUserInfo.setDayId(userOverTimeAddParam.getDayId());
        approvalFormUserInfo.setUserCode(userOverTimeAddParam.getUserCode());
        approvalFormUserInfo.setUserName(userOverTimeAddParam.getUserName());
        approvalFormUserInfo.setDeptId(userOverTimeAddParam.getDeptId());
        approvalFormUserInfo.setPostId(userOverTimeAddParam.getPostId());
        approvalFormUserInfo.setCountry(userOverTimeAddParam.getUserCountry());
        approvalFormUserInfo.setOriginCountry(userOverTimeAddParam.getUserOriginCountry());
        approvalFormUserInfo.setStartDate(userOverTimeAddParam.getOverTimeStartDate());
        approvalFormUserInfo.setEndDate(userOverTimeAddParam.getOverTimeEndDate());
        approvalFormUserInfo.setEstimateDuration(userOverTimeAddParam.getOverTimeDuration());
        approvalFormUserInfo.setRemark(userOverTimeAddParam.getRemark());
        BaseDOUtil.fillDOInsert(approvalFormUserInfo);
        return approvalFormUserInfo;
    }

    /**
     * 构建审批信息
     *
     * @param initInfoApiDTO   审批信息
     * @param formDO           审批单据
     * @param formUserInfoList 审批单据详情
     */
    private void overTimeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                              AttendanceApprovalFormDO formDO,
                                              List<AttendanceApprovalFormUserInfoDO> formUserInfoList,
                                              UserOverTimeAddParam userOverTimeAddParam) {

        initInfoApiDTO.setBizId(formDO.getId().toString());
        initInfoApiDTO.setApprovalType(formDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(userOverTimeAddParam.getApplyUserCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(userOverTimeAddParam.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(formDO.getApplicationCode());

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        // 构建BPM审批流信息
        this.buildBpmInfoData(formUserInfoList, fieldApiDTOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);
        log.info("overTimeAddApprovalDataBuild||调用BPM入参值为:{}", JSON.toJSONString(initInfoApiDTO));
    }

    /**
     * 构建bpm信息
     *
     * @param formUserInfoList 审批单局详情
     * @param fieldApiDTOList  属性值映射
     */
    private void buildBpmInfoData(List<AttendanceApprovalFormUserInfoDO> formUserInfoList,
                                  List<ApprovalTypeFieldApiDTO> fieldApiDTOList) {
        if (CollectionUtils.isNotEmpty(formUserInfoList)) {
            List<String> approvalUserCodeList = formUserInfoList
                    .stream()
                    .map(AttendanceApprovalFormUserInfoDO::getUserCode)
                    .collect(Collectors.toList());
            // 查询用户
            List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(approvalUserCodeList);
            Map<String, AttendanceUser> userCodeMap = userInfoList
                    .stream()
                    .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));
            // 查询部门
            List<Long> deptIdList = userInfoList
                    .stream()
                    .map(AttendanceUser::getDeptId)
                    .collect(Collectors.toList());
            List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
            Map<Long, AttendanceDept> deptInfoMap = deptList
                    .stream()
                    .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
            // 查询岗位
            List<Long> postIdList = userInfoList
                    .stream()
                    .map(AttendanceUser::getPostId)
                    .collect(Collectors.toList());
            List<AttendancePost> entPostList = postService.listByPostList(postIdList);
            Map<Long, AttendancePost> postInfoMap = entPostList.stream().collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

            // 被申请人存在多个：hrms导入入口
            if (formUserInfoList.size() > 1) {
                List<String> userNameList = Lists.newArrayList();
                List<String> userCodeList = Lists.newArrayList();
                List<String> deptInfoList = Lists.newArrayList();
                List<Long> deptIdInfoList = Lists.newArrayList();
                List<String> postInfoList = Lists.newArrayList();
                List<Long> dayIdList = Lists.newArrayList();
                List<String> startDateList = Lists.newArrayList();
                List<String> endDateList = Lists.newArrayList();
                List<String> estimateDurationList = Lists.newArrayList();
                List<String> remarkList = Lists.newArrayList();
                //Map<String, String> deptMap = new HashMap<>();

                for (AttendanceApprovalFormUserInfoDO approvalFormUserInfo : formUserInfoList) {
                    AttendanceUser userInfo = userCodeMap.get(approvalFormUserInfo.getUserCode());
                    if (ObjectUtil.isNull(userInfo)) {
                        log.info("userCode ：{} not found", approvalFormUserInfo.getUserCode());
                        continue;
                    }
                    userNameList.add(userInfo.getUserName());
                    userCodeList.add(approvalFormUserInfo.getUserCode());

                    AttendanceDept hrmsEntDept = deptInfoMap.get(userInfo.getDeptId());
                    deptInfoList.add("");
                    if (ObjectUtil.isNotNull(hrmsEntDept)) {
                        deptInfoList.add(hrmsEntDept.getDeptNameEn());
                        deptIdInfoList.add(hrmsEntDept.getId());
                    }

                    AttendancePost hrmsEntPost = postInfoMap.get(userInfo.getPostId());
                    postInfoList.add("");
                    if (ObjectUtil.isNotNull(hrmsEntPost)) {
                        postInfoList.add(hrmsEntPost.getPostNameEn());
                    }

                    dayIdList.add(approvalFormUserInfo.getDayId());
                    startDateList.add(String.valueOf(approvalFormUserInfo.getStartDate()));
                    endDateList.add(String.valueOf(approvalFormUserInfo.getEndDate()));
                    estimateDurationList.add(String.valueOf(approvalFormUserInfo.getEstimateDuration()));
                    remarkList.add(approvalFormUserInfo.getRemark());
                }
                // 将deptInfoList去重
                deptInfoList = deptInfoList.stream().distinct().collect(Collectors.toList());
                // 将deptIdInfoList去重
                Long deptId = deptIdInfoList.get(0);

                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_NAME_LIST.getCode(), JSON.toJSONString(userNameList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_CODE_LIST.getCode(), JSON.toJSONString(userCodeList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_DEPT_LIST.getCode(), JSON.toJSONString(deptInfoList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_POST_LIST.getCode(), JSON.toJSONString(postInfoList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_START_DATE_LIST.getCode(), JSON.toJSONString(startDateList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_END_DATE_LIST.getCode(), JSON.toJSONString(endDateList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_ESTIMATE_DURATION_List.getCode(), JSON.toJSONString(estimateDurationList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.REMARK_LIST.getCode(), JSON.toJSONString(remarkList), null);

                // 打包人员的只需要部门负责人审核就好了，都是同一个部门负责人
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DEPT_ID.getCode(), deptId.toString(), null);
                return;
            }


            // 存在一个被申请人：hrms手动添加入口
            AttendanceApprovalFormUserInfoDO approvalFormUserInfo = formUserInfoList.get(0);
            AttendanceUser userInfo = userCodeMap.get(approvalFormUserInfo.getUserCode());
            if (ObjectUtil.isNull(userInfo)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
            }

            //被申请人姓名
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_NAME.getCode(), userInfo.getUserName(), null);
            //被申请人编码
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_CODE.getCode(), userInfo.getUserCode(), null);
            //被申请人部门
            //AttendanceDept AttendanceDept = hrmsDeptManage.selectById(userInfo.getDeptId());
            AttendanceDept AttendanceDept = deptInfoMap.get(userInfo.getDeptId());
            if (ObjectUtil.isNull(AttendanceDept)) {
                throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
            }
            Map<String, String> deptMap = new HashMap<>();
            deptMap.put(LanguageTypeEnum.zh_CN.getCode(), AttendanceDept.getDeptNameCn());
            deptMap.put(LanguageTypeEnum.en_US.getCode(), AttendanceDept.getDeptNameEn());
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DEPT_NAME.getCode(), AttendanceDept.getDeptNameEn(), deptMap);

            //被申请人岗位
            AttendancePost AttendancePost = postInfoMap.get(userInfo.getPostId());
            if (ObjectUtil.isNull(AttendancePost)) {
                throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.POST_NAME.getCode(), AttendancePost.getPostNameEn(), null);

            // 加班日
            String overTimeDay = String.valueOf(approvalFormUserInfo.getDayId());
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_DAY.getCode(), overTimeDay, null);

            // 加班开始时间
            String overTimeStartDate = DateUtil.format(approvalFormUserInfo.getStartDate(), DatePattern.NORM_DATETIME_PATTERN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_START_DATE.getCode(), overTimeStartDate, null);

            // 加班开始时间
            String overTimeEndDate = DateUtil.format(approvalFormUserInfo.getEndDate(), DatePattern.NORM_DATETIME_PATTERN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_END_DATE.getCode(), overTimeEndDate, null);

            // 预计加班时长
            BigDecimal estimateDuration = approvalFormUserInfo.getEstimateDuration();
            BigDecimal estimateDurationHour = BigDecimal.ZERO;
            if (Objects.nonNull(estimateDuration)) {
                estimateDurationHour = estimateDuration.divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP);
            }
            String overTimeEstimateDuration = String.valueOf(estimateDurationHour);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_ESTIMATE_DURATION.getCode(), overTimeEstimateDuration, null);

            //备注
            String remark = approvalFormUserInfo.getRemark();
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.REMARK.getCode(), remark, null);

            //被申请人部门ID
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DEPT_ID.getCode(), AttendanceDept.getId() != null ? AttendanceDept.getId().toString() : null, null);

            //被申请人常驻国
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_COUNTRY.getCode(), approvalFormUserInfo.getCountry() != null ? approvalFormUserInfo.getCountry() : null, null);

            //被申请人结算国
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.ORIGIN_COUNTRY.getCode(), approvalFormUserInfo.getOriginCountry() != null ? approvalFormUserInfo.getOriginCountry() : null, null);

            //被审批人ID
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), userInfo.getId() != null ? userInfo.getId().toString() : null, null);
        }
    }
}
