package com.imile.attendance.form;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.rule.dto.DayNormalPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/23
 * @Description 通用审批表单计算服务
 */
@Slf4j
@Service
public class CommonFormCalcService {

    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;

    /**
     * 免打卡计算时长
     *
     * @param beforeDayId
     * @param afterDayId
     * @param finalTempDayId
     * @param startDate
     * @param endDate
     * @param dayDurationInfoDTOList
     */
    public void dayDurationNoNeedHandler(Long beforeDayId, Long afterDayId,
                                         Long finalTempDayId,
                                         Date startDate, Date endDate,
                                         List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //是请假开始的前置一天/后置一天
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            return;
        }
        Date actualBeginDayDate = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
        Date actualEndDayDate = DateUtil.beginOfDay(DateUtil.offsetDay(actualBeginDayDate, 1));
        //没有交集
        int days = 0;
        int hours = 0;
        int minutes = 0;

        String dayShiftInfo = RequestInfoHolder.isChinese() ? "免打卡" : "No Punch Card";
        String leaveInfo = null;
        //自由打卡肯定是今天和明天之间的，跨天
        if (actualEndDayDate.compareTo(startDate) < 1) {
            return;
        }
        if (actualBeginDayDate.compareTo(endDate) > -1) {
            return;
        }
        //请假完全包含本次自由打卡的上下班
        if (actualBeginDayDate.compareTo(startDate) > -1 && actualEndDayDate.compareTo(endDate) < 1) {
            days = 1;
            leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //打卡上下班时间完全包含请假
        if (actualBeginDayDate.compareTo(startDate) < 1 && actualEndDayDate.compareTo(endDate) > -1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (actualBeginDayDate.compareTo(startDate) < 1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, actualEndDayDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
        minutes = (int) DateUtil.between(actualBeginDayDate, endDate, DateUnit.MINUTE);
        if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
            days = 1;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    /**
     * 无排班情况计算时长
     *
     * @param beforeDayItemList
     * @param beforeDayId
     * @param afterDayId
     * @param finalTempDayId
     * @param startDate
     * @param endDate
     * @param dayDurationInfoDTOList
     */
    public void dayDurationNoShiftHandler(List<PunchClassItemConfigDO> beforeDayItemList
            , Long beforeDayId, Long afterDayId, Long finalTempDayId
            , Date startDate, Date endDate
            , List<DayDurationInfoDTO> dayDurationInfoDTOList) {

        //是请假开始的前置一天/后置一天
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            return;
        }
        Date actualBeginDayDate = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
        Date actualEndDayDate = DateUtil.beginOfDay(DateUtil.offsetDay(actualBeginDayDate, 1));
        //查看前一天的班次是否跨天
        if (CollectionUtils.isNotEmpty(beforeDayItemList)) {
            beforeDayItemList = beforeDayItemList.stream().sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
            //查看前一天最后一个时段是否跨天,跨天则需要修改休息日当天的上班打卡时间为前一天最晚时段的下班时间
            PunchClassItemConfigDO lastItemConfig = beforeDayItemList.get(beforeDayItemList.size() - 1);
            if (lastItemConfig.getIsAcross() == 1) {
                Date punchOutTime = Objects.isNull(lastItemConfig.getPunchOutTime()) ? lastItemConfig.getLatestPunchOutTime() : lastItemConfig.getPunchOutTime();
                String punchOutTimeString = DateUtil.format(punchOutTime, "HH:mm:ss");
                String punchOutTimeDayString = DateUtil.format(actualBeginDayDate, "yyyy-MM-dd");
                actualBeginDayDate = DateUtil.parse(punchOutTimeDayString + " " + punchOutTimeString, "yyyy-MM-dd HH:mm:ss");
            }
        }
        //没有交集
        int days = 0;
        int hours = 0;
        int minutes = 0;

        String dayShiftInfo = RequestInfoHolder.isChinese() ? "未排班" : "Not scheduled";
        String leaveInfo = null;
        //自由打卡肯定是今天和明天之间的，跨天
        if (actualEndDayDate.compareTo(startDate) < 1) {
            return;
        }
        if (actualBeginDayDate.compareTo(endDate) > -1) {
            return;
        }
        //请假完全包含本次自由打卡的上下班
        if (actualBeginDayDate.compareTo(startDate) > -1 && actualEndDayDate.compareTo(endDate) < 1) {
            days = 1;
            leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //打卡上下班时间完全包含请假
        if (actualBeginDayDate.compareTo(startDate) < 1 && actualEndDayDate.compareTo(endDate) > -1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (actualBeginDayDate.compareTo(startDate) < 1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, actualEndDayDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
        minutes = (int) DateUtil.between(actualBeginDayDate, endDate, DateUnit.MINUTE);
        if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
            days = 1;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    /**
     * PH/OFF 计算时长
     *
     * @param beforeDayItemList
     * @param beforeDayId
     * @param afterDayId
     * @param finalTempDayId
     * @param startDate
     * @param endDate
     * @param dayPunchType
     * @param companyLeaveConfigDO
     * @param dayDurationInfoDTOList
     */
    public void dayDurationPhHandler(List<PunchClassItemConfigDO> beforeDayItemList
            , Long beforeDayId, Long afterDayId, Long finalTempDayId
            , Date startDate, Date endDate, String dayPunchType
            , CompanyLeaveConfigDO companyLeaveConfigDO
            , List<DayDurationInfoDTO> dayDurationInfoDTOList) {

        //是请假开始的前置一天/后置一天，表示不存在排班跨天的情况
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            return;
        }
        String dayShiftInfo = RequestInfoHolder.isChinese() ? "休息日/节假日" : "OFF/PH";
        String leaveInfo = null;
        Date actualBeginDayDate = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"));
        Date actualEndDayDate = DateUtil.beginOfDay(DateUtil.offsetDay(actualBeginDayDate, 1));
        //查看前一天的班次是否跨天
        if (CollectionUtils.isNotEmpty(beforeDayItemList)) {
            beforeDayItemList = beforeDayItemList.stream().sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo)).collect(Collectors.toList());
            //查看前一天最后一个时段是否跨天,跨天则需要修改休息日当天的上班打卡时间为前一天最晚时段的下班时间
            PunchClassItemConfigDO lastItemConfig = beforeDayItemList.get(beforeDayItemList.size() - 1);
            if (lastItemConfig.getIsAcross() == 1) {
                Date punchOutTime = Objects.isNull(lastItemConfig.getPunchOutTime()) ? lastItemConfig.getLatestPunchOutTime() : lastItemConfig.getPunchOutTime();
                String punchOutTimeString = DateUtil.format(punchOutTime, "HH:mm:ss");
                String punchOutTimeDayString = DateUtil.format(actualBeginDayDate, "yyyy-MM-dd");
                actualBeginDayDate = DateUtil.parse(punchOutTimeDayString + " " + punchOutTimeString, "yyyy-MM-dd HH:mm:ss");
            }
        }
        //没有交集
        if (actualEndDayDate.compareTo(startDate) < 1) {
            return;
        }
        if (actualBeginDayDate.compareTo(endDate) > -1) {
            return;
        }
        int days = 0;
        int hours = 0;
        int minutes = 0;
        //请假完全包含本天
        if (actualBeginDayDate.compareTo(startDate) > -1 && actualEndDayDate.compareTo(endDate) < 1) {
            //外勤/请假(消耗假期)
            if (companyLeaveConfigDO != null && companyLeaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
                days = 1;
                leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO
                        , BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo)
                        , Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //请假(不消耗假期)
            leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes)
                    , BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo)
                    , new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //改天上下班时间完全包含请假
        if (actualBeginDayDate.compareTo(startDate) < 1 && actualEndDayDate.compareTo(endDate) > -1) {
            //外勤/请假(消耗假期)
            if (companyLeaveConfigDO != null && companyLeaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
                leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
                minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                    days = 1;
                    dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                    return;
                }
                //不足一天
                hours = minutes / (BusinessConstant.MINUTES.intValue());
                minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //请假(不消耗假期)
            leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (actualBeginDayDate.compareTo(startDate) < 1) {
            //外勤/请假(消耗假期)
            if (companyLeaveConfigDO != null && companyLeaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
                leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(actualEndDayDate, "HH:mm");
                minutes = (int) DateUtil.between(startDate, actualEndDayDate, DateUnit.MINUTE);
                if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                    days = 1;
                    dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                    return;
                }
                //不足一天
                hours = minutes / (BusinessConstant.MINUTES.intValue());
                minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //请假(不消耗假期)
            leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        //外勤/请假(消耗假期)
        if (companyLeaveConfigDO != null && companyLeaveConfigService.isConsumeLeave(companyLeaveConfigDO.getConsumeLeaveType(), dayPunchType)) {
            leaveInfo = DateUtil.format(actualBeginDayDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(actualBeginDayDate, endDate, DateUnit.MINUTE);
            if (minutes >= (BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //请假(不消耗假期)
        leaveInfo = RequestInfoHolder.isChinese() ? "不消耗假期" : "Free leave";
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS, Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    /**
     * 自由排班计算时长
     *
     * @param punchClassConfigDO
     * @param itemConfigDOS
     * @param beforeDayId
     * @param afterDayId
     * @param finalTempDayId
     * @param startDate
     * @param endDate
     * @param dayDurationInfoDTOList
     */
    public void dayDurationFreeShiftHandler(PunchClassConfigDO punchClassConfigDO,
                                            List<PunchClassItemConfigDO> itemConfigDOS,
                                            Long beforeDayId, Long afterDayId, Long finalTempDayId,
                                            Date startDate, Date endDate,
                                            List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        //获取最早打卡时间和最晚打卡时间
        String earliestPunchInTimeMinute = DateUtil.format(itemConfigDOS.get(0).getEarliestPunchInTime(), "HH:mm:ss");
        String earliestPunchInTimeDay = DateUtil.format(DateUtil.parse(finalTempDayId.toString(), "yyyyMMdd"), "yyyy-MM-dd");
        Date earliestPunchInTime = DateUtil.parse(earliestPunchInTimeDay + " " + earliestPunchInTimeMinute, "yyyy-MM-dd HH:mm:ss");
        Date latestPunchOutTime = DateUtil.offsetDay(earliestPunchInTime, 1);

        //是请假开始的前置一天/后置一天
        if (finalTempDayId.compareTo(beforeDayId) == 0 || finalTempDayId.compareTo(afterDayId) == 0) {
            return;
        }
        String dayShiftInfo = DateUtil.format(earliestPunchInTime, "HH:mm") + "-" + DateUtil.format(latestPunchOutTime, "HH:mm");
        String leaveInfo = null;
        //自由打卡肯定是今天和明天之间的，跨天
        if (latestPunchOutTime.compareTo(startDate) < 1) {
            return;
        }
        if (earliestPunchInTime.compareTo(endDate) > -1) {
            return;
        }
        int days = 0;
        int hours = 0;
        int minutes = 0;

        //请假完全包含本次自由打卡的上下班
        if (earliestPunchInTime.compareTo(startDate) > -1 && latestPunchOutTime.compareTo(endDate) < 1) {
            days = 1;
            leaveInfo = dayShiftInfo;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //打卡上下班时间完全包含请假
        if (earliestPunchInTime.compareTo(startDate) < 1 && latestPunchOutTime.compareTo(endDate) > -1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
            minutes = (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
            if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //左边交集
        if (earliestPunchInTime.compareTo(startDate) < 1) {
            leaveInfo = DateUtil.format(startDate, "HH:mm") + "-" + DateUtil.format(latestPunchOutTime, "HH:mm");
            minutes = (int) DateUtil.between(startDate, latestPunchOutTime, DateUnit.MINUTE);
            if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
                days = 1;
                dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
                return;
            }
            //不足一天
            hours = minutes / (BusinessConstant.MINUTES.intValue());
            minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //右边交集
        leaveInfo = DateUtil.format(earliestPunchInTime, "HH:mm") + "-" + DateUtil.format(endDate, "HH:mm");
        minutes = (int) DateUtil.between(earliestPunchInTime, endDate, DateUnit.MINUTE);
        if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
            days = 1;
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), Arrays.asList(dayShiftInfo), Arrays.asList(leaveInfo), new ArrayList<>(), dayDurationInfoDTOList);
    }

    /**
     * 一次打卡 计算时长
     *
     * @param punchClassConfigDO
     * @param itemConfigDOS
     * @param finalTempDayId
     * @param startDate
     * @param endDate
     * @param dayDurationInfoDTOList
     */
    public void dayOnceNormalShiftHandler(PunchClassConfigDO punchClassConfigDO, List<PunchClassItemConfigDO> itemConfigDOS,
                                          Long finalTempDayId, Date startDate, Date endDate,
                                          List<DayDurationInfoDTO> dayDurationInfoDTOList) {

        int days = 0;
        int hours = 0;
        int minutes = 0;
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        List<Date> leaveStartList = new ArrayList<>();
        List<Date> leaveEndList = new ArrayList<>();

        //针对每个时刻进行处理
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOS) {
            String shiftInfo = DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm");
            dayShiftInfoList.add(shiftInfo);
            if (itemConfigDO.getRestStartTime() != null) {
                String restInfo = DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm");
                restInfoList.add(restInfo);
            }

            DayNormalPunchTimeDTO dayNormalPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassNormalItemDayTime(finalTempDayId, itemConfigDO.getId(), itemConfigDOS);
            if (dayNormalPunchTimeDTO == null) {
                continue;
            }
            // 这边根据上班时间来设置下班时间是正常的班次时间还是弹性后的时间
            Date actualDayPunchEndTime = dayNormalPunchTimeDTO.getDayPunchEndTime();

            //该时刻和请假没有交集
            if (actualDayPunchEndTime.compareTo(startDate) < 1) {
                continue;
            }
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(endDate) > -1) {
                continue;
            }

            //请假完全包含本次时刻打卡的上下班
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) > -1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) < 1) {
                minutes = minutes + ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), dayNormalPunchTimeDTO.getDayPunchEndTime(), DateUnit.MINUTE));
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() != null) {
                    minutes = minutes - ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE));
                }
                leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
                leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                continue;
            }
            //打卡上下班时间完全包含请假
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) > -1) {
                leaveStartList.add(startDate);
                leaveEndList.add(endDate);
                //注意休息时间没有的情况  后续情况都是有休息时间
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1 || startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //休息时间包含请假
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1 && endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                    continue;
                }
                //请假包含休息时间
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1 && startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                //左边交集
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 0) {
                    minutes = minutes + (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestStartTime(), DateUnit.MINUTE);
                    continue;
                }
                //右边交集
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestEndTime(), endDate, DateUnit.MINUTE);
                continue;
            }

            //左边交集 ： 班次开始时间小于等于请假的开始时间，则一定是【9:00 16:00 17:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长这两种情况
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1) {
                Date actualLeaveTime = null;
                leaveStartList.add(startDate);
                // 无需弹性时间
                actualLeaveTime = dayNormalPunchTimeDTO.getDayPunchEndTime();
                leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());

                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                continue;
            }

            //右边交集：班次开始时间大于请假的开始时间，则一定是【8:00 9:00 16:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长、上班时长小于等于请假开始时间的这三种情况。
            // 这边无需做特殊处理了，因为请假开始时间如果小于班次时间，则一定是取班次时间作为请假开始时间
            leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
            leaveEndList.add(endDate);
            if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            //特殊处理，注意请假时间和休息时间的交际
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
            minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
        }

        if (CollectionUtils.isNotEmpty(leaveStartList)) {
            Collections.sort(leaveStartList);
            Collections.sort(leaveEndList);
            String dayShiftInfo = DateUtil.format(leaveStartList.get(0), "HH:mm") + "-" + DateUtil.format(leaveEndList.get(leaveEndList.size() - 1), "HH:mm");
            leaveInfoList.add(dayShiftInfo);
        }
        if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);

    }

    /**
     * 固班/多班次 计算时长
     *
     * @param punchClassConfigDO
     * @param itemConfigDOS
     * @param beforeDayId
     * @param afterDayId
     * @param finalTempDayId
     * @param startDate
     * @param endDate
     * @param dayDurationInfoDTOList
     * @param userPunchRecordDTOList
     */
    public void dayDurationNormalShiftHandler(PunchClassConfigDO punchClassConfigDO,
                                              List<PunchClassItemConfigDO> itemConfigDOS,
                                              Long beforeDayId, Long afterDayId, Long finalTempDayId,
                                              Date startDate, Date endDate,
                                              List<DayDurationInfoDTO> dayDurationInfoDTOList,
                                              List<UserPunchRecordDTO> userPunchRecordDTOList) {
        int days = 0;
        int hours = 0;
        int minutes = 0;
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        List<Date> leaveStartList = new ArrayList<>();
        List<Date> leaveEndList = new ArrayList<>();

        //针对每个时刻进行处理
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOS) {
            String shiftInfo = DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm");
            dayShiftInfoList.add(shiftInfo);
            if (itemConfigDO.getRestStartTime() != null) {
                String restInfo = DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm") + "-" + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm");
                restInfoList.add(restInfo);
            }

            DayNormalPunchTimeDTO dayNormalPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassNormalItemDayTime(finalTempDayId, itemConfigDO.getId(), itemConfigDOS);
            if (dayNormalPunchTimeDTO == null) {
                continue;
            }

            // 获取弹性时长
            BigDecimal elasticTime = itemConfigDO.getElasticTime();
            // 班次打卡以及固定打卡两种情况，一定会存在弹性时长，所以这边为了防止空指针出现，null的时候设置为0
            elasticTime = ObjectUtil.isNull(elasticTime) ? BigDecimal.ZERO : elasticTime;
            BigDecimal elasticMinutesTime = elasticTime.multiply(BusinessConstant.MINUTES);
            // 获取弹性时长的下班时间
            DateTime elasticPunchEndTime = DateUtil.offsetMinute(dayNormalPunchTimeDTO.getDayPunchEndTime(), elasticMinutesTime.intValue());
            // 获取上班时间
            Date punchInTime = itemConfigDO.getPunchInTime();
            // 获取最早上班时间
            Date earliestPunchInTime = itemConfigDO.getEarliestPunchInTime();
            // 获取最晚上班时间
            Date latestPunchInTime = itemConfigDO.getLatestPunchInTime();
            // 获取最早上班时间精确的年月日时分秒
            Date earliestPunchInStartTime = null;
            // 如果最早上班时间大于上班时间，说明最早时间跨天了
            if (earliestPunchInTime.compareTo(punchInTime) > 0) {
                String earliestPunchInTimeString = DateUtil.format(earliestPunchInTime, "HH:mm:ss");
                String earliestPunchInTimeDayString = DateUtil.format(DateUtil.offsetDay(dayNormalPunchTimeDTO.getDayPunchStartTime(), -1), "yyyy-MM-dd");
                earliestPunchInStartTime = DateUtil.parse(earliestPunchInTimeDayString + " " + earliestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");
            } else {
                String earliestPunchInTimeString = DateUtil.format(earliestPunchInTime, "HH:mm:ss");
                String earliestPunchInTimeDayString = DateUtil.format(dayNormalPunchTimeDTO.getDayPunchStartTime(), "yyyy-MM-dd");
                earliestPunchInStartTime = DateUtil.parse(earliestPunchInTimeDayString + " " + earliestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");
            }
            // 获取最晚上班时间精确的年月日时分秒：【这个时间的年月日一定是跟打卡时间同样的年月日】
            String latestPunchInTimeDayString = DateUtil.format(dayNormalPunchTimeDTO.getDayPunchStartTime(), "yyyy-MM-dd");
            String latestPunchInTimeString = DateUtil.format(latestPunchInTime, "HH:mm:ss");
            Date latestPunchInStartTime = DateUtil.parse(latestPunchInTimeDayString + " " + latestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");

            // 获取最早上班时间->最晚上班时间之间的打卡数据
            Date finalEarliestPunchInStartTime = earliestPunchInStartTime;
            List<UserPunchRecordDTO> itemPunchRecordList = userPunchRecordDTOList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(finalEarliestPunchInStartTime) > -1 && item.getFormatPunchTime().compareTo(latestPunchInStartTime) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
            // 获取最新的一个打卡记录
            UserPunchRecordDTO userPunchRecordDTO = null;
            long betweenMinutes = 0;
            if (CollUtil.isNotEmpty(itemPunchRecordList)) {
                userPunchRecordDTO = itemPunchRecordList.get(0);
                // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
                if (userPunchRecordDTO.getFormatPunchTime().compareTo(dayNormalPunchTimeDTO.getDayPunchStartTime()) > -1 && userPunchRecordDTO.getFormatPunchTime().compareTo(latestPunchInStartTime) < 1) {
                    // 获取两个时间相差分钟数
                    betweenMinutes = DateUtil.between(userPunchRecordDTO.getFormatPunchTime(), dayNormalPunchTimeDTO.getDayPunchStartTime(), DateUnit.MINUTE);
                }
            }
            // 这边根据上班时间来设置下班时间是正常的班次时间还是弹性后的时间
            Date actualDayPunchEndTime = dayNormalPunchTimeDTO.getDayPunchEndTime();
            if (betweenMinutes != 0) {
                actualDayPunchEndTime = DateUtil.offsetMinute(dayNormalPunchTimeDTO.getDayPunchEndTime(), (int) betweenMinutes);
            }

            //该时刻和请假没有交集
            if (actualDayPunchEndTime.compareTo(startDate) < 1) {
                continue;
            }
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(endDate) > -1) {
                continue;
            }

            //请假完全包含本次时刻打卡的上下班
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) > -1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) < 1) {
                minutes = minutes + ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), dayNormalPunchTimeDTO.getDayPunchEndTime(), DateUnit.MINUTE));
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() != null) {
                    minutes = minutes - ((int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE));
                }
                leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
                leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                continue;
            }
            //打卡上下班时间完全包含请假
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1 && dayNormalPunchTimeDTO.getDayPunchEndTime().compareTo(endDate) > -1) {
                leaveStartList.add(startDate);
                leaveEndList.add(endDate);
                //注意休息时间没有的情况  后续情况都是有休息时间
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1 || startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    continue;
                }
                //休息时间包含请假
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1 && endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                    continue;
                }
                //请假包含休息时间
                if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1 && startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                    minutes = minutes + (int) DateUtil.between(startDate, endDate, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                //左边交集
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 0) {
                    minutes = minutes + (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestStartTime(), DateUnit.MINUTE);
                    continue;
                }
                //右边交集
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestEndTime(), endDate, DateUnit.MINUTE);
                continue;
            }

            //左边交集 ： 班次开始时间小于等于请假的开始时间，则一定是【9:00 16:00 17:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长这两种情况
            if (dayNormalPunchTimeDTO.getDayPunchStartTime().compareTo(startDate) < 1) {
                Date actualLeaveTime = null;
                leaveStartList.add(startDate);
                // 需要修改这边的endTime，根据打卡时间来判断【背景：班次 9:00-17:00，早上打卡时间：9:15，晚上打卡时间：16:00.现在需要请假，请假时间16:00-18:00。这个时候请假需要计算上弹性时间，就是请假的endTime需要是16:00-17:15，而不是16:00-17:00，因为16:00-17:00生成当天考勤还是早退】
                //leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                // 如果请假结束时间小于等于弹性后的下班时间 ，则取请假结束时间，否则取弹性下班时间
                //if (endDate.compareTo(elasticPunchEndTime) < 1) {
                //    leaveEndList.add(endDate);
                //    actualLeaveTime = endDate;
                //} else {
                //    leaveEndList.add(elasticPunchEndTime);
                //    actualLeaveTime = elasticPunchEndTime;
                //}
                // 最新版弹性
                if (betweenMinutes == 0) {
                    // 无需弹性时间
                    actualLeaveTime = dayNormalPunchTimeDTO.getDayPunchEndTime();
                    leaveEndList.add(dayNormalPunchTimeDTO.getDayPunchEndTime());
                } else {
                    // 如果请假结束时间小于等于应该弹性后的下班时间 ，则取请假结束时间，否则取应该弹性下班时间。应该弹性下班时间：【比如9-18，弹性半小时，早上9:10打卡，则应弹性下班时间：18:10】
                    if (endDate.compareTo(actualDayPunchEndTime) < 1) {
                        leaveEndList.add(endDate);
                        actualLeaveTime = endDate;
                    } else {
                        leaveEndList.add(actualDayPunchEndTime);
                        actualLeaveTime = actualDayPunchEndTime;
                    }
                    // 需要弹性时间
                    //actualLeaveTime = DateUtil.offsetMinute(dayNormalPunchTimeDTO.getDayPunchEndTime(), (int) betweenMinutes);
                    //leaveEndList.add(actualLeaveTime);
                }
                if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                //特殊处理，注意请假时间和休息时间的交际
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    continue;
                }
                if (startDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) > -1) {
                    minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                    minutes = minutes - (int) DateUtil.between(startDate, dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                    continue;
                }
                minutes = minutes + (int) DateUtil.between(startDate, actualLeaveTime, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
                continue;
            }

            //右边交集：班次开始时间大于请假的开始时间，则一定是【8:00 9:00 16:00 18:00】的情况：因为上面已经判断了请假时长完全包含上班时长、上班时长完全包含请假时长、上班时长小于等于请假开始时间的这三种情况。
            // 这边无需做特殊处理了，因为请假开始时间如果小于班次时间，则一定是取班次时间作为请假开始时间
            leaveStartList.add(dayNormalPunchTimeDTO.getDayPunchStartTime());
            leaveEndList.add(endDate);
            if (dayNormalPunchTimeDTO.getDayPunchRestStartTime() == null) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            //特殊处理，注意请假时间和休息时间的交际
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestStartTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            if (endDate.compareTo(dayNormalPunchTimeDTO.getDayPunchRestEndTime()) < 1) {
                minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
                minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), endDate, DateUnit.MINUTE);
                continue;
            }
            minutes = minutes + (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchStartTime(), endDate, DateUnit.MINUTE);
            minutes = minutes - (int) DateUtil.between(dayNormalPunchTimeDTO.getDayPunchRestStartTime(), dayNormalPunchTimeDTO.getDayPunchRestEndTime(), DateUnit.MINUTE);
        }

        if (CollectionUtils.isNotEmpty(leaveStartList)) {
            Collections.sort(leaveStartList);
            Collections.sort(leaveEndList);
            String dayShiftInfo = DateUtil.format(leaveStartList.get(0), "HH:mm") + "-" + DateUtil.format(leaveEndList.get(leaveEndList.size() - 1), "HH:mm");
            leaveInfoList.add(dayShiftInfo);
        }
        if (minutes >= (punchClassConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).intValue())) {
            dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);
            return;
        }
        //不足一天
        hours = minutes / (BusinessConstant.MINUTES.intValue());
        minutes = minutes - hours * (BusinessConstant.MINUTES.intValue());
        dayDurationInfoDTOBuild(finalTempDayId, BigDecimal.valueOf(days), BigDecimal.valueOf(hours), BigDecimal.valueOf(minutes), punchClassConfigDO.getLegalWorkingHours(), dayShiftInfoList, leaveInfoList, restInfoList, dayDurationInfoDTOList);

    }


    private void dayDurationInfoDTOBuild(Long dayId, BigDecimal days,
                                         BigDecimal hours, BigDecimal minutes, BigDecimal legalWorkingHours,
                                         List<String> dayShiftInfoList,
                                         List<String> leaveInfoList, List<String> restInfoList,
                                         List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        DayDurationInfoDTO dayDurationInfoDTO = new DayDurationInfoDTO();
        dayDurationInfoDTO.setDayId(dayId);
        dayDurationInfoDTO.setDays(days);
        dayDurationInfoDTO.setHours(hours);
        dayDurationInfoDTO.setMinutes(minutes);
        dayDurationInfoDTO.setLegalWorkingHours(legalWorkingHours);
        dayDurationInfoDTO.setDayShiftInfoList(dayShiftInfoList);
        dayDurationInfoDTO.setLeaveInfoList(leaveInfoList);
        dayDurationInfoDTO.setRestInfoList(restInfoList);
        dayDurationInfoDTOList.add(dayDurationInfoDTO);
    }

}
