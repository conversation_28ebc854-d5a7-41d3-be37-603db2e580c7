package com.imile.attendance.form.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.biz.overtime.OvertimeApprovalService;
import com.imile.attendance.form.param.AttendanceOTLeavePaymentHandlerParam;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormUserInfoDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormUserInfoQuery;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 每日23:59分执行，查询是否有当天加班通过的单据，有则需要生成调休假
 * @author: han.wang
 * @createDate: 2024-7-10
 * @version: 1.0
 */
@Slf4j
@Component
public class AttendanceOTLeavePaymentHandler {

    @Resource
    private AttendanceApprovalFormDao approvalFormDao;
    @Resource
    private AttendanceApprovalFormUserInfoDao formUserInfoDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private OvertimeApprovalService overtimeApprovalService;

    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER)
    public ReturnT<String> attendanceOTLeavePaymentHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER, param);
        AttendanceOTLeavePaymentHandlerParam handlerParam = StringUtils.isNotBlank(param)
                ? JSON.parseObject(param, AttendanceOTLeavePaymentHandlerParam.class)
                : new AttendanceOTLeavePaymentHandlerParam();
        Long dayId = handlerParam.getDayId();
        List<String> userCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
            userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
        }
        if (Objects.isNull(dayId) || dayId <= 0) {
            dayId = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
            XxlJobLogger.log("XXL-JOB, {} 当前日期:{}", BusinessConstant.JobHandler.ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER, dayId);
        }
        // 查询当天审批通过的加班单
        ApprovalFormUserInfoQuery approvalFormUserInfoQuery = ApprovalFormUserInfoQuery
                .builder()
                .dayId(dayId)
                .build();
        if (CollectionUtils.isNotEmpty(userCodeList)) {
            approvalFormUserInfoQuery.setUserCodes(userCodeList);
        }

        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = formUserInfoDao.selectByCondition(approvalFormUserInfoQuery);
        if (CollectionUtils.isNotEmpty(approvalFormUserInfoList)) {
            // 查询被申请人对应的用户信息
            List<String> formUserCodes = approvalFormUserInfoList.stream().map(AttendanceApprovalFormUserInfoDO::getUserCode).distinct().collect(Collectors.toList());
            UserDaoQuery userQuery = UserDaoQuery
                    .builder()
                    .userCodes(formUserCodes)
                    .isDriver(0)
                    .build();
            List<AttendanceUser> userInfoDOS = userService.listUsersByQuery(userQuery);
            Map<String, List<AttendanceUser>> userCodeMap = userInfoDOS
                    .stream()
                    .collect(Collectors.groupingBy(AttendanceUser::getUserCode));
            // 通过申请单据id进行分组
            Map<Long, List<AttendanceApprovalFormUserInfoDO>> formUserMap = approvalFormUserInfoList
                    .stream()
                    .collect(Collectors.groupingBy(AttendanceApprovalFormUserInfoDO::getFormId));
            // 查询审批通过的审批单
            List<Long> formIdList = approvalFormUserInfoList
                    .stream()
                    .map(AttendanceApprovalFormUserInfoDO::getFormId)
                    .distinct()
                    .collect(Collectors.toList());
            ApprovalFormQuery approvalFormQuery = ApprovalFormQuery.builder().formIdList(formIdList)
                    .formStatus(FormStatusEnum.PASS.getCode()).build();
            List<AttendanceApprovalFormDO> AttendanceApprovalFormDOS = approvalFormDao.selectByCondition(approvalFormQuery);
            if (CollectionUtils.isEmpty(AttendanceApprovalFormDOS)) {
                XxlJobLogger.log("XXL-JOB, {} 当前日期:{}, 没有审批通过的加班单", BusinessConstant.JobHandler.ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER, dayId);
                return ReturnT.SUCCESS;
            }
            List<UserLeaveStageDetailDO> updateUserLeaveStageDetail = Lists.newArrayList();
            List<UserLeaveRecordDO> addUserLeaveRecord = Lists.newArrayList();
            for (AttendanceApprovalFormDO attendanceApprovalFormDO : AttendanceApprovalFormDOS) {
                List<AttendanceApprovalFormUserInfoDO> formUserInfoDOS = formUserMap.get(attendanceApprovalFormDO.getId());
                if (CollectionUtils.isEmpty(formUserInfoDOS)) {
                    XxlJobLogger.log("XXL-JOB, {} 当前日期:{}, 找不到对应的加班单:{}", BusinessConstant.JobHandler.ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER, dayId, attendanceApprovalFormDO.getId());
                    continue;
                }

                for (AttendanceApprovalFormUserInfoDO formUserInfoDO : formUserInfoDOS) {
                    // 生成调休假
                    List<AttendanceUser> userInfoList = userCodeMap.get(formUserInfoDO.getUserCode());
                    if (CollectionUtils.isEmpty(userInfoList)) {
                        XxlJobLogger.log("XXL-JOB, {} 员工编码:{}, 找不到对应员工", BusinessConstant.JobHandler.ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER, formUserInfoDO.getUserCode());
                        continue;
                    }
                    AttendanceUser userInfo = userInfoList.get(0);
                    overtimeApprovalService.addOverTime(userInfo, userInfo.getUserCode(),
                            formUserInfoDO.getEstimateDuration(), updateUserLeaveStageDetail,
                            DateUtil.date(), dayId, formUserInfoDO.getStartDate(), formUserInfoDO.getEndDate(),
                            addUserLeaveRecord, "【定时任务发放】审批通过：增加调休假期余额");
                }
            }
            // 落库
            attendanceApprovalManage.approvalFormOrLeaveUpdate(null, null, updateUserLeaveStageDetail, addUserLeaveRecord);
        }
        return ReturnT.SUCCESS;
    }
}
