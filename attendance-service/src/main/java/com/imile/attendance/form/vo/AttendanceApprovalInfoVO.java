package com.imile.attendance.form.vo;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class AttendanceApprovalInfoVO {
    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private String deptName;

    /**
     * 被申请人岗位
     */
    private String postName;

    /**
     * 被申请人国家
     */
    private String country;

    /**
     * 被申请人结算国
     */
    private String originCountry;

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据ID
     */
    private Long applicationFormId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 单据状态
     */
    private String formStatusDesc;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人编码
     */
    private String createUserCode;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;


    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 假期类型描述(多语转换)
     */
    private String leaveTypeByLang;

    /**
     * 请假开始时间
     */
    private Date leaveStartDate;

    /**
     * 请假结束时间
     */
    private Date leaveEndDate;

    /**
     * 用户预计请假时长(中英文拼接好给前端)
     */
    private String expectedLeaveTime;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 外勤开始时间
     */
    private Date outOfOfficeStartDate;

    /**
     * 外勤结束时间
     */
    private Date outOfOfficeEndDate;

    /**
     * 用户预计外勤时长
     */
    private String expectedOutOfOfficeTime;

    /**
     * 补卡类型
     */
    private String reissueCardType;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;

    /**
     * 这里单据关联的撤销单编码
     */
    private String revokeApplicationCode;

    /**
     * 这里单据关联的撤销单ID
     */
    private Long revokeApplicationFormId;

    /**
     * 这里单据关联的撤销单状态
     */
    private String revokeFormStatus;

    /**
     * 这里单据关联的撤销单类型
     */
    private String revokeFormType;

    /**
     * 审批中心的单据ID
     */
    private Long approvalId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期名称描述(多语转换)
     */
    private String leaveNameByLang;

}

