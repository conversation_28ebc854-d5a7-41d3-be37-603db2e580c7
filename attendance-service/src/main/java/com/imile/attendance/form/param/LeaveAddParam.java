package com.imile.attendance.form.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Data
public class LeaveAddParam {

    /**
     * 申请单据ID(新增无需传)
     */
    private Long applicationFormId;

    /**
     * 申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long applyUserId;

    /**
     * 被申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 假期规则主键
     */
    private Long configId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 请假开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveStartDate;

    /**
     * 请假结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveEndDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;


    //后端自己生成的业务逻辑，前端无需传递
    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人所在国
     */
    private String country;

    /**
     * 被申请人所属结算国
     */
    private String originCountry;

    /**
     * 是否仓内员工
     */
    private Integer isWarehouseStaff;

    /**
     * 每天时长计算明细
     */
    private List<DayDurationInfoDTO> dayDurationInfoDTOList;

    /**
     * 用户假期可用余额(分钟)
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 请假单位
     */
    private String leaveUnit;

    /**
     * 最小请假时长
     */
    private Integer miniLeaveDuration;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 用户偶默认每天的出勤小时线
     */
    private BigDecimal dayAttendanceHours;

    /**
     * 是否上传附件
     */
    private Integer isUploadAttachment;

    /**
     * 上传附件条件：
     */
    private Long uploadAttachmentCondition;

    /**
     * 上传附件单位：DAYS-天 HOURS-小时 MINUTES-分钟
     */
    private String attachmentUnit;
}
