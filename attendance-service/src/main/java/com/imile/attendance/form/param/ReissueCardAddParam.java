package com.imile.attendance.form.param;

import com.imile.attendance.infrastructure.repository.form.model.AttendanceUserCardConfigDO;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-6-27
 * @version: 1.0
 */
@Data
public class ReissueCardAddParam {

    /**
     * 申请单据ID(新增无需传)
     */
    private Long applicationFormId;

    /**
     * 申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long applyUserId;

    /**
     * 被申请人ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;

    /**
     * 异常考勤ID
     */
    private Long abnormalId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件
     */
    private List<AttachmentDTO> attachmentList;

    /**
     * 操作方式 0 暂存  1保存  2预览
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer operationType;

    //后端逻辑处理需要的字段
    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private Long deptId;

    /**
     * 被申请人岗位
     */
    private Long postId;

    /**
     * 被申请人所在国
     */
    private String country;

    /**
     * 被申请人所属结算国
     */
    private String originCountry;

    /**
     * 是否仓内员工
     */
    private Integer isWarehouseStaff;

    /**
     * 补卡日期(即要处理的哪天的异常考勤日期)
     */
    private Long reissueCardDayId;

    /**
     * 补卡类型
     */
    private String reissueCardType;

    /**
     * 剩余可用补卡次数
     */
    private Integer residueReissueCardCount;

    /**
     * 当前补卡日期对应的考勤周期起始时间
     */
    private Date attendanceStartDate;

    /**
     * 当前补卡日期对应的考勤周期截止时间
     */
    private Date attendanceEndDate;

    /**
     * 打卡规则对应的班次的所有的时刻信息
     */
    private String punchConfigClassItemInfo;

    /**
     * 实际打卡时间(没有就为空)，有多个取离上下班最近的一个
     */
    private Date actualPunchTime;

    /**
     * 补卡后的时间(根据时刻时间来补)
     */
    private Date correctPunchTime;

    /**
     * 本次补卡日期对应的用户补卡配置
     */
    private AttendanceUserCardConfigDO userCardConfigDO;

    /**
     * 补卡日期对应班次id
     */
    private Long reissueCardClassId;

    /**
     * 补卡日期对应打卡规则id
     */
    private Long reissueCardConfigId;

    /**
     * 当日最早实际打卡时间
     */
    private Date earlyPunchTime;

    /**
     * 当日最晚实际打卡时间
     */
    private Date latePunchTime;
}
