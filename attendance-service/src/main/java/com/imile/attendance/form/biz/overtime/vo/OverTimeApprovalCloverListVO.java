package com.imile.attendance.form.biz.overtime.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * {@code @author:} han.wang
 * {@code @className:} OverTimeApprovalFromListVO
 * {@code @since:} 2024-07-11 10:09
 */
@Data
public class OverTimeApprovalCloverListVO {

    /**
     * 被申请人编码
     */
    private String userCode;

    /**
     * 被申请人姓名
     */
    private String userName;

    /**
     * 被申请人部门
     */
    private String deptName;

    /**
     * 被申请人岗位
     */
    private String postName;

    /**
     * 被申请人国家
     */
    private String country;

    /**
     * 申请单号
     */
    private String applicationCode;

    /**
     * 单据ID
     */
    private Long applicationFormId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 单据状态
     */
    private String formStatusDesc;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建人编码
     */
    private String createUserCode;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 日期：年月日
     */
    private Long dayId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    /**
     * 预计加班时长(页面显示小时数)
     */
    private BigDecimal estimateDuration;


    /**
     * 最后修改用户编码
     */
    private String lastUpdUserCode;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 这里单据关联的撤销单编码
     */
    private String revokeApplicationCode;

    /**
     * 这里单据关联的撤销单ID
     */
    private Long revokeApplicationFormId;

    /**
     * 这里单据关联的撤销单状态
     */
    private String revokeFormStatus;

    /**
     * 这里单据关联的撤销单类型
     */
    private String revokeFormType;

    /**
     * 审批中心的单据ID
     */
    private Long approvalId;

}
