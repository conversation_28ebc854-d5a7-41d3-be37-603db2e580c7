package com.imile.attendance.form.mapstruct;


import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.ApprovalUserInfoDTO;
import com.imile.attendance.form.param.AttendanceApprovalInfoParam;
import com.imile.attendance.form.param.UserAuthParam;
import com.imile.attendance.form.vo.AttendanceApplicationFromDetailVO;
import com.imile.attendance.form.vo.AttendanceApprovalInfoVO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalUserInfoApiDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/15
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceFormMapstruct {

    AttendanceFormMapstruct INSTANCE = Mappers.getMapper(AttendanceFormMapstruct.class);

    AttendanceApprovalInfoQuery mapInitInfoToApprovalApiQuery(AttendanceApprovalInfoParam infoParam);

    ApprovalEmptyRecordApiQuery mapInitInfoToRecordApiQuery(ApprovalInitInfoApiDTO initInfoApiDTO);

    UserDaoQuery mapAuthParamToQuery(UserAuthParam authParam);

    // 审批列表
    AttendanceApprovalInfoQuery mapListParamToApprovalInfoQuery(AttendanceApprovalInfoParam approvalInfoParam);
    AttendanceApprovalInfoVO mapFormDOToApprovalInfoVO(AttendanceFormDO formDO);

    // 单据详情
    AttendanceApplicationFromDetailVO mapFormDOToApprovalDetailInfoVO(AttendanceFormDO formDO);
    // 审批预览
    List<ApprovalUserInfoDTO> mapApprovalUserInfoApiToDTO(List<ApprovalUserInfoApiDTO> approvalUserInfoApiDTOS);
    ApprovalDetailStepRecordDTO mapApiDTOToStepRecordDTO(ApprovalEmptyRecordApiDTO apiDTO);

    // 加班相关
    ApprovalFormQuery mapQueryToApprovalFormQuery(OverTimeListQuery query);

    // deep copy
    AttendanceFormDO deepCopy(AttendanceFormDO formDO);
}
