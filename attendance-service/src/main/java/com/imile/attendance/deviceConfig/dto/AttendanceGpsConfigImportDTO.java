package com.imile.attendance.deviceConfig.dto;

import com.imile.common.excel.ExcelImport;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Data
public class AttendanceGpsConfigImportDTO extends ExcelImport {

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * 地址名称
     */
    private String addressName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 有效范围
     */
    private Integer effectiveRange;
}
