package com.imile.attendance.domain.employee.repository.impl;

import com.imile.attendance.domain.employee.Employee;
import com.imile.attendance.domain.employee.converter.EmployeeConverter;
import com.imile.attendance.domain.employee.repository.EmployeeRepository;
import com.imile.attendance.repository.employee.dao.UserInfoDao;
import com.imile.attendance.repository.employee.modle.UserInfoDO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class EmployeeRepositoryImpl implements EmployeeRepository {

    private final UserInfoDao userInfoDao;


    @Override
    public Employee getEmployeeByCode(String userCode) {
        if (StringUtils.isBlank(userCode)){
            return null;
        }
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        return EmployeeConverter.INSTANCE.mapToDomain(userInfoDO);
    }
}
