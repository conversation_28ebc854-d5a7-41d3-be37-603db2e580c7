package com.imile.attendance.domain.employee.converter;

import com.imile.attendance.domain.employee.Employee;
import com.imile.attendance.repository.employee.modle.UserInfoDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Mapper
public interface EmployeeConverter {

    EmployeeConverter INSTANCE = Mappers.getMapper(EmployeeConverter.class);


    @Mapping(target = "lastUpdUserName", source = "employee.modifier.name")
    @Mapping(target = "lastUpdUserCode", source = "employee.modifier.id")
    @Mapping(target = "createUserName", source = "employee.creator.name")
    @Mapping(target = "createUserCode", source = "employee.creator.id")
    UserInfoDO mapToDO(Employee employee);

    @InheritInverseConfiguration
    Employee mapToDomain(UserInfoDO userInfoDO);
}
