package com.imile.attendance.excel.annonation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * excel导出参数填充
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExportParamFill {

    Source source() default Source.IPEP;

    enum Source {
        IPEP
    }
}
