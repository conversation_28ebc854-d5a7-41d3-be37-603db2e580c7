package com.imile.attendance.excel.export;

import com.imile.attendance.util.WebUtils;
import com.imile.common.query.BaseQuery;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> chen
 * @Date 2024/12/3 
 * @Description
 */
public abstract class AbstractParameterFillResolver implements ParameterFillResolver {

    @Override
    public void resolve(BaseQuery query) {
        HttpServletRequest request = WebUtils.request();
        doResolve(query, request);
    }

    public abstract void doResolve(BaseQuery query, HttpServletRequest request);
}
