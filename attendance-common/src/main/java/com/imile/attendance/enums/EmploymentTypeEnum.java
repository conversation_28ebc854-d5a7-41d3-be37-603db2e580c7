package com.imile.attendance.enums;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.ucenter.api.context.RequestInfoHolder;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * {@code @author:} allen
 * {@code @className:} EmploymentTypeEnum
 * {@code @since:} 2023-10-07 11:24
 * {@code @description:} 用工类型标准化后为减少影响范围code值尽量保持与老的一致
 */
@Getter
public enum EmploymentTypeEnum {
    DEFAULT("", "", "", ""),
    EMPLOYEE("Employee", EmployeeNatureEnum.INTERNAL.getCode(), "员工", "Employee"),
    SUB_EMPLOYEE("SubEmployee", EmployeeNatureEnum.INTERNAL.getCode(), "挂靠", "Sponsorship"),
    OS_FIXED_SALARY("OSFixedsalary", EmployeeNatureEnum.EXTERNAL.getCode(), "劳务派遣", "Manpower OS"),
    OS_PER_DELIVERED("OSPerdelivered", EmployeeNatureEnum.EXTERNAL.getCode(), "合作伙伴", "Partner-employee"),
    FREELANCER("Freelancer", EmployeeNatureEnum.EXTERNAL.getCode(), "众包", "Freelancer"),
    INTERN("Intern", EmployeeNatureEnum.INTERNAL.getCode(), "实习生", "Intern"),
    PART_TIMER("PartTimer", EmployeeNatureEnum.INTERNAL.getCode(), "兼职", "Part-timer"),
    CONSULTANT("Consultant", EmployeeNatureEnum.EXTERNAL.getCode(), "顾问", "Consultant"),
    ;
    private final String code;
    private final String nature;
    private final String desc;
    private final String descEn;

    EmploymentTypeEnum(String code, String nature, String desc, String descEn) {
        this.code = code;
        this.nature = nature;
        this.desc = desc;
        this.descEn = descEn;
    }

    private static final Map<String, EmploymentTypeEnum> EMPLOYMENT_TYPE_MAP = Maps.newHashMap();

    static {
        for (EmploymentTypeEnum employmentType : EmploymentTypeEnum.values()) {
            EMPLOYMENT_TYPE_MAP.put(employmentType.code, employmentType);
        }
    }

    /**
     * 根据用工类型获取枚举
     *
     * @param code 用工类型
     * @return 枚举
     */
    public static EmploymentTypeEnum getByCode(String code) {
        if (ObjectUtil.isEmpty(code)) {
            return DEFAULT;
        }
        return EMPLOYMENT_TYPE_MAP.getOrDefault(code, DEFAULT);
    }

    public static EmploymentTypeEnum valueOfDesc(String desc) {
        for (EmploymentTypeEnum typeEnum : EmploymentTypeEnum.values()) {
            if (typeEnum.getDesc().equals(desc) || typeEnum.getDescEn().equals(desc)) {
                return typeEnum;
            }
        }
        return DEFAULT;
    }

    public static String descOfCode(String code) {
        for (EmploymentTypeEnum typeEnum : EmploymentTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return RequestInfoHolder.isChinese() ? typeEnum.desc : typeEnum.getDescEn();
            }
        }
        return "";
    }

    /**
     * 需要选择供应商的用工类型
     */
    public static final List<String> TYPE_OF_REQUIRED_VENDOR
            = Lists.newArrayList(SUB_EMPLOYEE, OS_FIXED_SALARY, OS_PER_DELIVERED, CONSULTANT).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * TMS可修改黑名单的用工类型
     */
    public static final List<String> TYPE_OF_TMS_EDIT_BLACKLIST
            = Lists.newArrayList(OS_FIXED_SALARY, OS_PER_DELIVERED).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 需要设置默认供应商的用工类型
     */
    public static final List<String> TYPE_OF_DEFAULT_VENDOR
            = Lists.newArrayList(EMPLOYEE, INTERN, PART_TIMER).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 可选汇报上级的用工类型
     */
    public static final List<String> TYPE_OF_OPTIONAL_LEADER
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 自有的用工类型
     */
    public static final List<String> TYPE_OF_OWN
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE, INTERN, PART_TIMER).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 外包的用工类型
     */
    public static final List<String> TYPE_OF_OS
            = Lists.newArrayList(OS_FIXED_SALARY, OS_PER_DELIVERED, CONSULTANT).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 需要进行试用期考核的用工类型
     */
    public static final List<String> TYPE_OF_REQUIRED_PROBATION
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 需要设置福利假默认的用工类型
     */
    public static final List<String> TYPE_OF_DEFAULT_WELFARE_LEAVE
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE, INTERN, PART_TIMER, CONSULTANT).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 仓内人员的用工类型
     */
    public static final List<String> TYPE_OF_DEFAULT_WAREHOUSE
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE, OS_FIXED_SALARY).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * MEX仓内可生成无排班计划异常的用工类型
     */
    public static final List<String> TYPE_OF_NOT_SUPPORT_NO_SCHEDULING_PLAN_WAREHOUSE
            = Lists.newArrayList(OS_FIXED_SALARY, OS_PER_DELIVERED, FREELANCER, PART_TIMER, CONSULTANT).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 仓内自有人员的用工类型
     */
    public static final List<String> TYPE_OF_EMPLOYEE_WAREHOUSE
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());

    /**
     * 需要填工龄的用工类型
     */
    public static final List<String> TYPE_OF_WORK_SENIORITY
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());


    /**
     * 员工、挂靠、实习生、兼职
     */
    public static boolean canTransform(String employeeType) {
        if (StringUtils.isBlank(employeeType)) {
            return Boolean.FALSE;
        }
        return TYPE_OF_OWN.contains(employeeType);
    }

    /**
     * 是否可显示签约主体
     */
    public static boolean showVendor(String employeeType) {
        if (StringUtils.isBlank(employeeType)) {
            return Boolean.FALSE;
        }

        return TYPE_OF_DEFAULT_VENDOR.contains(employeeType);
    }

    /**
     * 需要设置考勤提醒的用工类型
     */
    public static final List<String> TYPE_OF_ATTENDANCE_REMIND_EMPLOYEE_TYPE
            = Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE, INTERN, CONSULTANT).stream()
            .map(EmploymentTypeEnum::getCode)
            .collect(Collectors.toList());


    /**
     * KSA、UAE、MEX、OMN、QAT、KWT、BRA排班(员工、挂靠、实习生、顾问、劳务派遣)
     */
    public static final List<String> SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT =
            Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE, INTERN, CONSULTANT, OS_FIXED_SALARY)
                    .stream()
                    .map(EmploymentTypeEnum::getCode)
                    .collect(Collectors.toList());
    /**
     * 所有国家排班(员工、挂靠、实习生、顾问)
     */
    public static final List<String> ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT =
            Lists.newArrayList(EMPLOYEE, SUB_EMPLOYEE, INTERN, CONSULTANT)
                    .stream()
                    .map(EmploymentTypeEnum::getCode)
                    .collect(Collectors.toList());


    public static boolean isOwn(String employeeType) {
        if (StringUtils.isBlank(employeeType)) {
            return Boolean.FALSE;
        }
        // 员工、实习生、兼职、挂靠
        return TYPE_OF_OWN.contains(employeeType);

    }

    public static boolean areOsFixedSalary(String employeeType) {
        if (StringUtils.isBlank(employeeType)) {
            return Boolean.FALSE;
        }
        return OS_FIXED_SALARY.getCode().equals(employeeType);
    }

    /**
     * 考勤系统用户国家和用工类型有效范围匹配
     *
     * @param locationCountry 常驻国
     * @param employeeType    用工类型
     */
    public static Boolean employeeTypeEffectiveDetect(String locationCountry, String employeeType) {
        if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(locationCountry)) {
            return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        } else {
            return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        }
    }
}
