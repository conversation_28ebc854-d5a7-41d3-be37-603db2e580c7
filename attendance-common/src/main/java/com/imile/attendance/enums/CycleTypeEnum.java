package com.imile.attendance.enums;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.ucenter.api.context.RequestInfoHolder;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 计薪周期类型
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/12/29
 */
@Getter
public enum CycleTypeEnum {

    /**
     * 月
     */
    MONTH(BusinessConstant.ONE_MONTH) {
        @Override
        public List<CycleDetail> getCycleDetailList() {
            Integer count = 1;
            List<CycleDetail> cycleDetails = new ArrayList<>();
            while (count <= MONTH_DAYS) {
                cycleDetails.add(getCycleEndDetail(String.valueOf(count)));
                count++;
            }
            return cycleDetails;
        }

        @Override
        public Date getCycleDate(Date nowDate, String cycleCode, int offset) {
            // 此处不考虑数据的合法性
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(nowDate);
            calendar.add(Calendar.MONTH, offset);
            // 设置日期
            if (END_OF_MONTH_CODE.equals(cycleCode)) {
                return DateUtil.beginOfDay(DateUtil.endOfMonth(calendar.getTime()));
            }
            calendar.set(Calendar.DAY_OF_MONTH, Integer.parseInt(cycleCode));
            return DateUtil.beginOfDay(calendar.getTime());
        }

        /**
         * 根据当前时间获取周的开始结束时间
         *
         * @param nowDate
         * @param cycleStart
         * @param cycleEnd
         * @return
         */
        @Override
        public CycleRangDetail getCurrentRange(Date nowDate, String cycleStart, String cycleEnd) {
            // 1. 判断是否是整月周期
            if (isFullMonthCycle(cycleStart, cycleEnd)) {
                // 整月周期：从当月的第一天到当月的最后一天
                return new CycleRangDetail(
                        cycleStart,
                        DateUtil.beginOfMonth(nowDate),
                        cycleEnd,
                        DateUtil.endOfMonth(nowDate)
                );
            }

            // 2. 根据当前日期判断是上个月周期还是当前周期
            int currentDayOfMonth = DateUtil.dayOfMonth(nowDate);

            // 此时cycleEnd为数字，将周期起始日和截止日转换为整数
            int cycleStartInt = Integer.parseInt(cycleStart);
            int cycleEndInt = Integer.parseInt(cycleEnd);

            if (currentDayOfMonth < cycleStartInt) {
                // 当前日期在周期的起始日之前 -> 属于上个月的周期
                Date startOfPreviousMonthCycle = calculateStartOfPreviousMonthCycle(nowDate, cycleStartInt);
                Date endOfCurrentMonthCycle = calculateEndOfCurrentMonthCycle(nowDate, cycleEndInt);

                // 设置周期范围为：上个月的起始日到本月的截止日
                return new CycleRangDetail(
                        cycleStart,
                        startOfPreviousMonthCycle,
                        cycleEnd,
                        endOfCurrentMonthCycle
                );
            } else {
                // 当前日期在周期的起始日之后或等于起始日 -> 属于当前周期
                Date startOfCurrentMonthCycle = calculateStartOfCurrentMonthCycle(nowDate, cycleStartInt);
                Date endOfNextMonthCycle = calculateEndOfNextMonthCycle(nowDate, cycleEndInt);

                // 设置周期范围为：本月的起始日到下个月的截止日
                return new CycleRangDetail(
                        cycleStart,
                        startOfCurrentMonthCycle,
                        cycleEnd,
                        endOfNextMonthCycle
                );
            }
        }


        @Override
        public CycleDetail getCycleEndDetail(String cycleStart) {
            // 月底
            if (END_OF_MONTH_CODE.equals(cycleStart)) {
                return new CycleDetail(cycleStart, RequestInfoHolder.isChinese() ? END_OF_MONTH_CN : END_OF_MONTH_EN, END_OF_MONTH_CODE, RequestInfoHolder.isChinese() ? END_OF_MONTH_CN : END_OF_MONTH_EN);
            }
            if (START_OF_MONTH.equals(cycleStart)) {
                return new CycleDetail(cycleStart, cycleStart + (RequestInfoHolder.isChinese() ? MONTH_SUFFIX_CN : MONTH_SUFFIX_EN), END_OF_MONTH_CODE, RequestInfoHolder.isChinese() ? END_OF_MONTH_CN : END_OF_MONTH_EN);
            }
            return new CycleDetail(cycleStart, cycleStart + (RequestInfoHolder.isChinese() ? MONTH_SUFFIX_CN : MONTH_SUFFIX_EN), String.valueOf(Integer.parseInt(cycleStart) - 1), (Integer.parseInt(cycleStart) - 1) + (RequestInfoHolder.isChinese() ? MONTH_SUFFIX_CN : MONTH_SUFFIX_EN));
        }

        @Override
        public Integer getActualAbnormalExpired(Date nowDate, String cycleStart, String cycleEnd, Integer abnormalExpired) {
            if (END_OF_MONTH_CODE.equals(cycleEnd)) {
                //跳月
                return -abnormalExpired;
            }
            if (Integer.parseInt(cycleStart) > DateUtil.dayOfMonth(nowDate)) {
                //跳月
                return -abnormalExpired;
            } else {
                return -abnormalExpired + 1;
            }
        }
    },
    /**
     * 周
     */
    WEEK(BusinessConstant.ONE_WEEK) {
        @Override
        public List<CycleDetail> getCycleDetailList() {
            List<CycleDetail> cycleDetails = new ArrayList<>();
            for (String weekDaysCode : WEEK_DAYS_CODES) {
                cycleDetails.add(this.getCycleEndDetail(weekDaysCode));
            }
            return cycleDetails;
        }

        @Override
        public Date getCycleDate(Date nowDate, String cycleCode, int offset) {

            // 此处不考虑数据的合法性
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(nowDate);
            calendar.add(Calendar.WEEK_OF_YEAR, offset);
            calendar.set(Calendar.DAY_OF_WEEK, WEEK_DAYS_CODES_CACHE.get(cycleCode) + 1);
            return DateUtil.beginOfDay(calendar.getTime());
        }

        /**
         * 根据当前时间获取周的开始结束时间
         *
         * @param nowDate    当前时间
         * @param cycleStart 周期开始
         * @param cycleEnd   周期结束
         * @return CycleWeekDetail
         */
        @Override
        public CycleRangDetail getCurrentRange(Date nowDate, String cycleStart, String cycleEnd) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(nowDate);
            // 设置为当前周的第一天
            Integer cycleStartInt = WEEK_DAYS_CODES_CACHE.get(cycleStart);
            Integer cycleEndInt = WEEK_DAYS_CODES_CACHE.get(cycleEnd);
            if (ObjectUtil.isNull(cycleStartInt) || ObjectUtil.isNull(cycleEndInt)) {
                return null;
            }

            int cycleStartIndex = cycleStartInt + 1;
            int cycleEndIndex = cycleEndInt + 1;
            calendar.setFirstDayOfWeek(cycleStartIndex);
            calendar.set(Calendar.DAY_OF_WEEK, cycleStartIndex);
            Date currentWeekStart = DateUtil.beginOfDay(calendar.getTime());

            // 设置为当前周的最后一天（周六）
            calendar.set(Calendar.DAY_OF_WEEK, cycleEndIndex);
            Date currentWeekEnd = DateUtil.endOfDay(calendar.getTime());

            return new CycleRangDetail(cycleStart, currentWeekStart, cycleEnd, currentWeekEnd);
        }


        @Override
        public CycleDetail getCycleEndDetail(String cycleStart) {
            Integer index = WEEK_DAYS_CODES_CACHE.get(cycleStart);
            if (index == null) {
                return null;
            }
                  /*
                       0 ->6  0+6/7
                       1 ->0 1+6/7
                       2 ->1 2+6/7
                       6 ->5 6+6/7
                     */
            int endIndex = (index + WEEK_DAYS_CODES.length - 1) % WEEK_DAYS_CODES.length;
            return new CycleDetail(WEEK_DAYS_CODES[index], RequestInfoHolder.isChinese() ? WEEK_DAYS_CN[index] : WEEK_DAYS_EN[index], WEEK_DAYS_CODES[endIndex], RequestInfoHolder.isChinese() ? WEEK_DAYS_CN[endIndex] : WEEK_DAYS_EN[endIndex]);
        }

        @Override
        public Integer getActualAbnormalExpired(Date nowDate, String cycleStart, String cycleEnd, Integer abnormalExpired) {
            return -abnormalExpired + 1;
        }
    };

    CycleTypeEnum(BigDecimal totalDays) {
        this.totalDays = totalDays;
    }

    /**
     * 周期内的总天数
     */
    private BigDecimal totalDays;

    /**
     * 生成对应的周期
     *
     * @return
     */
    public abstract List<CycleDetail> getCycleDetailList();


    /**
     * 以当前日期作为参考，返回周期编码对应的具体日期
     * 示例：入参 ->  2022 1 15 返回 2022年1月15日
     * 入参 ->   2022 1 SATURDAY 表示返回2022年第一周的SATURDAY 即 2022-01-01
     *
     * @param nowDate   当前日期
     * @param cycleCode 周期编码，比如月 传15、16,周传：SATURDAY
     * @param offset    正数向未来偏移，负数向历史偏移
     * @return
     */
    public abstract Date getCycleDate(Date nowDate, String cycleCode, int offset);

    /**
     * 根据当前时间获取周的开始结束时间
     *
     * @param nowDate
     * @param cycleStart
     * @param cycleEnd
     * @return
     */
    public abstract CycleRangDetail getCurrentRange(Date nowDate, String cycleStart, String cycleEnd);

    /**
     * 根据周期开始日期获取结束日期
     *
     * @param cycleStart
     * @return
     */
    public abstract CycleDetail getCycleEndDetail(String cycleStart);


    public abstract Integer getActualAbnormalExpired(Date nowDate, String cycleStart, String cycleEnd, Integer abnormalExpired);

    /**
     * 月底标记
     */
    public static final String END_OF_MONTH_CODE = "END_OF_MONTH";
    public static final String END_OF_MONTH_CN = "月底";
    public static final String END_OF_MONTH_EN = "End of this month";

    /**
     * 月初标记
     */
    public static final String START_OF_MONTH = "1";
    /**
     * 目前最多28天
     */
    public static final Integer MONTH_DAYS = 28;
    public static final String MONTH_SUFFIX_CN = "号";
    public static final String MONTH_SUFFIX_EN = "";
    public static final String[] WEEK_DAYS_CODES = new String[]{"SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"};

    public static final String[] WEEK_DAYS_CN = new String[]{"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
    public static final String[] WEEK_DAYS_EN = new String[]{"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};

    private static final Map<String, Integer> WEEK_DAYS_CODES_CACHE;

    static {
        WEEK_DAYS_CODES_CACHE = new LinkedHashMap<>();
        for (int i = 0; i < WEEK_DAYS_CODES.length; i++) {
            WEEK_DAYS_CODES_CACHE.put(WEEK_DAYS_CODES[i], i);
        }
    }

    public static CycleTypeEnum getInstance(String cycleType) {
        for (CycleTypeEnum value : CycleTypeEnum.values()) {
            if (value.name().equals(cycleType)) {
                return value;
            }
        }
        return null;
    }

    @Getter
    public static class CycleDetail {
        public CycleDetail(String cycleStart, String cycleStartDesc, String cycleEnd, String cycleEndDesc) {
            this.cycleStart = cycleStart;
            this.cycleStartDesc = cycleStartDesc;
            this.cycleEnd = cycleEnd;
            this.cycleEndDesc = cycleEndDesc;
        }

        /**
         * 周期开始日期编码 如果是月，则值为1，2,3,4.....28,END_OF_MONTH表示月底
         * 如果是周，则值为 1,2,3,4,5,6,7表示周日
         */
        private String cycleStart;
        /**
         * 周期开始 描述
         */
        private String cycleStartDesc;
        /**
         * 周期结束编码
         */
        private String cycleEnd;
        /**
         * 周期结束 描述
         */
        private String cycleEndDesc;
    }

    @Getter
    @ToString
    public static class CycleRangDetail {
        public CycleRangDetail(String cycleStart, Date cycleStartDate, String cycleEnd, Date cycleEndDate) {
            this.cycleStart = cycleStart;
            this.cycleStartDate = cycleStartDate;
            this.cycleEnd = cycleEnd;
            this.cycleEndDate = cycleEndDate;
        }

        /**
         * 周期开始日期编码 如果是月，则值为1，2,3,4.....28,END_OF_MONTH表示月底
         * 如果是周，则值为 1,2,3,4,5,6,7表示周日
         */
        private String cycleStart;
        /**
         * 周期开始
         */
        private Date cycleStartDate;
        /**
         * 周期结束编码
         */
        private String cycleEnd;
        /**
         * 周期结束
         */
        private Date cycleEndDate;
    }


    private static boolean isFullMonthCycle(String cycleStart, String cycleEnd) {
        return StringUtils.equalsIgnoreCase(START_OF_MONTH, cycleStart) &&
                StringUtils.equalsIgnoreCase(END_OF_MONTH_CODE, cycleEnd);
    }


    /**
     * 计算上个月周期的起始时间
     */
    private static Date calculateStartOfPreviousMonthCycle(Date dayDate, int cycleStartInt) {
        // 获取上个月第一天，再偏移到指定的周期起始日
        Date previousMonthStart = DateUtil.beginOfMonth(DateUtil.offsetMonth(dayDate, -1));
        return DateUtil.beginOfDay(DateUtil.offsetDay(previousMonthStart, cycleStartInt - 1));
    }

    /**
     * 计算本月周期的起始时间
     */
    private static Date calculateStartOfCurrentMonthCycle(Date dayDate, int cycleStartInt) {
        // 获取本月第一天，再偏移到指定的周期起始日
        return DateUtil.beginOfDay(DateUtil.offsetDay(DateUtil.beginOfMonth(dayDate), cycleStartInt - 1));
    }

    /**
     * 计算本月周期的截止时间
     */
    private static Date calculateEndOfCurrentMonthCycle(Date dayDate, int cycleEndInt) {
        // 获取本月第一天，再偏移到指定的周期截止日
        return DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.beginOfMonth(dayDate), cycleEndInt - 1));
    }

    /**
     * 计算下个月周期的截止时间
     */
    private static Date calculateEndOfNextMonthCycle(Date dayDate, int cycleEndInt) {
        // 获取下个月第一天，再偏移到指定的周期截止日
        Date nextMonthStart = DateUtil.beginOfMonth(DateUtil.offsetMonth(dayDate, 1));
        return DateUtil.endOfDay(DateUtil.offsetDay(nextMonthStart, cycleEndInt - 1));
    }


    /**
     * 获取考勤周期结束时间
     *
     * @param cycleType 考勤周期频次
     * @param cycleCode 考勤周期开始时间
     * @return CycleDetail
     */
    public static CycleDetail getCycleDetail(String cycleType, String cycleCode) {
        CycleDetail cycleEndDetail = Optional.ofNullable(CycleTypeEnum.getInstance(cycleType))
                .map(cycleTypeEnum -> cycleTypeEnum.getCycleEndDetail(cycleCode))
                .orElse(null);
        BusinessLogicException.checkTrue(cycleEndDetail == null,
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "cycleType/cycleCode");
        return cycleEndDetail;
    }

    public static void main(String[] args) {
//        CycleTypeEnum.CycleRangDetail currentMonthRange = CycleTypeEnum.MONTH.getCurrentRange(
//                new Date(),
//                START_OF_MONTH,
//                END_OF_MONTH_CODE
//        );
//        System.out.println(currentMonthRange);
//        CycleTypeEnum.CycleRangDetail currentMonthRange2 = CycleTypeEnum.MONTH.getCurrentRange(
//                new Date(),
//                "25",
//                "28"
//        );
//        System.out.println(currentMonthRange2);
        for (String weekDaysCode : WEEK_DAYS_CODES) {
            CycleDetail cycleEndDetail = WEEK.getCycleEndDetail(weekDaysCode);
            System.out.println(cycleEndDetail.getCycleStart() + " " + cycleEndDetail.getCycleStartDesc() + " " + cycleEndDetail.getCycleEnd() + " " + cycleEndDetail.getCycleEndDesc());
        }
    }
}
