package com.imile.attendance.enums.approval;

import lombok.Getter;

/**
 * 补时长审批模板自定义的字段
 */
@Getter
public enum AddDurationCustomFieldEnum {
    USER_NAME("userName", "被申请人姓名"),
    USER_CODE("userCode", "被申请人编码"),
    DEPT_NAME("deptName", "被申请人部门"),
    WORK_OC_NAME("workOcName", "工作网点"),
    WORK_VENDOR_NAME("workVendorName", "工作供应商"),
    EMPLOYMENT_TYPE("employmentType", "用工类型"),

    ABNORMAL_DATE("abnormalDate", "异常日期"),
    ABNORMAL_TYPE("abnormalType", "异常类型"),
    ABNORMAL_TYPE_DESC("abnormalTypeDesc", "异常类型描述"),
    ACTUAL_ATTENDANCE_TIME("actualAttendanceTime", "实出勤时长"),
    ACTUAL_WORKING_HOURS("actualWorkingHours", "实工作时长"),
    NEW_ACTUAL_ATTENDANCE_TIME("newActualAttendanceTime", "更新后的实出勤时长"),
    NEW_ACTUAL_WORKING_HOURS("newActualWorkingHours", "更新后的实工作时长"),
    CLASS_NAME("className", "班次"),

    REMARK("remark", "备注"),
    ATTACHMENT("attachment", "附件"),

    //角色字段
    BE_APPROVERED_USER_ID("beApproveredUserId", "被审批人ID"),
    DEPT_ID("deptId", "被申请人部门ID"),

    //判断条件
    USER_COUNTRY("userCountry", "被申请人所在国"),
    USER_ORIGIN_COUNTRY("userOriginCountry", "被申请人结算国"),
    ;

    private String code;

    private String desc;

    AddDurationCustomFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
