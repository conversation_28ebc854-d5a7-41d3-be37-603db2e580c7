package com.imile.attendance.enums;

import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21 
 * @Description
 */
@Getter
public enum RangeTypeEnum {


    /**
     * 用户
     */
    USER("USER", 2),

    /**
     * 部门
     */
    DEPT("DEPT", 1),

    /**
     * 默认
     */
    DEFAULT("DEFAULT", 0),
    ;

    private String code;

    private Integer priority;


    RangeTypeEnum(String code, Integer priority) {
        this.code = code;
        this.priority = priority;
    }

    public static RangeTypeEnum getInstance(String code) {
        for (RangeTypeEnum value : RangeTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
