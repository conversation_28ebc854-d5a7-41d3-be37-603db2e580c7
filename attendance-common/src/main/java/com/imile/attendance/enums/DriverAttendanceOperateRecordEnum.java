package com.imile.attendance.enums;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} DriverOperateRecordEnum
 * {@code @since:} 2024-01-24 17:40
 * {@code @description:}
 */
@Getter
public enum DriverAttendanceOperateRecordEnum {
    DEFAULT(0, "",""),
    MODIFY_ATTENDANCE(1, "修改考勤","Modify attendance"),
    EXPORT(2, "导出","Export"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, DriverAttendanceOperateRecordEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (DriverAttendanceOperateRecordEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    DriverAttendanceOperateRecordEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static DriverAttendanceOperateRecordEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
