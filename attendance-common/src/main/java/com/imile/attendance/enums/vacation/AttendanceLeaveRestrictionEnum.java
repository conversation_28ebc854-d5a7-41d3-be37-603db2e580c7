package com.imile.attendance.enums.vacation;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024/4/30
 */
public enum AttendanceLeaveRestrictionEnum {
    EMPLOYED(1, "入职后即可使用"),
    REGULAR(2, "转正后方可使用"),
    ONE_YEAR(3, "入职一年后方可使用"),
    TWO_YEAR(4, "入职两年后方可使用");

    private Integer code;

    private String value;

    private static final Map<Integer, AttendanceLeaveRestrictionEnum> cacheMap = new ConcurrentHashMap<Integer, AttendanceLeaveRestrictionEnum>();

    static {
        for (AttendanceLeaveRestrictionEnum codeEnum : values()) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }
    }

    AttendanceLeaveRestrictionEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static AttendanceLeaveRestrictionEnum getIsInvalidEnum(Integer code) {
        if (code == null) {
            return null;
        }
        return cacheMap.get(code);
    }
}
