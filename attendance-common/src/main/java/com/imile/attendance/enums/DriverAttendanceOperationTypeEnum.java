package com.imile.attendance.enums;


import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@code @author:} allen
 * {@code @className:} DriverOperationType
 * {@code @since:} 2024-01-20 14:29
 * {@code @description:}
 */
@Getter
public enum DriverAttendanceOperationTypeEnum {
    DEFAULT(0, "",""),
    DLD_SIGN(1, "DLD签收","Dld sign"),
    DA_LOCUS_PUNCH(2, "轨迹打卡","Track punch"),
    LEAVE(3, "请假","Leave"),
    MODIFY_ATTENDANCE(4, "修改考勤","Modify attendance"),
    ;

    private final Integer type;

    private final String desc;

    private final String descEn;

    private static final Map<Integer, DriverAttendanceOperationTypeEnum> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (DriverAttendanceOperationTypeEnum codeEnum : values()) {
            CACHE_MAP.put(codeEnum.getType(), codeEnum);
        }
    }

    DriverAttendanceOperationTypeEnum(Integer type, String desc, String descEn) {
        this.type = type;
        this.desc = desc;
        this.descEn = descEn;
    }

    public static DriverAttendanceOperationTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        return CACHE_MAP.get(type);
    }
}
