package com.imile.attendance.enums.rule;

import lombok.Getter;

/**
 * <AUTHOR> chen
 * @Date 2025/4/15 
 * @Description 规则范围类型枚举
 */
@Getter
public enum RuleRangeTypeEnum {

    /**
     * 用户
     */
    USER("USER", 1),

    /**
     * 部门
     */
    DEPT("DEPT", 2),


    /**
     * 国家
     */
    COUNTRY("COUNTRY", 3),


    ;

    private final String code;

    private final Integer priority;


    RuleRangeTypeEnum(String code, Integer priority) {
        this.code = code;
        this.priority = priority;
    }

    public static RuleRangeTypeEnum getInstance(String code) {
        for (RuleRangeTypeEnum value : RuleRangeTypeEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }
}
