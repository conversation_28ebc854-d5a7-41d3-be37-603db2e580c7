package com.imile.attendance.enums;

import lombok.Getter;

/**
 * 管理员类型（前端配置化）
 */
@Getter
public enum RoleTypeEnum {

    SYSTEM("SYSTEM", "系统管理员"),
    ORG("ORG", "一级组织管理员"),
    NONE("NONE", "非管理员"),
    DEPT("DEPT", "部门管理员"),
    VENDOR("VENDOR", "供应商管理员"),

    ;

    private String code;

    private String desc;

    RoleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
