package com.imile.attendance.util;

import com.google.common.collect.Lists;
import com.imile.util.asserts.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.Annotation;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class ReflectionUtils {
    private ReflectionUtils() {
    }

    private static final ConcurrentHashMap<String, Object> typeCache = new ConcurrentHashMap<>();

    public static <T> T newInstance(Class<T> type) throws Exception {
        try {
            Constructor<T> constructor = type.getConstructor();
            return constructor.newInstance();
        } catch (Exception e) {
            throw new Exception("exception occured in method newInstance for type : " + type.toString(), e);
        }
    }

    public static <T> T newInstance(String className) throws Exception {
        Class<T> clazz = (Class<T>) classForName(className);
        try {
            return newInstance(clazz);
        } catch (Exception e) {
            throw new Exception("exception occured in method newInstance for className : " + className, e);
        }

    }

    public static Class<?> classForName(String name) throws ClassNotFoundException {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        Class<?> type = (Class<?>) typeCache.get(name);
        type = type != null ? type : Class.forName(name);
        typeCache.putIfAbsent(name, type);

        return type;
    }

    public static List<Method> listGetMethod(Class<?> type) {
        List<Method> methods = new LinkedList<>();
        for (Method method : type.getMethods()) {
            if (method.getGenericParameterTypes().length == 0) {
                String methodName = method.getName();
                if (methodName.startsWith("get") && methodName.length() > 3 && !method.isBridge()) {
                    methods.add(method);
                } else if (methodName.startsWith("is") && methodName.length() > 2 && !method.isBridge()) {
                    methods.add(method);
                }
            }
        }

        return methods;
    }

    public static Method listGetMethodWithName(Class<?> type, String name) {
        String nameSubfix = name.substring(0, 1).toUpperCase() + name.substring(1);
        for (Method method : type.getMethods()) {
            if (method.getGenericParameterTypes().length == 0) {
                String methodName = method.getName();
                if (methodName.startsWith("get") && methodName.length() > 3 && (nameSubfix.length() + 3) == methodName.length()
                        && !method.isBridge() && methodName.endsWith(nameSubfix)) {
                    return method;
                } else if (methodName.startsWith("is") && methodName.length() > 2 && (nameSubfix.length() + 2) == methodName.length()
                        && !method.isBridge() && methodName.endsWith(nameSubfix)) {
                    return method;
                }
            }
        }
        return null;
    }

    public static List<Method> listGetMethodWithAnnotations(Class<?> type, Class<? extends Annotation>... annotations) {
        List<Method> getMethods = listGetMethod(type);
        return filterMethodsWithAnnotations(getMethods, annotations);
    }

    public static List<Field> getDeclaredFields(Class<?> clazz) {
        List<Field> fieldList = (List<Field>) typeCache.get("getDeclaredFields" + clazz);
        if (fieldList == null) {
            fieldList = new ArrayList<>();
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null) {
                fieldList.addAll(getDeclaredFields(superClass));
            }
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            typeCache.putIfAbsent("getDeclaredFields" + clazz, fieldList);
            return fieldList;
        }
        return fieldList;
    }

    public static List<Field> getDeclaredFields(Object obj) {
        if (null == obj) {
            return Lists.newArrayList();
        }
        return getDeclaredFields(obj.getClass());
    }

    public static List<Method> listSetMethod(Class<?> type) {
        List<Method> methods = new LinkedList<>();
        Method[] allMethods = type.getMethods();
        for (Field field : getDeclaredFields(type)) {
            String ufName = null;
            String fieldName = field.getName();
            ufName = fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            for (Method method : allMethods) {
                String methodName = method.getName();
                if (methodName.startsWith("set" + ufName) && methodName.length() > 3 && !method.isBridge()) {
                    methods.add(method);
                }
            }
        }
        return methods;
    }

    public static List<Method> listSetMethodWithAnnotations(Class<?> type, Class<? extends Annotation>... annotations) {
        List<Method> methods = listSetMethod(type);
        return filterMethodsWithAnnotations(methods, annotations);
    }

    private static List<Method> filterMethodsWithAnnotations(List<Method> methods, Class<? extends Annotation>... annotations) {
        List<Method> list = new LinkedList<>();

        for (Iterator<Method> iterator = methods.iterator(); iterator.hasNext(); ) {
            Method method = iterator.next();
            for (Class<? extends Annotation> item : annotations) {
                if (method.isAnnotationPresent(item)) {
                    list.add(method);
                    continue;
                }
            }
        }
        return list;
    }

    public static Object invokeMethod(Method method, Object target, Object... args) {
        try {
            method.setAccessible(true);
            return method.invoke(target, args);
        } catch (IllegalArgumentException e) {
            log.error(e.getMessage(), e);
        } catch (IllegalAccessException e) {
            log.error(e.getMessage(), e);
        } catch (InvocationTargetException e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    @SuppressWarnings("unchecked")
    public static boolean isColleciton(Object obj) {
        return (obj instanceof Map || obj instanceof Collection);
    }

    @SuppressWarnings("unchecked")
    public static Iterator<?> iteratorColleciton(Object collecion) {
        if (collecion instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) collecion;
            return map.values().iterator();
        } else if (collecion instanceof Collection) {
            return ((Collection<?>) collecion).iterator();
        } else {
            return null;
        }
    }


    /**
     * 查找指定类中的字段，包括父类字段
     *
     * @param beanClass 要查找的类
     * @param fieldName 字段名
     * @return 找到的字段，如果未找到返回null
     */
    public static Field findField(Class<?> beanClass, String fieldName) {
        Assert.notNull(beanClass, "Class must not be null");
        Assert.notNull(fieldName, "Field name must not be null");
        
        // 从当前类开始向上查找字段
        Class<?> searchType = beanClass;
        while (searchType != null && searchType != Object.class) {
            try {
                return searchType.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                // 字段未在当前类中找到，继续查找父类
                searchType = searchType.getSuperclass();
            }
        }
        return null;
    }

    /**
     * 使字段可访问，特别是对于私有字段
     *
     * @param field 要设置的字段
     */
    public static void makeAccessible(Field field) {
        if (field == null) {
            return;
        }
        
        if ((!Modifier.isPublic(field.getModifiers()) ||
             !Modifier.isPublic(field.getDeclaringClass().getModifiers()) ||
             Modifier.isFinal(field.getModifiers())) && !field.isAccessible()) {
            field.setAccessible(true);
        }
    }

    /**
     * 使字段可访问并获取字段值
     *
     * @param field 要访问的字段
     * @param target 目标对象
     * @return 字段值
     * @throws IllegalAccessException 如果访问被拒绝
     */
    public static Object getFieldValue(Field field, Object target) throws IllegalAccessException {
        makeAccessible(field);
        return field.get(target);
    }

    /**
     * 使字段可访问并设置字段值
     *
     * @param field 要设置的字段
     * @param target 目标对象
     * @param value 要设置的值
     * @throws IllegalAccessException 如果访问被拒绝
     */
    public static void setFieldValue(Field field, Object target, Object value) throws IllegalAccessException {
        makeAccessible(field);
        field.set(target, value);
    }

    /**
     * 获取指定对象中指定字段名称的值
     *
     * @param target 目标对象
     * @param fieldName 字段名
     * @return 字段值，如果未找到或发生错误返回null
     */
    public static Object getFieldValue(Object target, String fieldName) {
        if (target == null || StringUtils.isEmpty(fieldName)) {
            return null;
        }
        
        try {
            Field field = findField(target.getClass(), fieldName);
            if (field == null) {
                return null;
            }
            return getFieldValue(field, target);
        } catch (IllegalAccessException e) {
            log.error("Failed to get field value: {}", fieldName, e);
            return null;
        }
    }

    /**
     * 设置指定对象中指定字段名称的值
     *
     * @param target 目标对象
     * @param fieldName 字段名
     * @param value 要设置的值
     * @return 是否设置成功
     */
    public static boolean setFieldValue(Object target, String fieldName, Object value) {
        if (target == null || StringUtils.isEmpty(fieldName)) {
            return false;
        }
        
        try {
            Field field = findField(target.getClass(), fieldName);
            if (field == null) {
                return false;
            }
            setFieldValue(field, target, value);
            return true;
        } catch (IllegalAccessException e) {
            log.error("Failed to set field value: {}", fieldName, e);
            return false;
        }
    }
}
