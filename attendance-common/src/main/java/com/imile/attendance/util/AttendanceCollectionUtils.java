package com.imile.attendance.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/6
 */
public class AttendanceCollectionUtils {
    private AttendanceCollectionUtils() {
    }

    /**
     * 集合去重，根据指定属性去重，一般以基础类型的属性作为去重判断。
     * 对象类型需要重写hashcode与equal方法，可以使用hutool CollectionUtil中的 distinct方法
     *
     * @param list   原始集合
     * @param getter get方法，用于作为去重比较，采用DTO::getXX形式传入
     * @param <T>    集合泛型，一般为DTO对象
     * @param <R>    get方法返回的值对应的类型
     * @return
     */
    public static <T, R> List<T> distinct(List<T> list, Function<T, R> getter) {
        if (list == null || list.size() == 0 || getter == null) {
            return list;
        }
        Set<R> cache = new HashSet<>();
        List<T> collect = list.stream().filter(t -> {
            // 利用SET去重
            if (!cache.contains(getter.apply(t))) {
                cache.add(getter.apply(t));
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        cache.clear();
        return collect;
    }

    /**
     * 根据属性匹配，查找集合中指定元素，如果存在多个，只返回其中一个
     *
     * @param collections 目标元素所在集合
     * @param key         需要匹配值
     * @param getter      对应get方法函数
     * @param <T>         元素返回类型
     * @param <R>         key对应的类型
     * @return
     */
    public static <T, R> T findElement(Collection<T> collections, R key, Function<T, R> getter) {
        return collections == null || key == null ? null : collections.stream().filter(t -> key.equals(getter.apply(t))).findFirst().orElse(null);
    }

    /**
     * 集合互相转换
     *
     * @param originDatas
     * @param clazz
     * @param <R>
     * @param <T>
     * @return
     */
    public static <R, T> List<T> convert(List<R> originDatas, Class<T> clazz) {
        if (originDatas == null || originDatas.isEmpty()) {
            return new ArrayList<>();
        }
        // 方案一：fastJson转换
        // 方案二：遍历，使用BeanUtil
        return JSONArray.parseArray(JSONObject.toJSONString(originDatas), clazz);
    }

    public static <R, T> T convertSingle(R originData, Class<T> clazz) {
        if (originData == null) {
            return null;
        }

        // 方案一：fastJson转换
        // 方案二：遍历，使用BeanUtil
        return JSONArray.parseObject(JSONObject.toJSONString(originData), clazz);
    }


}
