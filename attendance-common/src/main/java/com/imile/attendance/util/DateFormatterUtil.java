package com.imile.attendance.util;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.enums.MonthEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
public class DateFormatterUtil {

    private DateFormatterUtil() {
    }

    //系统固定时区
    public static final Integer SYSTEM_TIME_ZONE = 8;
    //时间格式常量
    public static final String FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String FORMAT_YYMMDD = "yyMMdd";
    public static final String SLASH_YYYYMMDD = "yyyy/MM/dd";
    public static final String FORMAT_YYYYMMDD_HH_MM_SS = "yyyyMMdd HH:mm:ss";
    public static final String FORMAT_YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static final String FIRST_DAY = "01-01";

    public static final String LAST_DAY = "12-31";

    /**
     * 根据页面时区转成8时区的时间
     *
     * @param date      时区转换
     * @param formatStr 时间格式
     * @return
     */
    public static Date translateDateForEight(Date date, String formatStr) throws ParseException {
        return DateFormatterUtil.translateDateByTimeZoneForImport(date, String.valueOf(SYSTEM_TIME_ZONE), formatStr);
    }


    /**
     * 北京时间转成时区时间
     *
     * @param date
     * @param timeZone
     * @return
     */
    public static Date translateDateToBeiJingByTimeZone(Date date, String timeZone) {
        Integer timeZoneHours = Integer.valueOf(timeZone);
        long resultTime = date.getTime() + (60 * 60 * 1000 * (SYSTEM_TIME_ZONE - timeZoneHours));
        return new Date(resultTime);
    }

    /**
     * 页面时间转换系统时间-按时区
     *
     * @param date      时区转换
     * @param timeZone  时区 -8 8
     * @param formatStr 时间格式
     * @return
     */
    public static Date translateDateByTimeZone(Date date, String timeZone, String formatStr) throws ParseException {
        Integer timeZoneHours = Integer.valueOf(timeZone);
        Long resultTime = date.getTime() + (60 * 60 * 1000 * (timeZoneHours - SYSTEM_TIME_ZONE));
        date = new Date(resultTime);
        SimpleDateFormat df = new SimpleDateFormat(formatStr);
        String dateStr = df.format(date);
        date = df.parse(dateStr);
        return date;
    }

    public static Date translateDateByTimeZoneForImport(Date date, String timeZone, String formatStr) throws ParseException {
        Integer timeZoneHours = Integer.valueOf(timeZone);
        Long resultTime = date.getTime() + (60 * 60 * 1000 * (SYSTEM_TIME_ZONE - timeZoneHours));
        date = new Date(resultTime);
        SimpleDateFormat df = new SimpleDateFormat(formatStr);
        String dateStr = df.format(date);
        date = df.parse(dateStr);
        return date;
    }

    public static Date translateDateByTimeZone(Date date, String timeZone) {
        Integer timeZoneHours = Integer.valueOf(timeZone);
        long resultTime = date.getTime() + (60 * 60 * 1000 * (timeZoneHours - SYSTEM_TIME_ZONE));
        return new Date(resultTime);
    }

    /**
     * 页面时间转换系统时间-按时区
     *
     * @param date      时区转换
     * @param timeZone  时区 -8 8
     * @param formatStr 时间格式
     * @return
     */
    public static String translateDateStringByTimeZone(Date date, String timeZone, String formatStr) {
        Integer timeZoneHours = Integer.valueOf(timeZone);

        Long resultTime = date.getTime() + (60 * 60 * 1000 * (timeZoneHours - SYSTEM_TIME_ZONE));
        date = new Date(resultTime);
        SimpleDateFormat df = new SimpleDateFormat(formatStr);
        return df.format(date);
    }

    /**
     * 根据时区装成北京时间
     *
     * @param date
     * @param timeZone
     * @param formatStr
     */
    public static String translateDateForBeiJingDate(Date date, String timeZone, String formatStr) {
        Integer timeZoneHours = Integer.valueOf(timeZone);
        Long resultTime = date.getTime() + (60 * 60 * 1000 * (SYSTEM_TIME_ZONE - timeZoneHours));
        date = new Date(resultTime);
        SimpleDateFormat df = new SimpleDateFormat(formatStr);
        return df.format(date);

    }

    /**
     * 根据时区装成北京时间
     *
     * @param date
     * @param timeZone
     */
    public static Date translateDateForBeiJingDate(Date date, String timeZone) {
        Integer timeZoneHours = Integer.valueOf(timeZone);
        Long resultTime = date.getTime() + (60 * 60 * 1000 * (SYSTEM_TIME_ZONE - timeZoneHours));
        return new Date(resultTime);
    }

    /**
     * 将字符串转换按格式转换为时间类型
     *
     * @param type    时间格式
     * @param dateStr 时间字符串
     * @return
     */
    public static Date getNowDateByType(String type, String dateStr) {
        Date date = null;
        try {
            SimpleDateFormat df = new SimpleDateFormat(type);
            date = df.parse(dateStr);
        } catch (ParseException e) {
            log.error(DateFormatterUtil.class.toString(), e);
        }
        return date;
    }

    public static Date getTodayZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getYesterdayZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

    /**
     * 校验日期是否满足指定格式
     *
     * @param str 日期字符串
     * @return
     */
    public static Date validDate(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        Date date = null;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
        try {
            // 设置lenient为false.
            // 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            date = format.parse(str);
        } catch (ParseException e) {

            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            try {
                SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat(FORMAT_YYYY_MM_DD);
                date = simpleDateFormat2.parse(str);
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        }
        return date;
    }

    /**
     * dayId格式转换为  15-Nov-22格式  20220101
     */
    public static String dayIdFormat(Long dayId) {
        String day = dayId.toString().substring(6);
        String monthInt = dayId.toString().substring(4, 6);
        MonthEnum instance = MonthEnum.getInstance(monthInt);
        String month = instance.getDesc();
        String year = dayId.toString().substring(0, 4);
        return day + "-" + month + "-" + year;
    }

    /**
     * 2个时间段比较
     * @param startTime
     * @param endTime
     * @param startTime1
     * @param endTime2
     * @return
     */
    public static Boolean bijiao(String startTime, String endTime,String startTime1, String endTime2){
        //被比较的时间
        String t1=startTime1;
        String t2=endTime2;

        String t3=startTime;
        String t4=endTime;
        DateFormat d_Fm = new SimpleDateFormat("yyyy-mm-dd");
        try {
            Date T1=d_Fm.parse(t1);
            Date T2=d_Fm.parse(t2);
            Date T3=d_Fm.parse(t3);
            Date T4=d_Fm.parse(t4);
            if((
                    //T1-T2为第一个时间段  T3-T4为第二个时间段
                    //判断第一种情况，第二个时间段的结束时间T4在第一个时间段（T1-T2）内
//                          T1------T2
//                     T3--------t4
                    T3.getTime()<=T1.getTime() && T4.getTime()>=T1.getTime())||
                    //判断第二种情况，第二个时间段的开始时间T3在第一个时间段（T1-T2）内
//                     T1------T2
//                          T3--------t4
                    (T3.getTime()<=T2.getTime()) && (T4.getTime()>=T2.getTime())||
                    //判断第三种情况，第一个时间段（T1-T2）包含第二个时间段（T3-T4）
//                     T1------------------T2
//                          T3--------t4
                    (T3.getTime()>=T1.getTime() && T4.getTime()<=T2.getTime())||
                    //判断第四种情况，第二个时间段（T1-T2）包含第一个时间段（T3-T4）
//                          T1---------T2
//                     T3------------------t4
                    (T3.getTime()<=T1.getTime() && T4.getTime()>=T2.getTime())
            ){
                return true;
            }else{
                return false;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 2个时间段比较
     * @param T1
     * @param T2
     * @param T3
     * @param T4
     * @return
     */
    public static Boolean bijiaoDate(Date T3, Date T4,Date T1, Date T2){
        //被比较的时间
        if((
                //T1-T2为第一个时间段  T3-T4为第二个时间段
                //判断第一种情况，第二个时间段的结束时间T4在第一个时间段（T1-T2）内
//                          T1------T2
//                     T3--------t4
                T3.getTime()<=T1.getTime() && T4.getTime()>=T1.getTime())||
                //判断第二种情况，第二个时间段的开始时间T3在第一个时间段（T1-T2）内
//                     T1------T2
//                          T3--------t4
                (T3.getTime()<=T2.getTime()) && (T4.getTime()>=T2.getTime())||
                //判断第三种情况，第一个时间段（T1-T2）包含第二个时间段（T3-T4）
//                     T1------------------T2
//                          T3--------t4
                (T3.getTime()>=T1.getTime() && T4.getTime()<=T2.getTime())||
                //判断第四种情况，第二个时间段（T1-T2）包含第一个时间段（T3-T4）
//                          T1---------T2
//                     T3------------------t4
                (T3.getTime()<=T1.getTime() && T4.getTime()>=T2.getTime())
        ){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 完整的时间
     */
    public static String getCurrentYear(String date){
        Calendar currCal=Calendar.getInstance();
        Integer currentYear = currCal.get(Calendar.YEAR);
        return currentYear+"-"+date;
    }

    /**
     * 传入两个时间范围，返回这两个时间范围内的所有日期，并保存在一个集合中
     *
     * @param beginTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public static List<String> findEveryDay(Date beginTime, Date endTime){
        //创建一个放所有日期的集合
        List<String> dates = new ArrayList();

        //创建时间解析对象规定解析格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //将格式化后的第一天添加进集合
        dates.add(sdf.format(beginTime));

        //使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();

        //传入起始时间将此日历设置为起始日历
        calBegin.setTime(beginTime);

        //判断结束日期前一天是否在起始日历的日期之后
        while (endTime.after(calBegin.getTime())) {

            //根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);

            //得到的每一天就添加进集合
            dates.add(sdf.format(calBegin.getTime()));
            //如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }

    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Boolean compareOfYear(Date first, Date last, int numYear) {
        if (first == null || last == null) return false;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(first);
        calendar.add(Calendar.YEAR, numYear);
        if (calendar.getTime().getTime() > last.getTime()) {
            return false;
        }
        return true;
    }

    public static class AreaDates {
        public Date start;
        public Date end;

        public AreaDates(Date start, Date end) {
            this.start = start;
            this.end = end;
        }

        @Override
        public String toString() {
            return "Start: " + start + ", End: " + end;
        }
    }

    public static AreaDates getWeekDates(int year, int month, int weekOfMonth) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar.MONTH is 0-based
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        // 移动到该月的第一个周一
        while (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        // 移动到指定的周
        calendar.add(Calendar.WEEK_OF_MONTH, weekOfMonth - 1);

        // 如果开始日期不在指定的月份内，则调整到该月的第一天
        if (calendar.get(Calendar.MONTH) != month - 1) {
            calendar.set(Calendar.MONTH, month - 1);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
        }

        Date startDate = calendar.getTime();

        // 计算结束日期（开始日期加6天）
        calendar.add(Calendar.DAY_OF_MONTH, 6);

        // 如果结束日期超出了该月，则调整到该月的最后一天
        if (calendar.get(Calendar.MONTH) != month - 1) {
            calendar.set(Calendar.MONTH, month - 1);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        }

        Date endDate = calendar.getTime();

        return new AreaDates(startDate, endDate);
    }

    public static AreaDates getQuarterDates(int year, int quarter) {
        if (quarter < 1 || quarter > 4) {
            throw new IllegalArgumentException("Quarter must be between 1 and 4");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);

        // 设置季度开始月份
        int startMonth = (quarter - 1) * 3;
        calendar.set(Calendar.MONTH, startMonth);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date startDate = calendar.getTime();

        // 设置季度结束月份
        int endMonth = startMonth + 2;
        calendar.set(Calendar.MONTH, endMonth);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date endDate = calendar.getTime();

        return new AreaDates(startDate, endDate);
    }

    public static AreaDates getMonthDates(int year, int month) {
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("Month must be between 1 and 12");
        }

        LocalDate startLocalDate = LocalDate.of(year, month, 1);
        LocalDate endLocalDate = startLocalDate.plusMonths(1).minusDays(1);

        Date startDate = Date.from(startLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        return new AreaDates(startDate, endDate);
    }

    /**
     * 转换时间 格式为 yyyy-MM-dd HH:mm:ss
     *
     * @param date 日期 yyyy-MM-dd
     * @param dateTime 时间 HH:mm:ss
     * @return date
     */
    public static Date convertDateTime(Date date,Date dateTime){
        if (Objects.isNull(date) || Objects.isNull(dateTime)){
            return new Date();
        }
       return DateUtil.parse(DateUtil.format(date, DateFormatterUtil.FORMAT_YYYY_MM_DD) + " " + DateUtil.format(dateTime, DateConvertUtils.FORMAT_HH_MM_SS), DateFormatterUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
    }

}
