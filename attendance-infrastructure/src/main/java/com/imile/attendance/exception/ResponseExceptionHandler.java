package com.imile.attendance.exception;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.result.Result;
import com.imile.util.ValidationUtil;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
@RestControllerAdvice
public class ResponseExceptionHandler {




    @ExceptionHandler(BusinessException.class)
    public Result<?> handleLocalizationException(BusinessException e) {
        log.info("业务异常:code=[{}],msg=[{}]", e.getCode(), e.getMessage());
        return Result.getFailResult(e.getCode(), e.getMessage());
    }


    @ExceptionHandler(BindException.class)
    public Result<?> bindException(BindException e){
        log.error("参数校验异常", e);
        return ValidationUtil.handleBindResult(e.getBindingResult());
    }


    @ExceptionHandler(ConstraintViolationException.class)
    public Result<?> processMethodArgumentNotValidException(ConstraintViolationException ex) {
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(violations)) {
            for (ConstraintViolation<?> constraintViolation : violations) {
                stringBuilder.append(I18nUtils.getMessage((constraintViolation.getMessageTemplate()))).append(";");
            }
        }
        return Result.getFailResult(ErrorCodeEnum.PARAM_VALIDATE_FAIL.getCode(), I18nUtils.getMessage(stringBuilder.toString()));
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleValidationExceptions(MethodArgumentNotValidException ex) {
        List<ObjectError> allErrors = ex.getBindingResult().getAllErrors();
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.isNotEmpty(allErrors)) {
            for (ObjectError error : allErrors) {
                sb.append(I18nUtils.getMessage((error.getDefaultMessage()))).append(";");
            }
        }
        return Result.getFailResult(ErrorCodeEnum.PARAM_VALIDATE_FAIL.getCode(), sb.toString());
    }



    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception e) {
        log.error("服务发生异常", e);
        return Result.getFailResult(ErrorCodeEnum.SERVICE_HANDLE_EXCEPTION.getCode(), ErrorCodeEnum.SERVICE_HANDLE_EXCEPTION.getMessage());
    }


}

