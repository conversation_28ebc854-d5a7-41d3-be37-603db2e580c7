package com.imile.attendance.interceptor;

import com.alibaba.fastjson.JSON;
import com.imile.common.exception.BusinessException;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.AbstractUTokenInterceptor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Component
public class AuthHandlerInterceptor extends AbstractUTokenInterceptor {
    /**
     * 复写这个方法可以实现检验失败之后要完成的操作 内置错误代码为 ERROR_CODE_NO_U_TOKEN ERROR_CODE_U_TOKEN_INVALID
     */
    @SneakyThrows
    @Override
    protected void afterVerifyFail(HttpServletRequest request, HttpServletResponse response, Object handler, String errorCode, String errorMessage) throws BusinessException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.getWriter().write(JSON.toJSONString(Result.getFailResult(errorCode, "Token invalid")));
    }

    /**
     * 复写这个方法可以实现Rpc调用异常之后要完成的操作
     */
    @SneakyThrows
    @Override
    protected void afterRpcException(HttpServletRequest request, HttpServletResponse response, String errorCode, String errorMessage) throws BusinessException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.getWriter().write(JSON.toJSONString(Result.getFailResult(errorCode, "ucenter service error")));
    }

    /**
     * 获得uToken 使用方需要复写这个方法
     */
    @Override
    protected String getUToken(HttpServletRequest request) {
        return StringUtils.substringAfter(request.getHeader(AbstractUTokenInterceptor.AUTHORIZATION_HEADER), AbstractUTokenInterceptor.TOKEN_PREFIX);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return super.preHandle(request, response, handler);
    }
}
