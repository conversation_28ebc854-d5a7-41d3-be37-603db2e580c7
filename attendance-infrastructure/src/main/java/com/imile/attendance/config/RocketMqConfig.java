package com.imile.attendance.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Data
@Configuration
@Slf4j
public class RocketMqConfig {

    @Value("${rocketmq.nameServer:********:9876;********:9876}")
    private String nameServAddr;

    @Value("${rocketmq.producer.group:pg_attendance}")
    private String groupName;

    @Bean
    public DefaultMQProducer defaultMqProducer() throws MQClientException {
        DefaultMQProducer producer = new DefaultMQProducer(this.groupName);
        producer.setNamesrvAddr(this.nameServAddr);
        producer.setVipChannelEnabled(false);
        producer.setRetryTimesWhenSendFailed(3);
        producer.setSendMsgTimeout(10000);
        producer.start();
        return producer;
    }
}
