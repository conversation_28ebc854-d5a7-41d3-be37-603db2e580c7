package com.imile.attendance.config;

import com.imile.attendance.util.OkHttpUtil;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Configuration
public class OkHttpConfig {


    @Bean
    public OkHttpUtil okHttpUtil() {
        OkHttpClient client = new OkHttpClient.Builder()
                //连接超时,建立到服务器的TCP连接所需的时间
                .connectTimeout(15, TimeUnit.SECONDS)
                //写入超时,发送HTTP请求到服务器所需的时间
                .writeTimeout(15, TimeUnit.SECONDS)
                //读取超时,等待服务器响应所需的时间,
                // 如果服务器处理请求通常需要一些时间，或者你的应用程序可以容忍较长的响应时间，
                // 你可以设置一个较长的读取超时（如10到30秒）
                .readTimeout(30, TimeUnit.SECONDS)
                //整个调用超时,整个HTTP请求生命周期的超时，包括连接、发送请求和接收响应,
                //通常这个值会大于连接超时、写入超时和读取超时中的任何一个
                .callTimeout(30, TimeUnit.SECONDS)
                //是否自动重连
                .retryOnConnectionFailure(true).connectionPool(new ConnectionPool())
                .hostnameVerifier((s, sslSession) -> true)
                .build();
        client.dispatcher().setMaxRequests(200);
        client.dispatcher().setMaxRequestsPerHost(10);
        return new OkHttpUtil(client);
    }

    @Bean(name = "okHttpUtilForSlow")
    public OkHttpUtil okHttpUtilForSlow() {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .callTimeout(60, TimeUnit.SECONDS)
                // 是否自动重连
                .retryOnConnectionFailure(true).connectionPool(new ConnectionPool())
                .hostnameVerifier((s, sslSession) -> true)
                .build();
        client.dispatcher().setMaxRequests(100);
        client.dispatcher().setMaxRequestsPerHost(5);
        return new OkHttpUtil(client);
    }
}
