package com.imile.attendance.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
public class ThreadPoolConfig {

    private static final String ATTENDANCE_TASK_THREAD_PREFIX = "ATTENDANCE-TASK-THREAD-";

    /**
     * pcs任务线程池
     */
    @Bean(name = "pcsTaskThreadPool")
    public Executor pcsTaskThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(1);
        //不能处理的使用调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadNamePrefix(ATTENDANCE_TASK_THREAD_PREFIX);
        executor.initialize();
        log.info("pcsThreadPool init corePoolSize={} maxPoolSize={} keepAliveSeconds={} queueCapacity={}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(),
                executor.getKeepAliveSeconds(), executor.getMaxPoolSize());
        return executor;
    }
}
