package com.imile.attendance.infrastructure.repository.driver.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailQuery
 * {@code @since:} 2024-01-22 16:32
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DriverAttendanceDetailQuery extends ResourceQuery implements Serializable {

    private static final long serialVersionUID = 8793721218383567249L;

    /**
     * 国家
     */
    private String country;

    /**
     * day_id 示例：20240124
     */
    private Long dayId;

    /**
     * dayDate 示例：2024-01-24
     * 司机日报查询条件
     */
    private Date dayDate;

    /**
     * dayDate 示例：2024-01-24 00:00:00,用来mybatis sql判断
     * 司机日报查询条件
     */
    private String dayDateString;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private List<Integer> attendanceTypeList;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 工作状态
     */
    private List<String> workStatusList;

    /**
     * 供应商code
     */
    private String vendorCode;

    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门id
     */
    private List<Long> deptIdList;


    /**
     * 汇报上级
     */
    private Long leaderId;


    /**
     * 部门id
     */
    private List<Long> deptList;

    /**
     * 国家列表
     */
    private List<String> countryList;

    /**
     * 是否司机
     */
    private Integer isDriver;
}
