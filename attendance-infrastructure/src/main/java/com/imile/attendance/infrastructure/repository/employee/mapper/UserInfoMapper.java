package com.imile.attendance.infrastructure.repository.employee.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/20
 * @Description
 */
@Mapper
@Repository
public interface UserInfoMapper extends BaseMapper<UserInfoDO> {

    UserDTO getUserByCode(String userCode);

    /**
     * 根据用户id获取用户的部门等属性(部门，岗位在rpc获取)
     */
    UserInformationDTO getUserInfoInformation(Long userId);

    /**
     * 联想查询
     */
    List<UserInfoDO> selectByAssociateCondition(UserDaoQuery query);

    /**
     * 员工考勤档案
     */
    List<UserArchiveDTO> userAttendanceArchive(UserArchiveQuery query);

}
