package com.imile.attendance.infrastructure.repository.abnormal.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 员工异常考勤数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Mapper
public interface EmployeeAbnormalAttendanceMapper extends AttendanceBaseMapper<EmployeeAbnormalAttendanceDO> {


    /**
     * 异常考勤分页查询
     *
     * @param query
     * @return
     */
    List<EmployeeAbnormalAttendanceDTO> list(EmployeeAbnormalAttendancePageQuery query);

    /**
     * 异常考勤分页查询(关联新考勤表)
     *
     * @param query
     * @return
     */
    List<EmployeeAbnormalAttendanceDTO> listNewAttendance(EmployeeAbnormalAttendancePageQuery query);


    Integer listCount(EmployeeAbnormalAttendancePageQuery query);
}
