package com.imile.attendance.infrastructure.repository.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Mapper
@Repository
public interface BaseDayInfoMapper extends BaseMapper<BaseDayInfoDO> {

    /**
     * 获取周期
     */
    List<BaseDayInfoDO> listCycle(@Param("year") Integer year);

}

