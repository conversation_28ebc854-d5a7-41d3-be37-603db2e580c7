package com.imile.attendance.infrastructure.repository.vacation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@Mapper
@Repository
public interface CompanyLeaveConfigCarryOverRangeMapper extends BaseMapper<CompanyLeaveConfigCarryOverRangeDO> {
}