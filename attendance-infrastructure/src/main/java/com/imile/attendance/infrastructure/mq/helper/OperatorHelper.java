package com.imile.attendance.infrastructure.mq.helper;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.hrms.api.base.result.OperatorDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 操作者信息帮助类
 * <p>
 * 该类用于在消息队列处理过程中设置操作者信息到用户上下文中。
 * 当系统处理来自消息队列的消息时，通常没有用户登录上下文，
 * 此类帮助设置必要的用户信息，以便系统可以正确记录操作者，
 * 并在需要时提供用户上下文信息。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/5/26
 * @see RequestInfoHolder
 * @see UserContext
 */
public class OperatorHelper {

    private static final String SYSTEM_USER_CODE = "210871";
    private static final String SYSTEM_USER_NAME = "admin";

    /**
     * 设置操作者信息到用户上下文中
     * <p>
     * 根据提供的OperatorDTO对象设置用户上下文信息。
     * 如果提供的operator为null，将使用默认的管理员信息。
     * </p>
     *
     * @param operator 操作者信息对象，可以为null
     * @see RequestInfoHolder#setLoginInfo(UserContext)
     */
    public static void putOperatorInfo(OperatorDTO operator) {
        // 创建新的用户上下文对象
        UserContext userContext = new UserContext();
        if (Objects.isNull(operator)) {
            // 如果操作者信息为空，使用默认的管理员信息
            userContext.setUserCode(SYSTEM_USER_CODE);
            userContext.setUserName(SYSTEM_USER_NAME);
            userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        } else {
            // 使用提供的操作者信息
            userContext.setUserCode(operator.getUserCode());
            userContext.setUserName(operator.getUserName());
            userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        }
        // 将用户上下文信息设置到RequestInfoHolder中
        RequestInfoHolder.setLoginInfo(userContext);
    }

    /**
     * 设置操作者信息到用户上下文中
     * <p>
     * 根据提供的用户编码和用户名设置用户上下文信息。
     * 如果提供的userCode为空，将使用默认的管理员信息。
     * </p>
     *
     * @param userCode 用户编码，可以为空
     * @param userName 用户名称，可以为空
     * @see RequestInfoHolder#setLoginInfo(UserContext)
     */
    public static void putOperatorInfo(String userCode, String userName) {
        // 创建新的用户上下文对象
        UserContext userContext = new UserContext();
        if (StringUtils.isEmpty(userCode)) {
            // 如果用户编码为空，使用默认的管理员信息
            userContext.setUserCode(SYSTEM_USER_CODE);
            userContext.setUserName(SYSTEM_USER_NAME);
            userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        } else {
            // 使用提供的用户信息
            userContext.setUserCode(userCode);
            userContext.setUserName(userName);
            userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        }
        // 将用户上下文信息设置到RequestInfoHolder中
        RequestInfoHolder.setLoginInfo(userContext);
    }


    /**
     * 如果当前没有用户信息，则设置管理员信息到用户上下文中
     * <p>
     * 检查当前请求信息中是否已经存在用户上下文信息。
     * 如果不存在，则创建一个新的用户上下文，并使用默认的管理员信息进行设置。
     * </p>
     */
    public static void putAdminInfoIfNoUser() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (Objects.isNull(userContext)) {
            userContext = new UserContext();
            // 如果用户编码为空，使用默认的管理员信息
            userContext.setUserCode(SYSTEM_USER_CODE);
            userContext.setUserName(SYSTEM_USER_NAME);
            userContext.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
            // 将用户上下文信息设置到RequestInfoHolder中
            RequestInfoHolder.setLoginInfo(userContext);
        }

    }
}
