package com.imile.attendance.infrastructure.excel.header;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/25 
 * @Description Excel表头工具类
 */
public class ExcelHeaderUtil {

    /**
     * 将枚举表头转换为导出DTO列表
     * @param headerEnum 表头枚举类
     * @param isChinese 是否使用中文
     * @return 导出DTO列表
     */
    public static <T extends Enum<T> & ExcelHeaderBaseService> List<ExcelTitleExportDTO> convertToExportDTOs(Class<T> headerEnum, boolean isChinese) {
        return Arrays.stream(headerEnum.getEnumConstants())
                .map(header -> header.toExportDTO(isChinese))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定表头的导出DTO
     * @param header 表头枚举
     * @param isChinese 是否使用中文
     * @return 导出DTO
     */
    public static ExcelTitleExportDTO getExportDTO(ExcelHeaderBaseService header, boolean isChinese) {
        return header.toExportDTO(isChinese);
    }
}
