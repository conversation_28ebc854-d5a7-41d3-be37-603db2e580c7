package com.imile.attendance.infrastructure.repository.rule.dto;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @Date 2025/5/23
 * @Description 用户对应日打卡规则DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDayConfigDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * dayID
     */
    private Long dayId;

    /**
     * 打卡规则
     */
    private PunchConfigDO punchConfigDO;

}
