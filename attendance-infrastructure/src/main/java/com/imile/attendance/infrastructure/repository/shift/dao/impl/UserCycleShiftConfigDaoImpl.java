package com.imile.attendance.infrastructure.repository.shift.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.shift.dao.UserCycleShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.mapper.UserCycleShiftConfigMapper;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description
 */
@Component
@RequiredArgsConstructor
public class UserCycleShiftConfigDaoImpl extends ServiceImpl<UserCycleShiftConfigMapper, UserCycleShiftConfigDO> implements UserCycleShiftConfigDao {

    @Override
    public List<UserCycleShiftConfigDO> selectByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<UserCycleShiftConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserCycleShiftConfigDO::getUserId, userIdList);
        queryWrapper.eq(UserCycleShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserCycleShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.list(queryWrapper);
    }

    @Override
    public List<UserCycleShiftConfigDO> selectAllLatest() {
        LambdaQueryWrapper<UserCycleShiftConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCycleShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserCycleShiftConfigDO::getIsLatest, BusinessConstant.Y);
        return this.list(queryWrapper);
    }

    @Override
    public void batchUpdate(List<UserCycleShiftConfigDO> cycleShiftConfigDOList) {
        if (CollectionUtils.isEmpty(cycleShiftConfigDOList)) {
            return;
        }
        this.updateBatchById(cycleShiftConfigDOList);
    }

    @Override
    public void deleteByUserId(Long userId) {
        LambdaQueryWrapper<UserCycleShiftConfigDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(UserCycleShiftConfigDO::getUserId, userId);
        updateWrapper.eq(UserCycleShiftConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        UserCycleShiftConfigDO model = new UserCycleShiftConfigDO();
        BaseDOUtil.fillDOUpdate(model);
        model.setIsDelete(IsDeleteEnum.YES.getCode());
        this.baseMapper.update(model, updateWrapper);

    }
}
