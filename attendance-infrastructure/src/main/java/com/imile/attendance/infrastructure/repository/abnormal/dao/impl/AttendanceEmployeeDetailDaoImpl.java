package com.imile.attendance.infrastructure.repository.abnormal.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.mapper.AttendanceEmployeeDetailMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.UserAttendanceQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 员工出勤明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Service
public class AttendanceEmployeeDetailDaoImpl extends ServiceImpl<AttendanceEmployeeDetailMapper, AttendanceEmployeeDetailDO> implements AttendanceEmployeeDetailDao {

    @Override
    public List<AttendanceEmployeeDetailDO> listByUserId(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AttendanceEmployeeDetailDO::getUserId, userIds);
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public AttendanceEmployeeDetailDO getAttendanceEmployeeDetailDO(Long userId, Long dayId) {

        if (userId == null && dayId == null) {
            return null;
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getUserId, userId);
        queryWrapper.eq(AttendanceEmployeeDetailDO::getDayId, dayId);
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(queryWrapper);
    }


    @Override
    public List<AttendanceEmployeeDetailDO> userAttendanceList(UserAttendanceQuery query) {
        if (query == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (query.getStartTime() != null) {
            queryWrapper.ge(AttendanceEmployeeDetailDO::getDate, query.getStartTime());
        }
        if (query.getEndTime() != null) {
            queryWrapper.le(AttendanceEmployeeDetailDO::getDate, query.getEndTime());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(AttendanceEmployeeDetailDO::getUserId, query.getUserIds());
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailDO> selectAttendanceRecordByDay(Long userId, Long dayId) {
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        if (userId != null) {
            queryWrapper.eq(AttendanceEmployeeDetailDO::getUserId, userId);
        }
        if (dayId != null) {
            queryWrapper.ge(AttendanceEmployeeDetailDO::getDayId, dayId);
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailDO> selectByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(AttendanceEmployeeDetailDO::getFormId, formIdList);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailDO> selectByUserId(List<Long> userIds, Long dayId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.in(AttendanceEmployeeDetailDO::getUserId, userIds);
        queryWrapper.eq(AttendanceEmployeeDetailDO::getDayId, dayId);
        return this.list(queryWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailDO> selectByYear(Long userId, Long year) {
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(AttendanceEmployeeDetailDO::getUserId, userId);
        queryWrapper.eq(AttendanceEmployeeDetailDO::getYear, year);
        return list(queryWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailDO> selectAttendanceByDayId(Long userId, Long startDayId, Long endDayId) {
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryAttendanceWrapper = Wrappers.lambdaQuery();
        queryAttendanceWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryAttendanceWrapper.eq(AttendanceEmployeeDetailDO::getUserId, userId);
        queryAttendanceWrapper.ge(AttendanceEmployeeDetailDO::getDayId, startDayId);
        queryAttendanceWrapper.le(AttendanceEmployeeDetailDO::getDayId, endDayId);
        return list(queryAttendanceWrapper);
    }

    @Override
    public List<AttendanceEmployeeDetailDO> selectAttendanceByDayIdList(Long userId, List<Long> dayIdList) {
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryAttendanceWrapper = Wrappers.lambdaQuery();
        queryAttendanceWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryAttendanceWrapper.eq(AttendanceEmployeeDetailDO::getUserId, userId);
        queryAttendanceWrapper.in(AttendanceEmployeeDetailDO::getDayId, dayIdList);
        return list(queryAttendanceWrapper);
    }
    @Override
    public List<AttendanceEmployeeDetailDO> selectByDayId(Long userId, Long dayId) {
        LambdaQueryWrapper<AttendanceEmployeeDetailDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AttendanceEmployeeDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(AttendanceEmployeeDetailDO::getUserId, userId);
        queryWrapper.eq(AttendanceEmployeeDetailDO::getDayId, dayId);
        return list(queryWrapper);
    }
}
