package com.imile.attendance.infrastructure.repository.form.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;


import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/19
 * @Description
 */
public interface AttendanceApprovalFormDao extends IService<AttendanceApprovalFormDO> {

    List<AttendanceApprovalFormDO> selectByCondition(ApprovalFormQuery approvalFormQuery);

}

