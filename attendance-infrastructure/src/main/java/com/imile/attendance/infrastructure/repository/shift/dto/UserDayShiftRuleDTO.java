package com.imile.attendance.infrastructure.repository.shift.dto;

import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description 用户当天的排班规则，有班次时是班次名称,无班次时为OFF，H
 */
@Data
public class UserDayShiftRuleDTO {

    /**
     * 排班规则，OFF/H/班次名称
     */
    private String dayShiftRule;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;


    public static UserDayShiftRuleDTO build(String dayShiftRule, Long classId, String className) {
        UserDayShiftRuleDTO userDayShiftRuleDTO = new UserDayShiftRuleDTO();
        userDayShiftRuleDTO.setDayShiftRule(dayShiftRule);
        userDayShiftRuleDTO.setClassId(classId);
        userDayShiftRuleDTO.setClassName(className);
        return userDayShiftRuleDTO;
    }
    
    /**
     * 构建OFF规则
     */
    public static UserDayShiftRuleDTO buildOff() {
        return build(DayShiftRuleEnum.OFF.getCode(), null, null);
    }

    /**
     * 构建节假日规则
     */
    public static UserDayShiftRuleDTO buildHoliday() {
        return build(DayShiftRuleEnum.H.getCode(), null, null);
    }

    /**
     * 构建班次规则
     */
    public static UserDayShiftRuleDTO buildClass(Long classId, String className) {
        return build(className, classId, className);
    }
}
