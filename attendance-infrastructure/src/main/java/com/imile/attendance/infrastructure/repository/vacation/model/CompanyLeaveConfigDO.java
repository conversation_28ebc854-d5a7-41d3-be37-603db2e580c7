package com.imile.attendance.infrastructure.repository.vacation.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公司假期配置表
 *
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@ApiModel(description = "公司假期配置表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company_leave_config")
public class CompanyLeaveConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "假期名称")
    private String leaveName;

    @ApiModelProperty(value = "假期类型")
    private String leaveType;

    @ApiModelProperty(value = "是否派遣假")
    private Integer isDispatch;

    @ApiModelProperty(value = "派遣地国家")
    private String dispatchCountry;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "性别")
    private Integer useSex;

    @ApiModelProperty(value = "员工类型")
    private String employeeType;

    @ApiModelProperty(value = "常驻地省份")
    private String locationProvince;

    @ApiModelProperty(value = "使用限制")
    private Integer leaveUsageRestrictions;

    @ApiModelProperty(value = "更新周期")
    private String useCycle;

    @ApiModelProperty(value = "假期可用的开始时间")
    private String useStartDate;

    @ApiModelProperty(value = "假期可用的结束时间")
    private String useEndDate;

    @ApiModelProperty(value = "假期简称")
    private String leaveShortName;

    @ApiModelProperty(value = "节假日是否消耗假期")
    private String consumeLeaveType;

    @ApiModelProperty(value = "是否上传附件")
    private Integer isUploadAttachment;

    @ApiModelProperty(value = "上传附件条件")
    private Long uploadAttachmentCondition;

    @ApiModelProperty(value = "上传附件单位")
    private String attachmentUnit;

    @ApiModelProperty(value = "请假单位")
    private String leaveUnit;

    @ApiModelProperty(value = "最小请假时长")
    private Integer miniLeaveDuration;

    @ApiModelProperty(value = "是否带薪")
    private Integer isSalary;

    @ApiModelProperty(value = "适用部门")
    private String deptIds;

    @ApiModelProperty(value = "入职日期范围(开始时间)")
    private String startEntryDate;

    @ApiModelProperty(value = "入职日期范围(结束时间)")
    private String endEntryDate;
}
