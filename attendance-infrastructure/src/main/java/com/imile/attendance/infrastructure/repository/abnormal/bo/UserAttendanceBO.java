package com.imile.attendance.infrastructure.repository.abnormal.bo;

import lombok.Data;

import java.util.Date;

@Data
public class UserAttendanceBO {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 最后修改时间
     */
    private Date lastUpdDate;

    /**
     * 最后修改人名称
     */
    private String lastUpdUserName;

    /**
     * 日历名称
     */
    private String attendanceConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

}
