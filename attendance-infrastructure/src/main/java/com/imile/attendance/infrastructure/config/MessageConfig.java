package com.imile.attendance.infrastructure.config;

import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
@Configuration
public class MessageConfig {

    @Bean
    public ReloadableResourceBundleMessageSource reloadableResourceBundleMessageSource() {

        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setDefaultEncoding("utf-8");
        messageSource.setBasenames("classpath:i18n/messages");

        // 是否使用message的format
        messageSource.setAlwaysUseMessageFormat(true);
        // 如果找不到国际化值，则默认使用code值，不抛出异常NoSuchMessageException
        messageSource.setUseCodeAsDefaultMessage(true);
        messageSource.setCacheSeconds(60);
        // 为了兼容framework, 其他的地方请勿使用
        I18nUtils.setMessageSource(messageSource);
        return messageSource;
    }
}
