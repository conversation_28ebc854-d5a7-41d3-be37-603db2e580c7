package com.imile.attendance.infrastructure.repository.rule.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReissueCardConfigRangeByDateQuery implements Serializable {
    private static final long serialVersionUID = -6099272622205258396L;

    private List<Long> userIds;

    private List<Long> ruleConfigIds;

    private Date startDate;

    private Date endDate;

    private Long startTimestamp;

    private Long endTimestamp;
}
