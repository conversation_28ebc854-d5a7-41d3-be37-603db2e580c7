package com.imile.attendance.infrastructure.repository.deviceConfig.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工考勤常用手机表
 *
 * <AUTHOR>
 * @Date 2025/4/17
 * @Description
 */
@ApiModel(description = "员工考勤常用手机表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_mobile_config")
public class AttendanceMobileConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户编码")
    private String userCode;

    /**
     * 手机唯一标识
     */
    @ApiModelProperty(value = "手机唯一标识")
    private String mobileUnicode;

    /**
     * 手机型号
     */
    @ApiModelProperty(value = "手机型号")
    private String mobileModel;

    /**
     * 手机品牌
     */
    @ApiModelProperty(value = "手机品牌")
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    @ApiModelProperty(value = "手机系统版本")
    private String mobileVersion;
}
