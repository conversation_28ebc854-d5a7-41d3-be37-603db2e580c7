package com.imile.attendance.infrastructure.repository.employee.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 用户离职记录表 系统-员工
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_dimission_record")
public class UserDimissionRecordDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户id 用户ID
     */
    private Long userId;

    /**
     * 计划离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planDimissionDate;

    /**
     * 实际离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date actualDimissionDate;

    /**
     * 离职状态 离职状态：待离职、已离职、取消离职、在职
     */
    private String dimissionStatus;
}
