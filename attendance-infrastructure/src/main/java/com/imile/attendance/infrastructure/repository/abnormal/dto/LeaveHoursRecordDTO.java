package com.imile.attendance.infrastructure.repository.abnormal.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 员工在该天的请假类型和时长
 * @author: taokang
 * @createDate: 2022-5-26
 * @version: 1.0
 */
@Data
public class LeaveHoursRecordDTO {

    /**
     * 请假类型(简码)
     */
    private String leaveConcreteType;

    /**
     * 请假类型(全称)
     */
    private String leaveType;

    /**
     * 请假小时数
     */
    private BigDecimal leaveHours;

    /**
     * 请假分钟
     */
    private BigDecimal leaveMinutes;

    /**
     * 请假的照片路径
     */
    private List<String> picturePathList;
}
