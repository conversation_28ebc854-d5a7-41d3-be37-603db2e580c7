package com.imile.attendance.infrastructure.repository.abnormal.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.UserAttendanceQuery;

import java.util.List;

/**
 * <p>
 * 员工出勤明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
public interface AttendanceEmployeeDetailDao extends IService<AttendanceEmployeeDetailDO> {
    /**
     * 根据用户id获取员工
     *
     * @param userIds
     * @return
     */
    List<AttendanceEmployeeDetailDO> listByUserId(List<Long> userIds);

    /**
     * 获取该员工这一天的出勤明细
     *
     * @param userId
     * @param dayId
     * @return
     */
    AttendanceEmployeeDetailDO getAttendanceEmployeeDetailDO(Long userId, Long dayId);

    List<AttendanceEmployeeDetailDO> userAttendanceList(UserAttendanceQuery query);

    /**
     * 查询员工当天之后的所有考勤记录
     */
    List<AttendanceEmployeeDetailDO> selectAttendanceRecordByDay(Long userId, Long dayId);

    List<AttendanceEmployeeDetailDO> selectByFormIdList(List<Long> formIdList);

    /**
     * 根据user_id查询某天所有考勤记录
     */
    List<AttendanceEmployeeDetailDO> selectByUserId(List<Long> userIds, Long dayId);

    /**
     * 查询员工该年的所有出勤明细
     */
    List<AttendanceEmployeeDetailDO> selectByYear(Long userId, Long year);


    /**
     * 查询用户指定时间段内的考勤记录
     */
    List<AttendanceEmployeeDetailDO> selectAttendanceByDayId(Long userId, Long startDayId, Long endDayId);

    /**
     * 查询用户指定时间的考勤记录
     */
    List<AttendanceEmployeeDetailDO> selectAttendanceByDayIdList(Long userId, List<Long> dayIdList);

    /**
     * 查询员工某天的考勤记录
     */
    List<AttendanceEmployeeDetailDO> selectByDayId(Long userId, Long dayId);


}
