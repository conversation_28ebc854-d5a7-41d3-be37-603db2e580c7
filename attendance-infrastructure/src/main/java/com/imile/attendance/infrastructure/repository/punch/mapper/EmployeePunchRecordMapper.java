package com.imile.attendance.infrastructure.repository.punch.mapper;

import com.imile.attendance.infrastructure.common.AttendanceBaseMapper;
import com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.UserPunchCardRecordQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Mapper
@Repository
public interface EmployeePunchRecordMapper extends AttendanceBaseMapper<EmployeePunchRecordDO> {

    List<PunchCardRecordDTO> listRecord(UserPunchCardRecordQuery query);

    List<Long> selectUserIdByPunchCardTypes(@Param("startDayId") Long startDayId, @Param("endDayId") Long endDayId, @Param("punchCardTypeList") List<String> punchCardTypeList);

    List<String> selectPunchCardTypeByUserCode(@Param("userCode") String userCode, @Param("startDayId") Long startDayId, @Param("endDayId") Long endDayId);
}
