package com.imile.attendance.infrastructure.repository.common;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.util.CommonUtil;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.imile.attendance.hermes.RpcHermesCountryClient;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.hermes.support.RpcHermesCountrySupport;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 国家配置信息
 */
@Component
public class CountryService {

    @Resource
    private RpcHermesCountryClient rpcHermesCountryClient;
    @Resource
    private RpcHermesCountrySupport rpcHermesCountrySupport;


    /**
     * 拉取所有业务国家配置
     */
    public List<CountryDTO> listAllCountry(){
        return rpcHermesCountrySupport.listAllCountry();
    }

    /**
     * 拉取所有业务国家配置
     */
    public List<CountryConfigDTO> queryAllCountryConfigList(){
        return rpcHermesCountryClient.queryAllCountryConfigList();
    }


    /**
     * 根据国家编码拉取国家配置
     */
    public CountryDTO queryCountry(String countryCode){
        return rpcHermesCountrySupport.queryCountry(countryCode);
    }

    /**
     * 获取国家配置
     */
    public CountryConfigDTO queryCountryConfig(CountryApiQuery query){
        return rpcHermesCountryClient.queryCountryConfig(query);
    }

    /**
     * 获取国家配置
     */
    public List<CountryConfigDTO> selectCountryConfigList(CountryApiQuery query){
        return rpcHermesCountryClient.queryCountryConfigList(query);
    }


    /**
     * 获取对应国家的当前时间
     */
    public Map<String, Date> getCountryCurrentDate(List<String> countryList){
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = this.selectCountryConfigList(countryQuery);
        if (CollectionUtils.isEmpty(countryConfigList)) {
            return Collections.emptyMap();
        }
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));
        Map<String, Date> dateCountryMap = new HashMap<>();
        // 获取国家对应时区
        for (String countryName : countryConfigMap.keySet()) {
            String timeZone = countryConfigMap.getOrDefault(countryName, "");
            if (ObjectUtil.equal(timeZone, "")) {
                continue;
            }
            Date now = new Date();
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            dateCountryMap.put(countryName, dateTime);
        }
        return dateCountryMap;
    }

}
