package com.imile.attendance.infrastructure.repository.zkteco.DO;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ZktecoEmployeeUpdateDO implements Serializable {
    private static final long serialVersionUID = 774281468638723792L;

    private Integer id;

    private String userCode;

    private String firstName;

    private String lastName;

    private Integer deptId;

    private Integer status;

    private List<Integer> areaIds;

}
