package com.imile.attendance.infrastructure.repository.abnormal.dto;

import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import lombok.Data;

/**
 * 员工出勤DTO
 *
 * <AUTHOR>
 */
@Data
public class UserAttendanceDTO extends UserInformationDTO {

    private UserYearAttendanceDTO userYearAttendance;

    /**
     * 用户考勤模板（默认还是个人） DEFAULT,CUSTOM
     */
    private String attendanceConfigType;
}
