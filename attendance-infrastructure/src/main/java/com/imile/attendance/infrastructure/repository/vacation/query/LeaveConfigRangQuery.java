package com.imile.attendance.infrastructure.repository.vacation.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeaveConfigRangQuery {
    /**
     * 假期规则主键
     */
    private Long leaveId;

    /**
     * 范围类型
     */
    private Integer rangeType;
}
