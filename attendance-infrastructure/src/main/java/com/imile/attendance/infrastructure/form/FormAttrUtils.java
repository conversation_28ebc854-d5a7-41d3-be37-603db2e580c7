package com.imile.attendance.infrastructure.form;

import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 表单属性工具类
 */
public class FormAttrUtils {

    /**
     * 从表单属性列表中获取指定key的单个属性
     *
     * @param formAttrDOList 表单属性列表
     * @param attrKey 属性key枚举
     * @return 匹配的表单属性，如果没有找到则返回null
     */
    public static AttendanceFormAttrDO getFormAttr(List<AttendanceFormAttrDO> formAttrDOList,
                                                   ApplicationFormAttrKeyEnum attrKey) {
        if (CollectionUtils.isEmpty(formAttrDOList) || Objects.isNull(attrKey)) {
            return null;
        }
        return formAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), attrKey.getLowerCode()))
                .findAny()
                .orElse(null);
    }

    /**
     * 从表单属性列表中获取指定key的单个属性
     *
     * @param formAttrDOList 表单属性列表
     * @param attrKey 属性key字符串
     * @return 匹配的表单属性，如果没有找到则返回null
     */
    public static AttendanceFormAttrDO getFormAttr(List<AttendanceFormAttrDO> formAttrDOList, String attrKey) {
        if (CollectionUtils.isEmpty(formAttrDOList) || StringUtils.isEmpty(attrKey)) {
            return null;
        }
        return formAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), attrKey))
                .findAny()
                .orElse(null);
    }

    /**
     * 从表单属性列表中获取指定key的所有属性
     *
     * @param formAttrDOList 表单属性列表
     * @param attrKey 属性key枚举
     * @return 匹配的表单属性列表，如果没有找到则返回空列表
     */
    public static List<AttendanceFormAttrDO> getFormAttrs(List<AttendanceFormAttrDO> formAttrDOList, ApplicationFormAttrKeyEnum attrKey) {
        if (CollectionUtils.isEmpty(formAttrDOList) || Objects.isNull(attrKey)) {
            return Collections.emptyList();
        }
        return formAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), attrKey.getLowerCode()))
                .collect(Collectors.toList());
    }

    /**
     * 从表单属性列表中获取指定key的所有属性
     *
     * @param formAttrDOList 表单属性列表
     * @param attrKey 属性key字符串
     * @return 匹配的表单属性列表，如果没有找到则返回空列表
     */
    public static List<AttendanceFormAttrDO> getFormAttrs(List<AttendanceFormAttrDO> formAttrDOList, String attrKey) {
        if (CollectionUtils.isEmpty(formAttrDOList) || StringUtils.isEmpty(attrKey)) {
            return Collections.emptyList();
        }
        return formAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), attrKey))
                .collect(Collectors.toList());
    }
}
