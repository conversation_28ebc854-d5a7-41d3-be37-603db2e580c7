package com.imile.attendance.infrastructure.repository.vacation.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveTypeDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveTypeQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/21
 * @Description
 */
public interface CompanyLeaveTypeDao extends IService<CompanyLeaveTypeDO> {

    List<CompanyLeaveTypeDO> queryByCondition(CompanyLeaveTypeQuery query);
}
