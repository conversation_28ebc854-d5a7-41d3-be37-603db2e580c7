package com.imile.attendance.infrastructure.excel.header;

import lombok.Data;

/**
 * Excel导出表头DTO
 * 用于定义Excel导出时的表头信息，支持国际化
 * 
 * <AUTHOR> chen
 * @since 2025/4/25
 */
@Data
public class ExcelTitleExportDTO {

    /**
     * 表头标题
     * 通常用作Excel的列标识符，例如：
     * - 对于固定表头：Employee ID, Employee Name 等
     * - 对于动态日期表头：2024-01-01, 2024-01-02 等
     */
    private String title;

    /**
     * 表头显示名称
     * 根据语言环境显示对应的文本，例如：
     * - 英文环境：Employee ID
     * - 中文环境：员工编号
     * 对于日期等通用字段，name 可能与 title 相同
     */
    private String name;

    /**
     * 创建ExcelTitleExportDTO实例
     * 
     * @param title 表头标题，作为Excel的列标识符
     * @param name 表头显示名称，支持国际化显示
     * @return ExcelTitleExportDTO实例
     */
    public static ExcelTitleExportDTO of(String title, String name) {
        ExcelTitleExportDTO excelTitleExportDTO = new ExcelTitleExportDTO();
        excelTitleExportDTO.setTitle(title);
        excelTitleExportDTO.setName(name);
        return excelTitleExportDTO;
    }
}