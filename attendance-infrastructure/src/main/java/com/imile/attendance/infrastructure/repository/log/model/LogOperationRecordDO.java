package com.imile.attendance.infrastructure.repository.log.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
@ApiModel(description = "操作日志记录表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("log_operation_record")
public class LogOperationRecordDO extends BaseDO {

    @ApiModelProperty(value = "")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码
     */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 被操作的对象用户名称
     */
    @ApiModelProperty(value = "被操作的对象用户名称")
    private String userName;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 外键关联key，也可以是业务唯一标识
     */
    @ApiModelProperty(value = "外键关联key，也可以是业务唯一标识")
    private String foreignKey;

    /**
     * 外键表
     */
    @ApiModelProperty(value = "外键表")
    private String foreignTable;

    /**
     * 操作模块
     */
    @ApiModelProperty(value = "操作模块")
    private String operationModule;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作编码
     */
    @ApiModelProperty(value = "操作编码")
    private String operationCode;

    /**
     * 操作对象编码
     */
    @ApiModelProperty(value = "操作对象编码")
    private String operationUserCode;

    /**
     * 操作对象名称
     */
    @ApiModelProperty(value = "操作对象名称")
    private String operationUserName;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String remark;

    /**
     * 字段对比差异
     */
    @ApiModelProperty(value = "字段对比差异")
    private String fieldDiff;
}

