package com.imile.attendance.infrastructure.repository.vacation.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveTypeDao;
import com.imile.attendance.infrastructure.repository.vacation.mapper.CompanyLeaveTypeMapper;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveTypeDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveTypeQuery;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/23
 * @Description 假期类型 数据库操作实现类
 */
@Component
@RequiredArgsConstructor
public class CompanyLeaveTypeDaoImpl extends ServiceImpl<CompanyLeaveTypeMapper, CompanyLeaveTypeDO> implements CompanyLeaveTypeDao {

    @Override
    public List<CompanyLeaveTypeDO> queryByCondition(CompanyLeaveTypeQuery query) {
        if (Objects.isNull(query)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<CompanyLeaveTypeDO> lambdaQuery = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(query.getCountry())) {
            lambdaQuery.eq(CompanyLeaveTypeDO::getCountry, query.getCountry());
        }
        if (StringUtils.isNotBlank(query.getLeaveType())) {
            lambdaQuery.eq(CompanyLeaveTypeDO::getLeaveType, query.getLeaveType());
        }
        lambdaQuery.eq(CompanyLeaveTypeDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(lambdaQuery);
    }
}
