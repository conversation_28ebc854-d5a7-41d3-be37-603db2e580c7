package com.imile.attendance.infrastructure.repository.rule.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.common.annotation.ApiModel;
import com.imile.common.enums.StatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7 
 * @Description
 */
@ApiModel(description = "考勤加班规则表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("over_time_config")
@FieldNameConstants
public class OverTimeConfigDO extends BaseDO {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "加班规则编码")
    private String configNo;

    @ApiModelProperty(value = "加班规则名称")
    private String configName;

    @ApiModelProperty(value = "加班配置 JSON格式")
    private String overtimeConfig;

    @ApiModelProperty(value = "是否国家级别")
    private Integer isCountryLevel;

    @ApiModelProperty(value = "状态 ACTIVE、DISABLED")
    private String status;

    @ApiModelProperty(value = "适用部门")
    private String deptIds;

    @ApiModelProperty(value = "是否最新")
    private Integer isLatest;

    @ApiModelProperty(value = "生效时间")
    private Date effectTime;

    @ApiModelProperty(value = "失效时间")
    private Date expireTime;

    @ApiModelProperty(value = "生效时间戳")
    private Long effectTimestamp;

    @ApiModelProperty(value = "失效时间戳")
    private Long expireTimestamp;

    /**
     * 工作加班开始时间
     */
    @ApiModelProperty(value = "工作加班开始时间")
    private BigDecimal workingOutStartTime;

    /**
     * 工作日最长有效加班时间
     */
    @ApiModelProperty(value = "工作日最长有效加班时间")
    private BigDecimal workingEffectiveTime;

    /**
     * 工作日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    @ApiModelProperty(value = "工作日加班补贴方式")
    private String workingSubsidyType;

    //==========休息日加班时长的判定====================

    /**
     * 休息日最长有效加班时间
     *
     */
    @ApiModelProperty(value = "休息日最长有效加班时间")
    private BigDecimal restEffectiveTime;

    /**
     * 休息日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    @ApiModelProperty(value = "休息日加班补贴方式")
    private String restSubsidyType;

    //==========节假日加班时长的判定====================

    /**
     * 节假日最长有效加班时间
     *
     */
    @ApiModelProperty(value = "节假日最长有效加班时间")
    private BigDecimal holidayEffectiveTime;

    /**
     * 节假日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    @ApiModelProperty(value = "节假日加班补贴方式")
    private String holidaySubsidyType;


    public Boolean areCountryLevel() {
        return Objects.equals(this.isCountryLevel, BusinessConstant.Y);
    }

    public List<Long> listDeptIds() {
        if (StringUtils.isNotBlank(this.deptIds)) {
            return Arrays.asList((Long[]) ConvertUtils.convert(this.deptIds.split(","), Long.class));
        }
        return new ArrayList<>();
    }

    public Boolean areLatest(){
        return Objects.equals(this.isLatest, BusinessConstant.Y);
    }

    public Boolean areActive() {
        return StringUtils.equals(this.status, StatusEnum.ACTIVE.getCode());
    }

    public Boolean areDisable() {
        return StringUtils.equals(this.status, StatusEnum.DISABLED.getCode());
    }
}

