package com.imile.attendance.infrastructure.repository.cycleConfig.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigPageQuery;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
public interface AttendanceCycleConfigDao extends IService<AttendanceCycleConfigDO> {

    List<AttendanceCycleConfigDO> selectByCondition(AttendanceCycleConfigQuery query);

    void saveAndUpdateAttendanceCycleConfig(AttendanceCycleConfigDO oldAttendanceCycleConfig,
                                            AttendanceCycleConfigDO model);

    List<AttendanceCycleConfigDO> selectListByCondition(AttendanceCycleConfigPageQuery query);


    List<AttendanceCycleConfigDO> listByPage(int currentPage, int pageSize);
}
