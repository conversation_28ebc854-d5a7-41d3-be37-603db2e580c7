package com.imile.attendance.easylog;

import com.alibaba.fastjson.JSON;
import com.github.easylog.model.EasyLogInfo;
import com.github.easylog.service.ILogRecordService;
import com.github.easylog.util.PlaceholderResolver;
import com.imile.attendance.mq.MqSend;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 接收操作日志，可根据情况存储到数据库或发送到MQ
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OpLogRecordService implements ILogRecordService {

    @Value("${topic.pms.log:TOPIC_PMS_LOG_DEV}")
    private String topicPmsLog;

    @Resource
    private MqSend mqSend;

    @Override
    public void record(EasyLogInfo easyLogInfo) {
//        记录条件为 false，则不记录
        if (!StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), easyLogInfo.getCondition())) {
            mqSend.send(topicPmsLog, null, "", JSON.toJSONString(easyLogInfo));
        }
        String resolve = PlaceholderResolver.getDefaultResolver().resolve(easyLogInfo.getContent(), easyLogInfo.getContentParam());
        easyLogInfo.setContent(resolve);
        log.info("操作日志={}", easyLogInfo);
    }
}
