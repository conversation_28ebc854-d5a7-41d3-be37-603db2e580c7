package com.imile.attendance.idwork;

import com.imile.attendance.enums.NoPrefixEnum;

import java.io.Serializable;

/**
 * <AUTHOR> chen
 * @Date 2025/1/17 
 * @Description
 */
public interface IdWorker {

    /**
     * 获取下一个ID，默认雪花ID算法
     */
    Long nextId();

    /**
     * 下一个编码
     */
    String nextNo(NoPrefixEnum noPrefixEnum);

    /**
     * 获取下一个考勤方案编码
     */
    String nextAttendanceConfigNo();


    /**
     * 获取下一个打卡方案编码
     */
    String nextAttendancePunchConfigNo();

    /**
     * 生成UUID，不带"-"
     */
    String uuid();


    /**
     * 生成版本号，生成规则以bizId作为唯一标识，以每天为单位，生成对应的版本号：yyMMdd+001，依次递增，例如：2022011001、2022011002、2022011003
     * @param bizId 业务唯一标识
     */
    Long recordVersion(Serializable bizId);

}
