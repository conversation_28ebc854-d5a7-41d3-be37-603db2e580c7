package com.imile.attendance.repository.employee.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_info")
public class UserInfoDO extends BaseDO {


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    private String userCode;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 性别 性别(1:男,2:女)
     */
    private Integer sex;

    /**
     * 出生日期,yyyy-MM-dd
     */
    private Date birthday;

    /**
     * 所属国家编码
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 是否全球派遣（0:否 1:是）
     */
    private Integer isGlobalRelocation;

    /**
     * 员工性质 INTERNAL-内部员工,EXTERNAL-外部员工
     */
    private String employeeType;

    /**
     * 是否为司机 是否为司机
     */
    private Integer isDriver;

    /**
     * 是否司机leader
     */
    private Integer isDtl;

    /**
     * 是否仓内作业员工
     */
    private Integer isWarehouseStaff;

    /**
     * 工作岗位
     */
    private Long postId;

    /**
     * 所属部门id
     */
    private Long deptId;

    /**
     * 所属网点编码
     */
    private String ocCode;

    /**
     * 汇报上级ID
     */
    private Long leaderId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 结算主体编码（迁移至hrms_labor_contract_info.contract_company_code）
     */
    @Deprecated
    private String settlementCenterCode;


    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    private String status;

    /**
     * 工作状态 在职、离职等
     */
    private String workStatus;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 实际离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualDimissionDate;

}
