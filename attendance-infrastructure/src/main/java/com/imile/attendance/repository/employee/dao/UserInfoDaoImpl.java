package com.imile.attendance.repository.employee.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.repository.employee.mapper.UserInfoMapper;
import com.imile.attendance.repository.employee.modle.UserInfoDO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Component
@DS(Constants.TableSchema.attendance)
@RequiredArgsConstructor
public class UserInfoDaoImpl extends ServiceImpl<UserInfoMapper, UserInfoDO> implements UserInfoDao {


    @Override
    public UserInfoDO getByUserCode(String userCode) {
        LambdaQueryWrapper<UserInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfoDO::getUserCode, userCode)
                .eq(UserInfoDO::getIsDelete, Constants.N);
        return this.getOne(queryWrapper);
    }
}
