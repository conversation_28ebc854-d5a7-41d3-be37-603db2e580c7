package com.imile.attendance.mq;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
@Slf4j
@Component
public class MqSend {

    @Resource
    private DefaultMQProducer defaultMqProducer;



    public void send(String topic, String tags, String key, String body) {
        try {
            log.info("MessageServiceImpl | sendMessage | topic={},tags={},key={},body={}", topic, tags, key, body);
            Message message = new Message(topic, tags, key, body.getBytes(StandardCharsets.UTF_8));
            SendResult sendResult = defaultMqProducer.send(message, (mqList, message1, arg) -> {
                int hashCode = Math.abs(key.hashCode());
                int index = hashCode % mqList.size();
                return mqList.get(index);
            }, key);
            log.info("MessageServiceImpl | sendMessage | sendResult={}", sendResult);
        } catch (Exception e) {
            log.error("MessageServiceImpl | sendMessage | is Exception", e);
        }
    }
}
