<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.employee.mapper.UserInfoMapper">
    <select id="getUserByCode" resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserDTO">
        SELECT ui.id,
               ui.user_code              as userCode,
               ui.user_name              as userName,
               ui.sex,
               ui.birthday,
               ui.country_code           as countryCode,
               ui.phone,
               ui.location_country       as locationCountry,
               ui.location_province      as locationProvince,
               ui.location_city          as locationCity,
               ui.is_global_relocation   as isGlobalRelocation,
               ui.employee_type          as employeeType,
               ui.is_driver              as isDriver,
               ui.is_dtl                 as isDtl,
               ui.is_warehouse_staff     as isWarehouseStaff,
               ui.post_id                as postId,
               ui.dept_id                as deptId,
               ui.oc_id                  as ocId,
               ui.oc_code                as ocCode,
               ui.vendor_Id              as vendorId,
               ui.vendor_code            as vendorCode,
               ui.settlement_center_code as settlementCenterCode,
               ui.email,
               ui.`status`,
               ui.work_status            as workStatus,
               ui.class_nature           as classNature,
               ui.disabled_date          as disabledDate,
               uer.entry_date            as entryDate,
               udr.actual_dimission_date as actualDimissionDate,
               ui.is_delete              as isDelete,
               ui.record_version         as recordVersion,
               ui.create_date            as createDate,
               ui.last_upd_date          as lastUpdDate,
               ui.create_user_code       as createUserCode,
               ui.create_user_name       as createUserName,
               ui.last_upd_user_code     as lastUpdUserCode,
               ui.last_upd_user_name     as lastUpdUserName
        from user_info ui
                 LEFT JOIN user_entry_record uer on uer.user_id = ui.id and uer.is_delete = 0 and uer.entry_status = 'ENTRY'
                 LEFT JOIN user_dimission_record udr on udr.user_id = ui.id  and udr.is_delete = 0 and udr.dimission_status = 'DIMISSION'
        WHERE ui.is_delete = 0
          and ui.user_code = #{userCode}
    </select>


    <select id="getUserInfoInformation"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO">
        select hui.id                     as userId,
               hui.user_code              as userCode,
               hui.user_name              as userName,
               hui.user_name_en           as userNameEn,
               hui.work_status            as workStatus,
               hui.status                 as status,
               hui.location_country       as locationCountry,
               hui.vendor_code            as vendorCode,
               hui.vendor_name            as vendorName,
               hui.leader_id              as leaderId,
               hui.leader_name            as leaderName,
               hui.employee_type          as employeeType,
               huer.entry_date            as entryDate,
               hudr.actual_dimission_date as dimissionDate,
               hui.dept_id                as deptId,
               hui.post_id                as postId
--                hed.id                     as deptId,
--                hed.dept_name_cn           as deptName,
--                hed.dept_name_en           as deptNameEn,
--                hep.id                     as postId,
--                hep.post_name_cn           as postName,
--                hep.post_name_en           as postNameEn
        from user_info hui
                 --                  left join hrms_ent_dept hed
--                            on hui.dept_id = hed.id and hed.is_delete = 0 and hed.status = 'ACTIVE'
--                  left join hrms_ent_post hep
--                            on hui.post_id = hep.id and hep.is_delete = 0 and hep.status = 'ACTIVE'
                 left join user_entry_record huer
                           on hui.id = huer.user_id and huer.is_delete = 0 and huer.entry_status = 'ENTRY'
                 left join user_dimission_record hudr
                           on hui.id = hudr.user_id and hudr.is_delete = 0 and hudr.dimission_status = 'DIMISSION'
        where hui.id = #{userId}
    </select>

<!--    <select id="selectByAssociateCondition"-->
<!--            parameterType="com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery"-->
<!--            resultType="com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO">-->
<!--        SELECT-->
<!--        id,-->
<!--        user_code AS userCode,-->
<!--        user_name AS userName,-->
<!--        user_name_en AS userNameEn-->
<!--        FROM user_info-->
<!--        WHERE is_delete = 0-->
<!--        AND user_code IS NOT NULL-->
<!--        <if test="userIds != null and userIds.size() > 0">-->
<!--            AND id IN-->
<!--            <foreach collection="userIds" close=")" open="(" separator="," item="item">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="userCodes != null and userCodes.size() > 0">-->
<!--            AND user_code IN-->
<!--            <foreach collection="userCodes" close=")" open="(" separator="," item="item">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        <if test="isSystem != null and !isSystem">-->
<!--            <if test="deptIds!=null and deptIds.size()>0">-->
<!--                and (dept_id in-->
<!--                <foreach collection="deptIds" item="deptId" index="i" open="(" close=")" separator=",">-->
<!--                    #{deptId}-->
<!--                </foreach>-->
<!--                <if test="locationCountryList!=null and locationCountryList.size()>0">-->
<!--                    or location_country in-->
<!--                    <foreach collection="locationCountryList" item="locationCountry" index="i" open="(" close=")"-->
<!--                             separator=",">-->
<!--                        #{locationCountry}-->
<!--                    </foreach>-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->

<!--            <if test="deptIds==null or deptIds.size()==0">-->
<!--                <if test="locationCountryList!=null and locationCountryList.size()>0">-->
<!--                    and location_country in-->
<!--                    <foreach collection="locationCountryList" item="locationCountry" index="i" open="(" close=")"-->
<!--                             separator=",">-->
<!--                        #{locationCountry}-->
<!--                    </foreach>-->
<!--                </if>-->
<!--            </if>-->
<!--        </if>-->

<!--        <if test="employeeTypes != null and employeeTypes.size() > 0">-->
<!--            AND employee_type IN-->
<!--            <foreach collection="employeeTypes" close=")" open="(" separator="," item="item">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="isDriver != null">-->
<!--            AND is_driver = #{isDriver}-->
<!--        </if>-->
<!--        &lt;!&ndash;未传工作状态时默认过滤工作状态为空的数据&ndash;&gt;-->
<!--        <if test="workStatus == null">-->
<!--            AND work_status IS NOT NULL AND work_status != ''-->
<!--        </if>-->
<!--        <if test="workStatus != null and workStatus != ''">-->
<!--            AND work_status = #{workStatus}-->
<!--        </if>-->
<!--        <if test="status != null and status != ''">-->
<!--            AND status = #{status}-->
<!--        </if>-->
<!--        <if test="classNature != null and classNature != ''">-->
<!--            AND class_nature = #{classNature}-->
<!--        </if>-->
<!--        &lt;!&ndash;传keyword时先按user_code正序再按id倒序&ndash;&gt;-->
<!--        <if test="keyword != null and keyword != ''">-->
<!--            AND (-->
<!--            id = #{keyword}-->
<!--            OR user_code LIKE CONCAT('%', #{keyword}, '%')-->
<!--            OR user_name LIKE CONCAT('%', #{keyword}, '%')-->
<!--            OR user_name_en LIKE CONCAT('%', #{keyword}, '%')-->
<!--            )-->
<!--            ORDER BY user_code, id DESC-->
<!--        </if>-->
<!--        &lt;!&ndash;未传keyword时默认按id倒序&ndash;&gt;-->
<!--        <if test="keyword == null or keyword == ''">-->
<!--            ORDER BY id DESC-->
<!--        </if>-->
<!--        LIMIT 50-->
<!--    </select>-->


    <select id="selectByAssociateCondition"
            parameterType="com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO">
        SELECT
        id,
        user_code ,
        user_name ,
        user_name_en
        FROM user_info
        <include refid="userAssociateCommonConditions"/>

        <if test="normalCountryList != null and normalCountryList.size()>0">
            <foreach collection="normalCountryList" separator="," open="and location_country in (" close=")" item="normalCountry">
                #{normalCountry}
            </foreach>
        </if>

        <if test="normalEmployeeTypeList != null and normalEmployeeTypeList.size() > 0">
            <foreach collection="normalEmployeeTypeList" separator="," open="and employee_type in (" close=")"
                     item="normalEmployeeType">
                #{normalEmployeeType}
            </foreach>
        </if>

        <if test="isNeedQuerySpecialCountry != null and isNeedQuerySpecialCountry">
            UNION ALL
            (SELECT
            id,
            user_code,
            user_name,
            user_name_en
            FROM user_info
            <include refid="userAssociateCommonConditions"/>


            <if test="specialCountryList != null and specialCountryList.size()>0">
                <foreach collection="specialCountryList" separator="," open="and location_country in (" close=")" item="specialCountry">
                    #{specialCountry}
                </foreach>
            </if>

            <if test="specialEmployeeTypeList != null and specialEmployeeTypeList.size() > 0">
                <foreach collection="specialEmployeeTypeList" separator="," open="and employee_type in (" close=")"
                         item="specialEmployeeType">
                    #{specialEmployeeType}
                </foreach>
            </if>
            )
        </if>

        <!-- 全局排序 -->
        ORDER BY
        <choose>
            <when test="keyword != null and keyword != ''">
                user_code ASC, id DESC
            </when>
            <otherwise>
                id DESC
            </otherwise>
        </choose>
        LIMIT 50

    </select>


    <sql id="userAssociateCommonConditions">
        <where>
            is_delete = 0
            <if test="userIds != null and userIds.size() > 0">
                AND id IN
                <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="userCodes != null and userCodes.size() > 0">
                AND user_code IN
                <foreach collection="userCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="isDriver != null">
                AND is_driver = #{isDriver}
            </if>
            <if test="workStatus == null">
                AND work_status IS NOT NULL AND work_status != ''
            </if>
            <if test="workStatus != null and workStatus != ''">
                AND work_status = #{workStatus}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="classNature != null and classNature != ''">
                AND class_nature = #{classNature}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                id = #{keyword}
                OR user_code LIKE CONCAT('%', #{keyword}, '%')
                OR user_name LIKE CONCAT('%', #{keyword}, '%')
                OR user_name_en LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>

            <if test="isSystem != null and !isSystem">
                <if test="authDeptList!=null and authDeptList.size()>0">
                    and (dept_id in
                    <foreach collection="authDeptList" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="authDeptList==null or authDeptList.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>
        </where>
    </sql>



    <select id="userAttendanceArchive"
            parameterType="com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO">
        SELECT ui.id,
               ui.user_code    AS userCode,
               ui.user_name    AS userName,
               ui.user_name_en AS userNameEn,
               ui.location_country AS locationCountry,
               ui.location_province AS locationProvince,
               ui.location_city AS locationCity,
               ui.employee_type AS employeeType,
               ui.is_driver AS isDriver,
               ui.is_global_relocation AS isGlobalRelocation,
               ui.dept_id AS deptId,
               ui.post_id AS postId,
               ui.status AS status,
               ui.work_status AS workStatus,
               ui.class_nature AS classNature,
               ui.last_upd_date AS lastUpdDate,
               ui.last_upd_user_name AS lastUpdUserName
        FROM user_info ui
        WHERE ui.is_delete = 0
          AND ui.user_code IS NOT NULL
        <if test="userIdList!=null and userIdList.size()>0">
            <foreach collection="userIdList" separator="," open="and ui.id in (" close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="workStatus != null and workStatus != ''">
            AND ui.work_status = #{workStatus}
        </if>
        <if test="classNature != null and classNature != ''">
            AND ui.class_nature = #{classNature}
        </if>
        <if test="isDriver != null">
            AND ui.is_driver = #{isDriver}
        </if>
        <if test="normalCountryList != null and normalCountryList.size()>0">
            <foreach collection="normalCountryList" separator="," open="and ui.location_country in (" close=")" item="normalCountry">
                #{normalCountry}
            </foreach>
        </if>
        <if test="normalEmployeeTypeList != null and normalEmployeeTypeList.size() > 0">
            <foreach collection="normalEmployeeTypeList" separator="," open="and ui.employee_type in (" close=")"
                     item="normalEmployeeType">
                #{normalEmployeeType}
            </foreach>
        </if>
        <include refid="permissionAndDeptConditions"/>


        <if test="isNeedQuerySpecialCountry!=null and isNeedQuerySpecialCountry == true">
            union

            SELECT ui.id,
            ui.user_code    AS userCode,
            ui.user_name    AS userName,
            ui.user_name_en AS userNameEn,
            ui.location_country AS locationCountry,
            ui.location_province AS locationProvince,
            ui.location_city AS locationCity,
            ui.employee_type AS employeeType,
            ui.is_driver AS isDriver,
            ui.is_global_relocation AS isGlobalRelocation,
            ui.dept_id AS deptId,
            ui.post_id AS postId,
            ui.status AS status,
            ui.work_status AS workStatus,
            ui.class_nature AS classNature,
            ui.last_upd_date AS lastUpdDate,
            ui.last_upd_user_name AS lastUpdUserName
            FROM user_info ui
            WHERE ui.is_delete = 0
            AND ui.user_code IS NOT NULL
            <if test="userIdList!=null and userIdList.size()>0">
                <foreach collection="userIdList" separator="," open="and ui.id in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="workStatus != null and workStatus != ''">
                AND ui.work_status = #{workStatus}
            </if>
            <if test="classNature != null and classNature != ''">
                AND ui.class_nature = #{classNature}
            </if>
            <if test="isDriver != null">
                AND ui.is_driver = #{isDriver}
            </if>
            <if test="specialCountryList != null and specialCountryList.size()>0">
                <foreach collection="specialCountryList" separator="," open="and ui.location_country in (" close=")" item="specialCountry">
                    #{specialCountry}
                </foreach>
            </if>
            <if test="specialEmployeeTypeList != null and specialEmployeeTypeList.size() > 0">
                <foreach collection="specialEmployeeTypeList" separator="," open="and ui.employee_type in (" close=")"
                         item="specialEmployeeType">
                    #{specialEmployeeType}
                </foreach>
            </if>
            <include refid="permissionAndDeptConditions"/>

        </if>
    </select>



    <!-- 权限和部门条件片段 -->
    <sql id="permissionAndDeptConditions">
        <!-- 同时具有部门和国家权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == true">
            <if test="deptIdList != null and deptIdList.size() > 0">
                and (ui.dept_id in
                <foreach collection="deptIdList" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    or ui.location_country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
                )
            </if>
            <if test="deptIdList == null or deptIdList.size() == 0">
                <if test="authLocationCountryList != null and authLocationCountryList.size() > 0">
                    and ui.location_country in
                    <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                        #{country}
                    </foreach>
                </if>
            </if>
        </if>

        <!-- 只有部门权限 -->
        <if test="hasDeptPermission == true and hasCountryPermission == false">
            and ui.dept_id in
            <foreach collection="deptIdList" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>

        <!-- 只有国家权限 -->
        <if test="hasDeptPermission == false and hasCountryPermission == true">
            and ui.location_country in
            <foreach collection="authLocationCountryList" item="country" separator="," open="(" close=")">
                #{country}
            </foreach>
        </if>

        <!-- 选择部门的特殊处理 -->
        <if test="isChooseDept == true">
            <if test="deptIdList != null and deptIdList.size() > 0">
                and ui.dept_id in
                <foreach collection="deptIdList" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
            </if>
        </if>
    </sql>

</mapper>