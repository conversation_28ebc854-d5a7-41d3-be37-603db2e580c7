<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.calendar.mapper.CalendarConfigRangeMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO">
        <!--@mbg.generated-->
        <!--@Table attendance.calendar_config_range-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_code" jdbcType="BIGINT" property="bizCode"/>
        <result column="attendance_config_id" jdbcType="BIGINT" property="attendanceConfigId"/>
        <result column="attendance_config_no" jdbcType="VARCHAR" property="attendanceConfigNo"/>
        <result column="range_type" jdbcType="VARCHAR" property="rangeType"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="is_latest" jdbcType="TINYINT" property="isLatest"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="orderby" jdbcType="DECIMAL" property="orderby"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, biz_id, biz_code, attendance_config_id, attendance_config_no, range_type, start_date, end_date,
        is_latest, remark, is_delete, record_version, create_date, create_user_code, create_user_name,
        last_upd_date, last_upd_user_code, last_upd_user_name, orderby
    </sql>


    <sql id="list_condition">
        <if test="bizId != null">
            AND ccr.biz_id = #{bizId}
        </if>
        <if test="bizCode != null">
            AND ccr.biz_code = #{bizCode}
        </if>
        <if test="bizIds != null and bizIds.size() != 0">
            <foreach collection="bizIds" item="item" open="AND ccr.biz_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="bizCodes != null and bizCodes.size() != 0">
            <foreach collection="bizCodes" item="item" open="AND ccr.biz_code in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="rangeType != null and rangeType != ''">
            AND ccr.range_type = #{rangeType}
        </if>
        <if test="calendarConfigNo != null">
            AND ccr.attendance_config_no = #{calendarConfigNo}
        </if>
        <if test="notEqCalendarConfigNo != null">
            AND ccr.attendance_config_no != #{notEqCalendarConfigNo}
        </if>
    </sql>


    <select id="listActiveRecords"
            resultType="com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO">
        select
        <include refid="Base_Column_List"/>
        from calendar_config_range ccr
        INNER JOIN (
        SELECT attendance_config_no AS l_attendance_config_no, MAX(end_date) AS max_end_date
        FROM calendar_config_range
        WHERE is_latest = 1
        GROUP BY attendance_config_no
        ) latest
        ON ccr.attendance_config_no = latest.l_attendance_config_no
        AND ccr.end_date = latest.max_end_date
        <where>
            <include refid="list_condition"/>
            AND ccr.is_latest = 1
            AND ccr.is_delete = 0
            AND ccr.start_date &lt;= NOW()
            AND ccr.attendance_config_id IN (
            SELECT ID
            from calendar_config cc
            WHERE cc.is_latest = 1 AND cc.is_delete = 0
            <if test="calendarConfigStatus != null and calendarConfigStatus != ''">
                AND cc.status = #{calendarConfigStatus}
            </if>
            )
        </where>
    </select>


    <select id="listAllRecords"
            resultType="com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO">
        select
        <include refid="Base_Column_List"/>
        from calendar_config_range ccr
        <where>
            <include refid="list_condition"/>
            AND ccr.is_delete = 0
            AND ccr.is_latest = 1
            and (ccr.start_date &lt;=#{endTime} and ccr.end_date >=#{startTime})
            AND ccr.attendance_config_id IN (
                SELECT ID
                from calendar_config cc
                WHERE cc.is_delete = 0
                <if test="calendarConfigStatus != null and calendarConfigStatus != ''">
                    AND cc.status = #{calendarConfigStatus}
                </if>
            )
        </where>
        order by ccr.start_date asc
    </select>

    <select id="selectConfigRangeByDate"
            resultType="com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO"
            parameterType="com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery">
        select
        <include refid="Base_Column_List"/>
        from calendar_config_range
        where is_delete = 0
        <if test="userIds!=null and userIds.size()>0">
            <foreach collection="userIds" item="userId" separator="," open="and biz_id in (" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="userCodes!=null and userCodes.size()>0">
            <foreach collection="userCodes" item="userCode" separator="," open="and biz_code in (" close=")">
                #{userCode}
            </foreach>
        </if>
        <if test="startDate != null and endDate != null">
            and ((start_date &lt;= #{startDate} and end_date >= #{startDate})
            or
            (start_date &lt;= #{endDate} and end_date >= #{endDate}))
        </if>
    </select>


    <select id="countCalendarRange"
            resultType="com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO">
        select
        ccr.attendance_config_id as calendarConfigId ,
        count(*) as `count`
        from calendar_config_range ccr
        left join hrms_user_info ui on ccr.biz_id = ui.id
        where ccr.is_latest = 1
        <if test="calendarConfigIds!=null and calendarConfigIds.size()>0">
            <foreach collection="calendarConfigIds" item="calendarConfigId" separator=","
                     open="and ccr.attendance_config_id in (" close=")">
                #{calendarConfigId}
            </foreach>
        </if>
        and ui.status = 'ACTIVE' and ui.work_status = 'ON_JOB'
        group by ccr.attendance_config_id
    </select>

    <select id="listOnJobNoDriverUsersExcludeRangeConfigured"
            parameterType="com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO">
        SELECT u.*
        FROM user_info u
        LEFT JOIN (SELECT DISTINCT biz_id
                FROM calendar_config_range
                WHERE is_latest = 1
                AND is_delete = 0) r ON u.id = r.biz_id
        WHERE u.is_driver = 0
            AND u.work_status = 'ON_JOB'
            AND u.`status`= 'ACTIVE'
            AND u.is_delete = 0
        <if test="country != null and country != ''">
            AND u.location_country = #{country}
        </if>
        <if test="countries != null and countries.size() > 0">
            AND u.location_country IN
        </if>
        <if test="userIds != null and userIds.size() > 0">
            AND u.id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.size() > 0">
            AND u.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="codeOrNameLike != null and codeOrNameLike != ''">
            AND (u.user_code LIKE CONCAT('%', #{codeOrNameLike}, '%')
            OR u.user_name LIKE CONCAT('%', #{codeOrNameLike}, '%'))
        </if>
        AND r.biz_id IS NULL
    </select>
</mapper>