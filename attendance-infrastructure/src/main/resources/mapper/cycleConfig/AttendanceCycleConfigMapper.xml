<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.cycleConfig.mapper.AttendanceCycleConfigMapper">
    <resultMap id="BaseResultMap" type="com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="record_version" jdbcType="BIGINT" property="recordVersion" />
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate" />
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode" />
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName" />
        <result column="country" jdbcType="VARCHAR" property="country" />
        <result column="cycle_type" jdbcType="TINYINT" property="cycleType" />
        <result column="cycle_start" jdbcType="VARCHAR" property="cycleStart" />
        <result column="cycle_end" jdbcType="VARCHAR" property="cycleEnd" />
        <result column="abnormal_expired" jdbcType="INTEGER" property="abnormalExpired" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>
    <sql id="Base_Column_List">
        id, is_delete, record_version, create_date, create_user_code,
    create_user_name, last_upd_date, last_upd_user_code, last_upd_user_name,
    country, cycle_type, cycle_start, cycle_end, abnormal_expired, status
    </sql>

</mapper>
