<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.punch.mapper.EmployeePunchRecordInspectionMapper">
    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordInspectionDO">
        <!--@mbg.generated-->
        <!--@Table hrms.employee_punch_record_inspection-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="is_delete" jdbcType="TINYINT" property="isDelete"/>
        <result column="record_version" jdbcType="BIGINT" property="recordVersion"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user_code" jdbcType="VARCHAR" property="createUserCode"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="last_upd_date" jdbcType="TIMESTAMP" property="lastUpdDate"/>
        <result column="last_upd_user_code" jdbcType="VARCHAR" property="lastUpdUserCode"/>
        <result column="last_upd_user_name" jdbcType="VARCHAR" property="lastUpdUserName"/>
        <result column="punch_record_id" jdbcType="BIGINT" property="punchRecordId"/>
        <result column="user_code" jdbcType="VARCHAR" property="userCode"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="day_id" jdbcType="VARCHAR" property="dayId"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="punch_time" jdbcType="TIMESTAMP" property="punchTime"/>
        <result column="punch_record_create_date_local" jdbcType="TIMESTAMP" property="punchRecordCreateDateLocal"/>
        <result column="punch_record_create_date" jdbcType="TIMESTAMP" property="punchRecordCreateDate"/>
        <result column="punch_area" jdbcType="VARCHAR" property="punchArea"/>
        <result column="punch_card_type" jdbcType="VARCHAR" property="punchCardType"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, is_delete, record_version, create_date, create_user_code, create_user_name, last_upd_date,
        last_upd_user_code, last_upd_user_name, punch_record_id, user_code, country, day_id,
        source_type, punch_time, punch_record_create_date_local, punch_record_create_date,
        punch_area, punch_card_type
    </sql>
</mapper>