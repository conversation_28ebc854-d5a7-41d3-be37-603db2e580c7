<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.punch.mapper.EmployeePunchRecordMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO">
        <id column="id" property="id"/>
        <result column="source_type" property="sourceType"/>
        <result column="user_code" property="userCode"/>
        <result column="punch_time" property="punchTime"/>
        <result column="punch_area" property="punchArea"/>
        <result column="punch_card_type" property="punchCardType"/>
        <result column="relation_id" property="relationId"/>
        <result column="day_id" property="dayId"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="extend" property="extend"/>
        <result column="orderby" property="orderby"/>
        <result column="country" property="country"/>
        <result column="oc_code" property="ocCode"/>
        <result column="oc_name" property="ocName"/>
        <result column="dept_id" property="deptId"/>
        <result column="employee_type" property="employeeType"/>
        <result column="punch_type" property="punchType"/>
        <result column="oc_longitude" property="ocLongitude"/>
        <result column="oc_latitude" property="ocLatitude"/>
        <result column="distance" property="distance"/>
        <result column="form_id" property="formId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_date
        ,
        create_user_code,
        create_user_name,
        last_upd_date,
        last_upd_user_code,
        last_upd_user_name,
        is_delete,record_version,
        id, source_type, user_code, punch_time, punch_area, punch_card_type, relation_id, day_id, longitude, latitude, extend, orderby,
        country, oc_code, oc_name, dept_id, user_code,employee_type,punch_type,oc_longitude,oc_latitude,distance, form_id
    </sql>


    <select id="listRecord" resultType="com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO"
            parameterType="com.imile.attendance.infrastructure.repository.punch.query.UserPunchCardRecordQuery">
        select pr.id, pr.day_id, pr.source_type, pr.punch_time, pr.punch_area, pr.punch_card_type,
        pr.user_code, pr.dept_id, hui.user_name, hui.email, pr.last_upd_date, pr.last_upd_user_name, hui.id as userId
        from employee_punch_record pr
        left join hrms_user_info hui on hui.user_code = pr.user_code
        <if test="attendanceConfigNo!=null and attendanceConfigNo!=''">
            inner join calendar_config_range as acr on acr.biz_id = hui.id and acr.is_delete=0 and acr.is_latest=1
            inner join calendar_config as ac on ac.id = acr.attendance_config_id and ac.is_delete=0 and ac.is_latest=1
            and ac.attendance_config_no = #{attendanceConfigNo}
        </if>
        <if test="punchConfigNo!=null and punchConfigNo!=''">
            inner join punch_config_range as cr on cr.biz_id = hui.id and cr.is_delete=0 and cr.is_latest=1
            inner join punch_config as pc on pc.id = cr.punch_config_id and pc.is_delete=0 and pc.is_latest=1 and
            pc.punch_config_no = #{punchConfigNo}
        </if>
        <where>
            pr.is_delete = 0
            and pr.punch_time BETWEEN #{startDate} AND #{endDate}
            <if test="userNameOrEmail!=null and userNameOrEmail!=''">
                and (hui.user_name like concat('%',#{userNameOrEmail},'%') or hui.email like
                concat('%',#{userNameOrEmail},'%'))
            </if>
            <if test="userCodeOrName!=null and userCodeOrName!=''">
                and (hui.user_code like concat('%',#{userCodeOrName},'%') or hui.user_name like
                concat('%',#{userCodeOrName},'%') or hui.user_name_en like concat('%',#{userCodeOrName},'%')
                or hui.sys_account_name like concat('%',#{userCodeOrName},'%'))
            </if>
            <if test="userCode!=null and userCode!=''">
                and pr.user_code = #{userCode}
            </if>
            <if test="userCodes!=null and userCodes.size()>0">
                <foreach collection="userCodes" item="userCode" open="and pr.user_code in (" close=")"
                         separator=",">
                    #{userCode}
                </foreach>
            </if>
            <if test="country!=null and country!=''">
                and pr.country = #{country}
            </if>
            <if test="sourceType!=null and sourceType!=''">
                and pr.source_type = #{sourceType}
            </if>
            <if test="deptId!=null and deptId!=''">
                and hui.dept_id = #{deptId}
            </if>
            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == true">
                <if test="deptIdList!=null and deptIdList.size()>0">
                    and (hui.dept_id in
                    <foreach collection="deptIdList" item="deptId" index="i" open="(" close=")" separator=",">
                        #{deptId}
                    </foreach>
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        or hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="("
                                 close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                    )
                </if>

                <if test="deptIdList==null or deptIdList.size()==0">
                    <if test="authLocationCountryList!=null and authLocationCountryList.size()>0">
                        and hui.location_country in
                        <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="("
                                 close=")"
                                 separator=",">
                            #{locationCountry}
                        </foreach>
                    </if>
                </if>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == true and hasCountryPermission!=null and hasCountryPermission == false">
                and hui.dept_id in
                <foreach collection="deptIdList" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>

            <if test="hasDeptPermission!=null and hasDeptPermission == false and hasCountryPermission!=null and hasCountryPermission == true">
                and hui.location_country in
                <foreach collection="authLocationCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            <if test="isChooseDept!=null and isChooseDept == true">
                <if test="deptIdList!=null and deptIdList.size()>0">
                    <foreach collection="deptIdList" item="deptId" open="and hui.dept_id in (" close=")"
                             separator=",">
                        #{deptId}
                    </foreach>
                </if>
            </if>
            <if test="punchCardType!=null and punchCardType!=''">
                and pr.punch_card_type = #{punchCardType}
            </if>
            <if test="punchCardTypeList!=null and punchCardTypeList.size()>0">
                <foreach collection="punchCardTypeList" item="punchCardType" open="and pr.punch_card_type in ("
                         close=")"
                         separator=",">
                    #{punchCardType}
                </foreach>
            </if>
            <!--        <include refid="com.imile.hrms.dao.common.HrmsCommonMapper.resourceSql"></include>-->
        </where>
        <if test="daySort == null and punchDateSort == null">
            order by pr.day_id desc, pr.punch_time desc
        </if>
        <if test="daySort != null and daySort == 0 and punchDateSort == null">
            order by pr.day_id asc
        </if>
        <if test="daySort != null and daySort == 1 and punchDateSort == null">
            order by pr.day_id desc
        </if>
        <if test="daySort == null and punchDateSort != null and punchDateSort == 0">
            order by pr.punch_time asc
        </if>
        <if test="daySort == null and punchDateSort != null and punchDateSort == 1">
            order by pr.punch_time desc
        </if>
        <if test="daySort != null and punchDateSort != null and daySort == 0 and punchDateSort == 0">
            order by pr.day_id asc, pr.punch_time asc
        </if>
        <if test="daySort != null and punchDateSort != null and daySort == 1 and punchDateSort == 0">
            order by pr.day_id desc, pr.punch_time asc
        </if>
        <if test="daySort != null and punchDateSort != null and daySort == 0 and punchDateSort == 1">
            order by pr.day_id asc, pr.punch_time desc
        </if>
        <if test="daySort != null and punchDateSort != null and daySort == 1 and punchDateSort == 1">
            order by pr.day_id desc, pr.punch_time desc
        </if>
    </select>



    <select id="selectUserIdByPunchCardTypes" resultType="java.lang.Long">
        select distinct hui.id
        from employee_punch_record pr
        left join hrms_user_info hui on hui.user_code = pr.user_code and hui.is_delete = 0
        <where>
            pr.is_delete = 0
            <if test="startDayId != null">
                and pr.day_id >= #{startDayId}
            </if>
            <if test="endDayId != null">
                and pr.day_id &lt;= #{endDayId}
            </if>
            <if test="punchCardTypeList != null and punchCardTypeList.size() > 0">
                and pr.punch_card_type in
                <foreach collection="punchCardTypeList" item="type" open="(" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectPunchCardTypeByUserCode" resultType="java.lang.String">
        select distinct pr.punch_card_type
        from employee_punch_record pr
        <where>
            pr.is_delete = 0
            <if test="userCode!=null and userCode!=''">
                and pr.user_code = #{userCode}
            </if>
            <if test="startDayId != null">
                and pr.day_id >= #{startDayId}
            </if>
            <if test="endDayId != null">
                and pr.day_id &lt;= #{endDayId}
            </if>

        </where>
    </select>
</mapper>
