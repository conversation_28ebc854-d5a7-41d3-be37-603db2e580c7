package com.imile.attendance.controller;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> chen
 * @Date 2025/5/7 
 * @Description
 */
@RestController
public class HealthCheckController {

    @GetMapping("/healthCheck")
    @NoLoginRequired
    public Result healthCheck() {
        return Result.ok(ErrorCodeEnum.SUCCESS.getCode());
    }
}
