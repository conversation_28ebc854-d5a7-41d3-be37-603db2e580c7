package com.imile.attendance.controller.driver;

import com.imile.attendance.driver.DriverAttendanceOperateRecordService;
import com.imile.attendance.driver.dto.DriverAttendanceOperateRecordParam;
import com.imile.attendance.driver.vo.DriverAttendanceOperateRecordVO;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description 司机考勤操作记录表(driver_attendance_operate_record)表控制层
 */
@RestController
@RequestMapping("/driver/attendance/operate/record")
public class DriverAttendanceOperateRecordController {

    @Resource
    private ConverterService converterService;
    @Resource
    private DriverAttendanceOperateRecordService driverAttendanceOperateRecordService;

    /**
     * 司机考勤操作记录列表
     * @param param 查询参数
     * @return 司机考勤操作记录列表
     */
    @PostMapping("/driverOperateRecord")
    public Result<PaginationResult<DriverAttendanceOperateRecordVO>> queryAttendanceOperateRecordList(@RequestBody @Valid DriverAttendanceOperateRecordParam param) {
        PaginationResult<DriverAttendanceOperateRecordVO> driverAttendanceOperateRecordVo =
                driverAttendanceOperateRecordService.pageAttendanceOperateRecordDetail(param);
        // 处理注解
        converterService.withAnnotation(driverAttendanceOperateRecordVo.getResults());
        return Result.ok(driverAttendanceOperateRecordVo);
    }
}
