package com.imile.attendance.controller.clover;

import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.clover.CloverAttendanceService;
import com.imile.attendance.clover.dto.UserAttendanceCalendarDTO;
import com.imile.attendance.clover.dto.UserAttendanceCycleDetailDTO;
import com.imile.attendance.clover.dto.UserAttendanceDayDetailDTO;
import com.imile.attendance.clover.dto.UserAttendanceGenerateCheckDTO;
import com.imile.attendance.clover.query.UserAttendanceCalendarQuery;
import com.imile.attendance.clover.query.UserAttendanceCycleDetailQuery;
import com.imile.attendance.clover.query.UserAttendanceDayDetailQuery;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/5/26 
 * @Description clover系统考勤信息
 */
@Slf4j
@RestController
@RequestMapping("/clover/attendance")
public class CloverAttendanceController {

    @Resource
    private CloverAttendanceService cloverAttendanceService;

    /**
     * 获取用户考勤周期内信息
     */
    @PostMapping("/user/attendance/cycle/detail")
    @NoLoginAuthRequired
    public Result<UserAttendanceCycleDetailDTO> getUserAttendanceCycleDetail(@RequestBody @Validated UserAttendanceCycleDetailQuery query) {
        return Result.ok(cloverAttendanceService.getUserAttendanceCycleDetail(query));
    }

    /**
     * 用户考勤日历展示
     */
    @PostMapping("/user/attendance/calendar")
    @NoLoginAuthRequired
    public Result<UserAttendanceCalendarDTO> getUserAttendanceCalendar(@RequestBody @Validated UserAttendanceCalendarQuery query) {
        return Result.ok(cloverAttendanceService.getUserAttendanceCalendar(query));
    }

    /**
     * 用户每日日历详情
     */
    @PostMapping("/user/attendance/day/detail")
    @NoLoginAuthRequired
    public Result<UserAttendanceDayDetailDTO> getUserAttendanceDayDetail(@RequestBody @Validated UserAttendanceDayDetailQuery query) {
        return Result.ok(cloverAttendanceService.getUserAttendanceDayDetail(query));
    }


    /**
     * 获取用户考勤未计算的日期
     */
    @PostMapping("/attendance/generate/check")
    @NoLoginAuthRequired
    public Result<UserAttendanceGenerateCheckDTO> getUserAttendanceGenerateCheck() {
        return Result.ok(cloverAttendanceService.getUserAttendanceGenerateCheck());
    }
}
