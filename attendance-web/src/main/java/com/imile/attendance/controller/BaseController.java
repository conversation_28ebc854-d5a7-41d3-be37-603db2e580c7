package com.imile.attendance.controller;

import com.alibaba.fastjson.JSONArray;
import com.imile.attendance.util.CollectionUtils;
import com.imile.attendance.util.ReflectionUtil;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.query.BaseQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@RestController
@Slf4j
public class BaseController {

    /**
     * 分页对象转换
     *
     * @param originDatas
     * @param clazz
     * @param <R>
     * @param <T>
     * @return
     */
    public <R, T> PaginationResult<T> convertPage(PaginationResult<R> originDatas, Class<T> clazz) {
        if (originDatas != null) {
            PaginationResult<T> paginationResult = new PaginationResult<>();
            paginationResult.setPagination(originDatas.getPagination());
            paginationResult.setResults(CollectionUtils.convert(originDatas.getResults(), clazz));
            return paginationResult;
        }
        return null;
    }

    public ExcelCallBackParam setExcelCallBackParam(HttpServletRequest request, BaseQuery query) {
        //excel回调返回数据
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);

        //业务分页入参适配
        query.setCurrentPage(callBackParam.getPageNum());
        query.setShowCount(callBackParam.getPageSize());
        query.setTotalResult(callBackParam.getTotalCount());

        //是否查询count
        Boolean isCount = (query.getCurrentPage() == 1);
        query.setCount(isCount);
        query.setPageEnabled(true);
        dealListField(query);
        log.info("setExcelCallBackParam | callBackParam={} | query={}", callBackParam, query);
        return callBackParam;
    }

    private void dealListField(BaseQuery query) {
        try {
            Class<? extends BaseQuery> aClass = query.getClass();
            List<Field> allDeclaredField = ReflectionUtil.getAllDeclaredField(aClass);
            allDeclaredField.forEach(field -> {
                Object o = ReflectionUtil.getFieldValue(query, field.getName());
                if (Objects.isNull(o)) {
                    return;
                }
                if (o instanceof Collection) {
                    List<String> oldList = JSONArray.parseArray(o.toString(), String.class);
                    String str = String.join("", oldList);
                    List<String> newList = JSONArray.parseArray(str, String.class);
                    ReflectionUtil.setFieldValue(query, field, newList);
                }
            });
        } catch (Exception e) {
            log.info("dealListField error", e);
        }
    }
}
