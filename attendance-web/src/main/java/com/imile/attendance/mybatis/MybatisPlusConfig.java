package com.imile.attendance.mybatis;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/11/16
 */
@Configuration
public class MybatisPlusConfig {
    @Bean
    public IdentifierGenerator idGenerator() {
        return new AttendanceIdGenerator();
    }

    /**
     * 解决MybatisPlus无法使用批量新增问题，但仅支持MYSQL（MybatisPlus中虽然使用savaBatch可以实现批量插入，但是使用for循环，效率比较低）
     *
     * @return
     */
    @Bean
    public EasySqlInjector easySqlInjector() {
        return new EasySqlInjector();
    }

}
