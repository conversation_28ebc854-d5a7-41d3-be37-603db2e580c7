package com.imile.attendance.infrastructure;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.attendance.base.BaseTest;
import com.imile.attendance.repository.employee.dao.UserInfoDao;
import com.imile.attendance.repository.employee.modle.UserInfoDO;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
public class HrmsUserInfoTest extends BaseTest {

    @Resource
    private UserInfoDao userInfoDao;

    @Test
    public void test() {
        LambdaQueryWrapper<UserInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInfoDO::getUserCode,"21032401");
        List<UserInfoDO> list = userInfoDao.list(queryWrapper);
        System.out.println(list);
    }
}
