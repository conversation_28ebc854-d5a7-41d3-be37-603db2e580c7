package com.imile.attendance.logRecord;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.logRecord.enums.OperationModuleEnum;
import com.imile.attendance.infrastructure.repository.log.dto.LogRecordPageDTO;
import com.imile.attendance.infrastructure.repository.log.query.LogRecordPageQuery;
import com.imile.common.page.PaginationResult;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31 
 * @Description
 */
public class LogOperationRecordServiceTest extends BaseTest {

    @Resource
    private LogOperationRecordService logOperationRecordService;

    @Test
    public void testPage(){
        LogRecordPageQuery logRecordPageQuery = new LogRecordPageQuery();
        logRecordPageQuery.setOperationModule(OperationModuleEnum.CALENDAR_MODULE.getCode());
        logRecordPageQuery.setShowCount(1);
        PaginationResult<LogRecordPageDTO> page = logOperationRecordService.page(logRecordPageQuery);
        List<LogRecordPageDTO> results = page.getResults();
        System.out.println(results);
    }
}
