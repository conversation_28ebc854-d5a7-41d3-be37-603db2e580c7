package com.imile.attendance.calendar;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.calendar.job.DateInitHandler;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
public class DateInitHandlerConvertTest {

    @InjectMocks
    private DateInitHandler dateInitHandler;

    @Mock
    private BaseDayInfoService baseDayInfoService;

    @Captor
    private ArgumentCaptor<List<BaseDayInfoDO>> listCaptor;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testConvertMethod() throws Exception {
        // 获取私有方法
        Method convertMethod = DateInitHandler.class.getDeclaredMethod("convert", DateTime.class);
        convertMethod.setAccessible(true);

        // 创建实例
        DateInitHandler handler = new DateInitHandler();

        // 测试特定日期：2021-12-09（星期四）
        DateTime testDate = DateUtil.parseDate("2021-12-09");
        BaseDayInfoDO result = (BaseDayInfoDO) convertMethod.invoke(handler, testDate);
        System.out.println(result);

        // 验证转换结果
        assertEquals(2021, Integer.parseInt(String.valueOf(result.getYear())));
        assertSame(4, Integer.parseInt(String.valueOf(result.getQuarter())));
        assertSame(12, Integer.parseInt(String.valueOf(result.getMonth())));
        assertSame(9, Integer.parseInt(String.valueOf(result.getDay())));
        assertEquals("THURSDAY", result.getDayOfWeek());
        assertEquals(20211209L, Long.parseLong(String.valueOf(result.getId())));

        // 测试周计算（需要注意代码中特别设置了周日为一周的第一天）
        int expectedWeek = DateUtil.weekOfYear(testDate.setFirstDayOfWeek(Week.SUNDAY));
        assertEquals(expectedWeek, Integer.parseInt(String.valueOf(result.getWeek())));
    }

    @Test
    public void testConvertForEdgeCases() throws Exception {
        Method convertMethod = DateInitHandler.class.getDeclaredMethod("convert", DateTime.class);
        convertMethod.setAccessible(true);
        DateInitHandler handler = new DateInitHandler();

        // 测试边界日期：跨年日期 2021-12-31
        DateTime yearEndDate = DateUtil.parseDate("2021-12-31");
        BaseDayInfoDO yearEndResult = (BaseDayInfoDO) convertMethod.invoke(handler, yearEndDate);
        assertEquals(2021, Integer.parseInt(String.valueOf(yearEndResult.getYear())));
        assertEquals(12, Integer.parseInt(String.valueOf(yearEndResult.getMonth())));
        assertEquals(31, Integer.parseInt(String.valueOf(yearEndResult.getDay())));

        // 测试边界日期：跨季度日期 2021-09-30
        DateTime quarterEndDate = DateUtil.parseDate("2021-09-30");
        BaseDayInfoDO quarterEndResult = (BaseDayInfoDO) convertMethod.invoke(handler, quarterEndDate);
        assertEquals(2021, Integer.parseInt(String.valueOf(quarterEndResult.getYear())));
        assertEquals(3, Integer.parseInt(String.valueOf(quarterEndResult.getQuarter())));
        assertEquals(9, Integer.parseInt(String.valueOf(quarterEndResult.getMonth())));

        // 闰年日期测试 2020-02-29
        DateTime leapYearDate = DateUtil.parseDate("2020-02-29");
        BaseDayInfoDO leapYearResult = (BaseDayInfoDO) convertMethod.invoke(handler, leapYearDate);
        assertEquals(2020, Integer.parseInt(String.valueOf(leapYearResult.getYear())));
        assertEquals(2, Integer.parseInt(String.valueOf(leapYearResult.getMonth())));
        assertEquals(29, Integer.parseInt(String.valueOf(leapYearResult.getDay())));
    }

    @Test
    public void testDateInitWithCustomParams() {
        // 准备自定义参数：2020年开始，偏移2年
        DateInitHandler.DateInitParam param = new DateInitHandler.DateInitParam();
        param.setStartYear(2020);
        param.setOffset(2);
        String paramJson = JSONObject.toJSONString(param);

        // 调用初始化方法
        dateInitHandler.dateInit(paramJson);

        // 验证保存方法被调用2次（偏移2年）
        verify(baseDayInfoService, times(2)).saveOrUpdateBatch(listCaptor.capture());

        // 验证第一年的数据（2020年）
        List<List<BaseDayInfoDO>> allCapturedLists = listCaptor.getAllValues();
        List<BaseDayInfoDO> firstYearData = allCapturedLists.get(0);

        // 检查第一年的开始日期是2020-01-01
        assertEquals(2020, Integer.parseInt(String.valueOf(firstYearData.get(0).getYear())));
        assertEquals(1, Integer.parseInt(String.valueOf(firstYearData.get(0).getMonth())));
        assertEquals(1, Integer.parseInt(String.valueOf(firstYearData.get(0).getDay())));

        // 检查第一年数据量（2020是闰年，应该有366天）
        assertEquals(366, firstYearData.size());
    }

    @Test
    public void testDateInitWithInvalidYear() {
        // 测试小于2018的年份（应该使用当前年份）
        DateInitHandler.DateInitParam param = new DateInitHandler.DateInitParam();
        param.setStartYear(2016); // 小于2018，应该使用当前年
        param.setOffset(1);
        String paramJson = JSONObject.toJSONString(param);

        // 调用初始化方法
        dateInitHandler.dateInit(paramJson);

        // 验证数据
        verify(baseDayInfoService).saveOrUpdateBatch(listCaptor.capture());
        List<BaseDayInfoDO> yearData = listCaptor.getValue();

        // 应该使用当前年份而非2016
        int currentYear = DateUtil.year(new Date());
        System.out.println("currentYear:" + currentYear);
        assertEquals(currentYear, Integer.parseInt(String.valueOf(yearData.get(0).getYear())));
    }

    @Test
    public void testNullParam() {
        // 测试空参数
        dateInitHandler.dateInit(null);

        // 验证数据
        verify(baseDayInfoService, times(3)).saveOrUpdateBatch(listCaptor.capture());
        List<List<BaseDayInfoDO>> allValues = listCaptor.getAllValues();

        // 应该使用当前年份
        List<BaseDayInfoDO> baseDayInfoDOS = allValues.get(0);
        int currentYear = DateUtil.year(new Date());
        assertEquals(currentYear, Integer.parseInt(String.valueOf(baseDayInfoDOS.get(0).getYear())));
    }
}
