package com.imile.attendance.base;

import org.mockito.MockitoAnnotations;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.support.AbstractTestExecutionListener;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15 
 * @Description
 */
public class MockitoTestExecutionListener extends AbstractTestExecutionListener {

    @Override
    public void prepareTestInstance(TestContext testContext) {
        Object testInstance = testContext.getTestInstance();
        MockitoAnnotations.initMocks(testInstance); // 使用 initMocks 方法
    }
}
