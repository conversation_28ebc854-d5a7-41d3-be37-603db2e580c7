package com.imile.attendance.rpc;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.ipep.dto.OssApiVo;
import com.imile.attendance.ipep.support.RpcIpepClientSupport;
import com.imile.ipep.dto.OssApiDto;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/4/2 
 * @Description
 */
public class RpcIpepClientTest extends BaseTest {

    @Resource
    private RpcIpepClientSupport rpcIpepClientSupport;

    @Test
    public void testgetUrlByFileKey(){
        //vendor/vendor/print/2024/8/20240807/6071124379760.pdf
        OssApiVo ossApiDto = rpcIpepClientSupport.getUrlByFileKey(
                "vendor/vendor/print/2024/10/20241012/202410121268934197062561793.pdf", 1);
        System.out.println(ossApiDto.getFileKey());
        System.out.println(ossApiDto.getFileUrl());
        System.out.println(ossApiDto.getBucketType());
        System.out.println(ossApiDto.getOriginalFilename());
        System.out.println(ossApiDto.getFileType());

    }
}
